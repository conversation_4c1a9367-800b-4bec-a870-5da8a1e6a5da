import mongoose, { Document, Schema, Model } from 'mongoose';

export interface ITransferLog extends Document {
  type: 'account' | 'profile';
  action: 'claimed' | 'transferred' | 'ownership_change';
  fromUserId: mongoose.Types.ObjectId;
  toUserId: mongoose.Types.ObjectId;
  fromUserName: string;
  toUserName: string;
  icon: string;
  actionText: string;
  myPts: number;
  details: {
    profileId?: mongoose.Types.ObjectId;
    accountId?: mongoose.Types.ObjectId;
    reason?: string;
    profileType?: string;
    accountType?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  toAPIResponse(): any;
}

interface ITransferLogModel extends Model<ITransferLog> {
  getTransferLogs(options?: {
    type?: 'account' | 'profile';
    page?: number;
    limit?: number;
    userId?: string;
    fromUserId?: string;
    toUserId?: string;
  }): Promise<{
    logs: any[];
    total: number;
    page: number;
    limit: number;
  }>;
  getTransferStats(userId?: string): Promise<any[]>;
}

const transferLogSchema = new Schema<ITransferLog>(
  {
    type: {
      type: String,
      enum: ['account', 'profile'],
      required: true,
      index: true
    },
    action: {
      type: String,
      enum: ['claimed', 'transferred', 'ownership_change'],
      required: true,
      index: true
    },
    fromUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    toUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    fromUserName: {
      type: String,
      required: true
    },
    toUserName: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    actionText: {
      type: String,
      required: true
    },
    myPts: {
      type: Number,
      required: true,
      default: 0
    },
    details: {
      profileId: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
      },
      accountId: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      reason: String,
      profileType: String,
      accountType: String
    }
  },
  {
    timestamps: true,
    collection: 'transfer_logs'
  }
);

// Indexes for better query performance
transferLogSchema.index({ type: 1, action: 1 });
transferLogSchema.index({ fromUserId: 1, createdAt: -1 });
transferLogSchema.index({ toUserId: 1, createdAt: -1 });
transferLogSchema.index({ createdAt: -1 });
transferLogSchema.index({ 'details.profileId': 1 });
transferLogSchema.index({ 'details.accountId': 1 });

// Static method to get transfer logs with pagination and filtering
transferLogSchema.statics.getTransferLogs = async function(
  options: {
    type?: 'account' | 'profile';
    page?: number;
    limit?: number;
    userId?: string;
    fromUserId?: string;
    toUserId?: string;
  } = {}
) {
  const { type, page = 1, limit = 20, userId, fromUserId, toUserId } = options;
  const skip = (page - 1) * limit;

  // Build query
  const query: any = {};
  
  if (type) {
    query.type = type;
  }
  
  if (userId) {
    query.$or = [
      { fromUserId: new mongoose.Types.ObjectId(userId) },
      { toUserId: new mongoose.Types.ObjectId(userId) }
    ];
  }
  
  if (fromUserId) {
    query.fromUserId = new mongoose.Types.ObjectId(fromUserId);
  }
  
  if (toUserId) {
    query.toUserId = new mongoose.Types.ObjectId(toUserId);
  }

  // Execute query with pagination
  const [logs, total] = await Promise.all([
    this.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    this.countDocuments(query)
  ]);

  return {
    logs,
    total,
    page,
    limit
  };
};

// Static method to get transfer statistics
transferLogSchema.statics.getTransferStats = async function(userId?: string) {
  const matchStage: any = {};
  
  if (userId) {
    matchStage.$or = [
      { fromUserId: new mongoose.Types.ObjectId(userId) },
      { toUserId: new mongoose.Types.ObjectId(userId) }
    ];
  }

  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: {
          type: '$type',
          action: '$action'
        },
        count: { $sum: 1 },
        totalMyPts: { $sum: '$myPts' }
      }
    },
    {
      $group: {
        _id: '$_id.type',
        actions: {
          $push: {
            action: '$_id.action',
            count: '$count',
            totalMyPts: '$totalMyPts'
          }
        },
        totalCount: { $sum: '$count' },
        totalMyPts: { $sum: '$totalMyPts' }
      }
    }
  ]);

  return stats;
};

// Instance method to format for API response
transferLogSchema.methods.toAPIResponse = function() {
  return {
    type: this.type,
    user: this.toUserName,
    action: this.actionText,
    icon: this.icon,
    date: this.createdAt.toISOString(),
    myPts: this.myPts,
    details: {
      fromUserId: this.fromUserId.toString(),
      toUserId: this.toUserId.toString(),
      profileId: this.details.profileId?.toString(),
      accountId: this.details.accountId?.toString(),
      reason: this.details.reason,
      profileType: this.details.profileType,
      accountType: this.details.accountType
    }
  };
};

export const TransferLogModel = mongoose.model<ITransferLog, ITransferLogModel>('TransferLog', transferLogSchema);
export type TransferLogDocument = ITransferLog;