import mongoose, { Schema, Document, Model } from 'mongoose';
import { MyPtsModel } from './my-pts.model';
import { MyPtsValueModel } from './my-pts-value.model';
import { IMyPts } from '../interfaces/my-pts.interface';
import { IProfileMethods } from '../interfaces/profile.interface';

export type ProfileDocument = IProfile & Document & {
  _id: mongoose.Types.ObjectId;
};

export type ProfileCategory = 'individual' | 'accessory' | 'group';
export type ProfileType =
  | 'personal' | 'academic' | 'work' | 'professional' | 'proprietor' | 'freelancer' | 'artist' | 'influencer' | 'athlete' | 'provider' | 'merchant' | 'vendor'
  | 'emergency' | 'medical' | 'pet' | 'ecommerce' | 'home' | 'transportation' | 'driver' | 'event' | 'dependent' | 'rider' | 'dummy'
  | 'group' | 'team' | 'family' | 'neighborhood' | 'company' | 'business' | 'association' | 'organization' | 'institution' | 'community';

interface IProfile {
  profileCategory: ProfileCategory;
  profileType: ProfileType;
  secondaryId?: string; // Secondary ID for easy user reference
  ProfileFormat: {
    profileImage?: string;
    coverImage?: string;
    profileLogo?: string;
    customization?: {
      theme?: {
        primaryColor?: string;
        secondaryColor?: string;
        accent?: string;
        background?: string;
        text?: string;
        font?: string;
      };
      layout?: {
        sections?: Array<{ id: string; type: string; order: number; visible: boolean }>;
        gridStyle?: 'right-sided' | 'centered' | 'left-sided';
        animation?: 'fade' | 'slide' | 'zoom';
      };
    };
    customCSS?: string;
    updatedAt: Date;
  };

  profileInformation: {
    username: string;
    profileLink: string;
    shareToken?: string; // Share token for the profile
    title?: string;
    name?: string; // For personal profiles - creator's full name
    accountHolder?: string;
    pid?: string;
    relationshipToAccountHolder?: string;
    creator: mongoose.Types.ObjectId;
    connectLink: string;
    followLink: string;
    followers: mongoose.Types.ObjectId[];
    following: mongoose.Types.ObjectId[];
    connectedProfiles: mongoose.Types.ObjectId[];
    affiliatedProfiles: mongoose.Types.ObjectId[];
    accessToken?: string; // Added for profile token authentication
    createdAt: Date;
    updatedAt: Date;
  };

  ProfileQrCode: {
    qrCode?: string;
    staticQrCode?: string;
    emailSignature?: string;
    wallPaper?: string;
    VirtualBackground?: string;
  };

  profileLocation?: {
    city?: string;
    stateOrProvince?: string;
    country?: string;
    countryCode?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };

  ProfileProducts?: {
    type: 'Accessory' | 'Device' | 'None';
    name?: string;
    description?: string;
  };

  verificationStatus?: {
    isVerified: boolean;
    badge: 'blue_tick' | 'gold_tick' | 'none';
    verifiedAt?: Date;
  };

  ProfileMypts?: {
    currentBalance: number;
    lifetimeMypts: number;
  };

  ProfileReferal?: {
    referalLink?: string;
    referals: number;
  };

  ProfileBadges?: {

    badges?: Array<{
      id: string;
      name: string;
      category: string;
      description: string;
      icon: string;
      earnedAt: Date;
    }>;
  };

  analytics?: {
    Mypts?: {
      balance: number;
      usage: number;
      redeemed: number;
      invested: number;
    };
    Usage?: {
      stamps: number;
      reward: number;
      badges: number;
      milestones: number;
    };
    Profiling?: {
      completion: number;
      category: number;
      links: number;
      content: number;
    };
    Products?: {
      accessories: number;
      devices: number;
      taps: number;
      scans: number;
    };
    Networking?: {
      shared: number;
      views: number;
      contacts: number;
      relationships: number;
    };
    Circles?: {
      contacts: number;
      connections: number;
      following: number;
      followers: number;
      affiliations: number;
    };
    engagement?: {
      chats: number;
      calls: number;
      posts: number;
      comments: number;
    };
    plans?: {
      interactions: number;
      task: number;
      events: number;
      schedules: number;
    };
    data?: {
      entries: number;
      dataPts: number;
      tracking: number;
    };
    discover?: {
      searches: number;
      Reviews: number;
      survey: number;
      videos: number;
    };
  };

  templatedId: mongoose.Types.ObjectId;
  sections: ITemplateSection[];
  
  links?: Array<{
    name: string;
    label: string;
    category: string;
    icon?: string;
    baseUrl?: string;
    urlPattern?: string;
    placeholder?: string;
    value?: string; // The actual link value/URL
    enabled: boolean;
    active: boolean; // Whether the link is currently active/visible
    order: number;
    description?: string;
  }>;
  
  members?: mongoose.Types.ObjectId[];
  groups?: mongoose.Types.ObjectId[];

  availability?: {
    isAvailable: { type: Boolean, default: true }, // 24/7 available by default
    defaultDuration: { type: Number, default: 60 }, // 60 minutes default
    bufferTime: { type: Number, default: 15 }, // 15 minutes default
    startDate: { type: Date }, // Optional start date for when availability begins
    endDate: { type: Date }, // Optional end date for when availability ends
    
    // Unavailability periods - when user is NOT available (for personal profiles)
    unavailablePeriods: [{
      // Recurring unavailability (e.g., "Not available on Mondays")
      type: { type: String, enum: ['recurring', 'specific'], default: 'recurring' },
      
      // For recurring periods
      days: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
      isAllDay: { type: Boolean, default: true },
      startTime: { type: String }, // e.g., "09:00" - only if not all day
      endTime: { type: String }, // e.g., "17:00" - only if not all day
      
      // For specific dates
      date: { type: Date }, // Specific date when unavailable
      
      // For recurring periods - date range when the recurrence is active
      startDate: { type: Date }, // When the recurring period starts
      endDate: { type: Date }, // When the recurring period ends (optional)
      
      // Common fields
      reason: { type: String, trim: true }, // Optional reason
      repeatWeekly: { type: Boolean, default: true } // Whether this repeats every week
    }],
    
    // Available periods - when user IS available (for non-personal profiles)
    availablePeriods: [{
      // Recurring availability (e.g., "Available on Mondays")
      type: { type: String, enum: ['recurring', 'specific'], default: 'recurring' },
      
      // For recurring periods
      days: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
      isAllDay: { type: Boolean, default: true },
      startTime: { type: String }, // e.g., "09:00" - only if not all day
      endTime: { type: String }, // e.g., "17:00" - only if not all day
      
      // For specific dates
      date: { type: Date }, // Specific date when available
      
      // For recurring periods - date range when the recurrence is active
      startDate: { type: Date }, // When the recurring period starts
      endDate: { type: Date }, // When the recurring period ends (optional)
      
      // Common fields
      reason: { type: String, trim: true }, // Optional reason
      repeatWeekly: { type: Boolean, default: true } // Whether this repeats every week
    }],
    
    bookingWindow: {
      minNotice: { type: Number, default: 60 }, // 1 hour default
      maxAdvance: { type: Number, default: 30 } // 30 days default
    }
  };

  specificSettings: {
    [key: string]: any;
  };

  // Publication status
  published: boolean;
  publishedAt?: Date;
  publishedBy?: mongoose.Types.ObjectId;
}

interface ITemplateSection {
  key: string;
  label: string;
  order: number;
  icon?: string;
  collapsible?: boolean;
  fields: ITemplateField[];
}

interface ITemplateField {
  key: string;
  label: string;
  widget: FieldWidget;
  required?: boolean;
  default?: any;
  enabled: boolean;
  placeholder?: string;
  icon?: string; // Add icon property for field icons
  options?: IFieldOption[];
  validation?: IFieldValidation;
  value?: any;
}

interface IFieldOption {
  label: string;
  value: string | number;
}

interface IFieldValidation {
  min?: number;
  max?: number;
  regex?: string;
}

type FieldWidget =
  | 'text' | 'textarea' | 'number' | 'select' | 'multiselect'
  | 'email' | 'url' | 'phone' | 'date' | 'datetime'
  | 'boolean' | 'file' | 'image' | 'object' | 'list:text'
  | 'profile_reference' | 'user_reference';

// Define the Profile Schema
const ProfileSchema = new Schema<IProfile>(
  {
    profileCategory: {
      type: String,
      required: true,
      enum: ['accessory', 'group', 'individual'],
      index: true,
    },
    profileType: {
      type: String,
      required: true,
      enum: [
        // individual
        'personal', 'academic', 'work', 'professional', 'proprietor', 'freelancer', 'artist', 'influencer', 'athlete', 'provider', 'merchant', 'vendor',
        // accessory
        'emergency', 'medical', 'pet', 'ecommerce', 'home', 'transportation', 'driver', 'event', 'dependent', 'rider',
        // group
        'group', 'team', 'family', 'neighborhood', 'company', 'business', 'association', 'organization', 'institution', 'community'
      ],
      index: true
    },
    secondaryId: {
      type: String,
      unique: true,
      sparse: true, // Allow null values (for existing profiles until updated)
      index: true,
      validate: {
        validator: function(v: string) {
          // Must start with a letter and be 8 characters long with only alphanumeric characters
          return /^[a-zA-Z][a-zA-Z0-9]{7}$/.test(v);
        },
        message: props => `${props.value} is not a valid secondary ID. It must start with a letter and be 8 characters long.`
      }
    },
    profileInformation: {
      username: { type: String, required: true, trim: true, index: true },
      profileLink: { type: String, required: true, unique: true, index: true },
      shareToken: { type: String, trim: true }, // Share token for the profile
      title: { type: String, trim: true },
      name: { type: String, trim: true }, // For personal profiles - creator's full name
      accountHolder: { type: String, trim: true },
      pid: { type: String, trim: true },
      relationshipToAccountHolder: { type: String, trim: true },
      creator: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
      connectLink: { type: String, required: true, unique: true, index: true },
      followLink: { type: String, required: true, unique: true, index: true },
      followers: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],
      following: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],
      connectedProfiles: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],
      affiliatedProfiles: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],
      accessToken: { type: String, trim: true, index: true }, // Added for profile token authentication
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    },
    templatedId: { type: Schema.Types.ObjectId, ref: 'ProfileTemplate', required: true },
    sections: [{ type: Schema.Types.Mixed }],
    links: [{ type: Schema.Types.Mixed }],
    
    members: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],
    groups: [{ type: Schema.Types.ObjectId, ref: 'Profile' }],

    ProfileFormat: {
      profileImage: { type: String, trim: true },
      coverImage: { type: String, trim: true },
      profileLogo: { type: String, trim: true },
      customization: {
        theme: {
          primaryColor: { type: String, default: '#000000' },
          secondaryColor: { type: String, default: '#ffffff' },
          accent: { type: String, default: '#ff4081' },
          background: { type: String, default: '#f5f5f5' },
          text: { type: String, default: '#212121' }
        },
        layout: {
          sections: [{ id: String, type: String, order: Number, visible: { type: Boolean, default: true } }],
          gridStyle: { type: String, enum: ['right-sided', 'centered', 'left-sided'], default: 'centered' }
        }
      },
      customCSS: { type: String, trim: true },
      updatedAt: { type: Date, default: Date.now }
    },
    ProfileQrCode: {
      qrCode: String,
      staticQrCode: String,
      emailSignature: String,
      wallPaper: String,
      VirtualBackground: String
    },
    profileLocation: {
      city: String,
      stateOrProvince: String,
      country: String,
      countryCode: String,
      coordinates: {
        latitude: { type: Number, default: 0 },
        longitude: { type: Number, default: 0 }
      }
    },
    ProfileProducts: {
      type: { type: String, enum: ['Accessory', 'Device', 'None'], default: 'None' },
      name: String,
      description: String
    },
    verificationStatus: {
      isVerified: { type: Boolean, default: false },
      badge: {
        type: String,
        enum: ['blue_tick', 'gold_tick', 'none'],
        default: 'none',
      },
      verifiedAt: Date,
    },
    ProfileMypts: {
      currentBalance: { type: Number, default: 0 },
      lifetimeMypts: { type: Number, default: 0 },
    },
    ProfileReferal: {
      referalLink: String,
      referals: { type: Number, default: 0 },
    },
    ProfileBadges: {
      badges: [{
        id: String,
        name: String,
        category: String,
        description: String,
        icon: String,
        earnedAt: Date,
      }],
    },
    analytics: {
      Mypts: {
        balance: { type: Number, default: 0 },
        usage: { type: Number, default: 0 },
        redeemed: { type: Number, default: 0 },
        invested: { type: Number, default: 0 }
      },
      Usage: {
        stamps: { type: Number, default: 0 },
        reward: { type: Number, default: 0 },
        badges: { type: Number, default: 0 },
        milestones: { type: Number, default: 0 }
      },
      Profiling: {
        completion: { type: Number, default: 0 },
        category: { type: Number, default: 0 },
        links: { type: Number, default: 0 },
        content: { type: Number, default: 0 }
      },
      Products: {
        accessories: { type: Number, default: 0 },
        devices: { type: Number, default: 0 },
        taps: { type: Number, default: 0 },
        scans: { type: Number, default: 0 }
      },
      Networking: {
        shared: { type: Number, default: 0 },
        views: { type: Number, default: 0 },
        contacts: { type: Number, default: 0 },
        relationships: { type: Number, default: 0 }
      },
      Circles: {
        contacts: { type: Number, default: 0 },
        connections: { type: Number, default: 0 },
        following: { type: Number, default: 0 },
        followers: { type: Number, default: 0 },
        affiliations: { type: Number, default: 0 }
      },
      engagement: {
        chats: { type: Number, default: 0 },
        calls: { type: Number, default: 0 },
        posts: { type: Number, default: 0 },
        comments: { type: Number, default: 0 }
      },
      plans: {
        interactions: { type: Number, default: 0 },
        task: { type: Number, default: 0 },
        events: { type: Number, default: 0 },
        schedules: { type: Number, default: 0 },
      },
      data: {
        entries: { type: Number, default: 0 },
        dataPts: { type: Number, default: 0 },
        tracking: { type: Number, default: 0 }
      },
      discover: {
        searches: { type: Number, default: 0 },
        Reviews: { type: Number, default: 0 },
        survey: { type: Number, default: 0 },
        videos: { type: Number, default: 0 },
      }
    },
    availability: {
      isAvailable: { type: Boolean, default: true }, // 24/7 available by default
      defaultDuration: { type: Number, default: 60 }, // 60 minutes default
      bufferTime: { type: Number, default: 15 }, // 15 minutes default
      startDate: { type: Date }, // Optional start date for when availability begins
      endDate: { type: Date }, // Optional end date for when availability ends
      
      // Unavailability periods - when user is NOT available (for personal profiles)
      unavailablePeriods: [{
        // Recurring unavailability (e.g., "Not available on Mondays")
        type: { type: String, enum: ['recurring', 'specific'], default: 'recurring' },
        
        // For recurring periods
        days: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
        isAllDay: { type: Boolean, default: true },
        startTime: { type: String }, // e.g., "09:00" - only if not all day
        endTime: { type: String }, // e.g., "17:00" - only if not all day
        
        // For specific dates
        date: { type: Date }, // Specific date when unavailable
        
        // For recurring periods - date range when the recurrence is active
        startDate: { type: Date }, // When the recurring period starts
        endDate: { type: Date }, // When the recurring period ends (optional)
        
        // Common fields
        reason: { type: String, trim: true }, // Optional reason
        repeatWeekly: { type: Boolean, default: true } // Whether this repeats every week
      }],
      
      // Available periods - when user IS available (for non-personal profiles)
      availablePeriods: [{
        // Recurring availability (e.g., "Available on Mondays")
        type: { type: String, enum: ['recurring', 'specific'], default: 'recurring' },
        
        // For recurring periods
        days: [{ type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] }],
        isAllDay: { type: Boolean, default: true },
        startTime: { type: String }, // e.g., "09:00" - only if not all day
        endTime: { type: String }, // e.g., "17:00" - only if not all day
        
        // For specific dates
        date: { type: Date }, // Specific date when available
        
        // For recurring periods - date range when the recurrence is active
        startDate: { type: Date }, // When the recurring period starts
        endDate: { type: Date }, // When the recurring period ends (optional)
        
        // Common fields
        reason: { type: String, trim: true }, // Optional reason
        repeatWeekly: { type: Boolean, default: true } // Whether this repeats every week
      }],
      
      bookingWindow: {
        minNotice: { type: Number, default: 60 }, // 1 hour default
        maxAdvance: { type: Number, default: 30 } // 30 days default
      }
    },

    specificSettings: {
      type: Map,
      of: {
        type: Schema.Types.Mixed,
        default: {}
      }
    },

    // Publication status
    published: { type: Boolean, default: false },
    publishedAt: { type: Date },
    publishedBy: { type: Schema.Types.ObjectId, ref: 'User' }
  },
  { timestamps: true }
);

// Add profile methods for MyPts
ProfileSchema.methods.getMyPts = async function(): Promise<IMyPts> {
  // Find or create MyPts for this profile
  const myPts = await MyPtsModel.findOrCreate(this._id);
  return myPts;
};

ProfileSchema.methods.getMyPtsValue = async function(currency: string = 'USD'): Promise<{
  balance: number;
  valuePerPts: number;
  currency: string;
  symbol: string;
  totalValue: number;
  formattedValue: string;
}> {
  try {
    // Get MyPts balance
    const myPts = await this.getMyPts();

    // Get current MyPts value
    const currentValue = await MyPtsValueModel.getCurrentValue();

    // Get value in specified currency
    const valuePerPts = currentValue.getValueInCurrency(currency);

    // Calculate total value
    const totalValue = myPts.balance * valuePerPts;

    // Get currency symbol
    let symbol = currentValue.baseSymbol;
    if (currency !== currentValue.baseCurrency) {
      const exchangeRate = currentValue.exchangeRates.find(er => er.currency === currency);
      if (exchangeRate) {
        symbol = exchangeRate.symbol;
      }
    }

    // Format the value
    const formattedValue = `${symbol}${totalValue.toFixed(2)}`;

    return {
      balance: myPts.balance,
      valuePerPts,
      currency,
      symbol,
      totalValue,
      formattedValue
    };
  } catch (error) {
    console.error('Error getting MyPts value:', error);

    // Fallback to default values if there's an error
    return {
      balance: this.ProfileMypts?.currentBalance || 0,
      valuePerPts: 0.024, // Default base value
      currency: currency,
      symbol: currency === 'USD' ? '$' : currency === 'EUR' ? '€' : currency,
      totalValue: (this.ProfileMypts?.currentBalance || 0) * 0.024,
      formattedValue: `${currency === 'USD' ? '$' : currency === 'EUR' ? '€' : currency}${((this.ProfileMypts?.currentBalance || 0) * 0.024).toFixed(2)}`
    };
  }
};

// Add post-save middleware to create a referral code
ProfileSchema.post('save', async function(doc) {
  try {
    // Import here to avoid circular dependency
    const { ProfileReferralService } = require('../services/profile-referral.service');
    await ProfileReferralService.initializeReferralCode(doc._id);
    console.log(`Referral code initialized for profile: ${doc._id}`);
  } catch (error) {
    console.error(`Error initializing referral code for profile ${doc._id}:`, error);
    // Don't throw the error to avoid disrupting the save operation
  }
});

// Add method to check availability for a specific time slot
ProfileSchema.methods.checkAvailability = function(startTime: Date, endTime: Date): boolean {
  // Check if availability is enabled
  if (!this.availability?.isAvailable) return false;

  // Check if the date is within the availability window
  if (this.availability.startDate && startTime < this.availability.startDate) return false;
  if (this.availability.endDate && endTime > this.availability.endDate) return false;
  
  // Check against unavailable periods
  const unavailablePeriods = this.availability.unavailablePeriods || [];
  
  for (const period of unavailablePeriods) {
    if (this.isTimeInUnavailablePeriod(startTime, endTime, period)) {
      return false;
    }
  }
  
  return true; // Available by default
};

ProfileSchema.methods.isTimeInUnavailablePeriod = function(startTime: Date, endTime: Date, period: any): boolean {
  if (period.type === 'specific') {
    // Check specific date unavailability
    const periodDate = new Date(period.date);
    const requestDate = new Date(startTime.getFullYear(), startTime.getMonth(), startTime.getDate());
    const periodDateOnly = new Date(periodDate.getFullYear(), periodDate.getMonth(), periodDate.getDate());
    
    if (requestDate.getTime() === periodDateOnly.getTime()) {
      if (period.isAllDay) {
        return true; // Unavailable all day
      }
      // Check specific time range
      return this.isTimeInRange(startTime, endTime, period.startTime, period.endTime);
    }
  } else if (period.type === 'recurring') {
    // Check recurring unavailability
    const dayName = this.getDayName(startTime.getDay());
    
    if (period.days && period.days.includes(dayName)) {
      if (period.isAllDay) {
        return true; // Unavailable all day
      }
      // Check specific time range
      return this.isTimeInRange(startTime, endTime, period.startTime, period.endTime);
    }
  }
  
  return false;
};

ProfileSchema.methods.isTimeInRange = function(startTime: Date, endTime: Date, unavailableStart: string, unavailableEnd: string): boolean {
  if (!unavailableStart || !unavailableEnd) return false;
  
  const [startHour, startMin] = unavailableStart.split(':').map(Number);
  const [endHour, endMin] = unavailableEnd.split(':').map(Number);
  
  const requestStartTime = startTime.getHours() * 60 + startTime.getMinutes();
  const requestEndTime = endTime.getHours() * 60 + endTime.getMinutes();
  const unavailableStartTime = startHour * 60 + startMin;
  const unavailableEndTime = endHour * 60 + endMin;
  
  // Check if requested time overlaps with unavailable time
  return !(requestEndTime <= unavailableStartTime || requestStartTime >= unavailableEndTime);
};

ProfileSchema.methods.getDayName = function(dayIndex: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayIndex];
};

// Updated method to get available slots for a specific date using availability system
ProfileSchema.methods.getAvailableSlots = function(date: Date): Array<{ start: Date; end: Date }> {
  if (!this.availability?.isAvailable) return [];
  
  // Check if date is within availability window
  if (this.availability.startDate && date < this.availability.startDate) return [];
  if (this.availability.endDate && date > this.availability.endDate) return [];
  
  const slots: Array<{ start: Date; end: Date }> = [];
  const defaultDuration = this.availability.defaultDuration || 60;
  
  // Start with full day availability (24 hours)
  const dayStart = new Date(date);
  dayStart.setHours(0, 0, 0, 0);
  const dayEnd = new Date(date);
  dayEnd.setHours(23, 59, 59, 999);
  
  // Get all unavailable periods for this date
  const unavailablePeriods = this.getUnavailablePeriodsForDate(date);
  
  // Create available slots by excluding unavailable periods
  const availableRanges = this.getAvailableRanges(dayStart, dayEnd, unavailablePeriods);
  
  // Convert available ranges to slots
  for (const range of availableRanges) {
    const rangeSlots = this.createSlotsFromRange(range.start, range.end, defaultDuration);
    slots.push(...rangeSlots);
  }
  
  return slots;
};

ProfileSchema.methods.getUnavailablePeriodsForDate = function(date: Date): Array<{ start: Date; end: Date }> {
  const unavailablePeriods = this.availability.unavailablePeriods || [];
  const periods: Array<{ start: Date; end: Date }> = [];
  
  for (const period of unavailablePeriods) {
    if (period.type === 'specific') {
      // Check specific date
      const periodDate = new Date(period.date);
      const requestDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const periodDateOnly = new Date(periodDate.getFullYear(), periodDate.getMonth(), periodDate.getDate());
      
      if (requestDate.getTime() === periodDateOnly.getTime()) {
        if (period.isAllDay) {
          const start = new Date(date);
          start.setHours(0, 0, 0, 0);
          const end = new Date(date);
          end.setHours(23, 59, 59, 999);
          periods.push({ start, end });
        } else if (period.startTime && period.endTime) {
          const [startHour, startMin] = period.startTime.split(':').map(Number);
          const [endHour, endMin] = period.endTime.split(':').map(Number);
          
          const start = new Date(date);
          start.setHours(startHour, startMin, 0, 0);
          const end = new Date(date);
          end.setHours(endHour, endMin, 0, 0);
          
          periods.push({ start, end });
        }
      }
    } else if (period.type === 'recurring') {
      // Check recurring pattern
      const dayName = this.getDayName(date.getDay());
      
      if (period.days && period.days.includes(dayName)) {
        if (period.isAllDay) {
          const start = new Date(date);
          start.setHours(0, 0, 0, 0);
          const end = new Date(date);
          end.setHours(23, 59, 59, 999);
          periods.push({ start, end });
        } else if (period.startTime && period.endTime) {
          const [startHour, startMin] = period.startTime.split(':').map(Number);
          const [endHour, endMin] = period.endTime.split(':').map(Number);
          
          const start = new Date(date);
          start.setHours(startHour, startMin, 0, 0);
          const end = new Date(date);
          end.setHours(endHour, endMin, 0, 0);
          
          periods.push({ start, end });
        }
      }
    }
  }
  
  return periods;
};

ProfileSchema.methods.getAvailableRanges = function(dayStart: Date, dayEnd: Date, unavailablePeriods: Array<{ start: Date; end: Date }>): Array<{ start: Date; end: Date }> {
  if (unavailablePeriods.length === 0) {
    return [{ start: dayStart, end: dayEnd }];
  }
  
  // Sort unavailable periods by start time
  const sortedPeriods = unavailablePeriods.sort((a, b) => a.start.getTime() - b.start.getTime());
  
  const availableRanges: Array<{ start: Date; end: Date }> = [];
  let currentStart = dayStart;
  
  for (const period of sortedPeriods) {
    // Add available range before this unavailable period
    if (currentStart < period.start) {
      availableRanges.push({ start: currentStart, end: period.start });
    }
    
    // Move current start to after this unavailable period
    currentStart = new Date(Math.max(currentStart.getTime(), period.end.getTime()));
  }
  
  // Add remaining available time after last unavailable period
  if (currentStart < dayEnd) {
    availableRanges.push({ start: currentStart, end: dayEnd });
  }
  
  return availableRanges;
};

ProfileSchema.methods.createSlotsFromRange = function(start: Date, end: Date, durationMinutes: number): Array<{ start: Date; end: Date }> {
  const slots: Array<{ start: Date; end: Date }> = [];
  const bufferTime = this.availability.bufferTime || 0;
  
  let currentTime = new Date(start);
  
  while (currentTime.getTime() + (durationMinutes * 60 * 1000) <= end.getTime()) {
    const slotEnd = new Date(currentTime.getTime() + (durationMinutes * 60 * 1000));
    
        slots.push({
          start: new Date(currentTime),
      end: slotEnd
    });
    
    // Move to next slot (including buffer time)
    currentTime = new Date(slotEnd.getTime() + (bufferTime * 60 * 1000));
  }

  return slots;
};

// Add the addSettings method
ProfileSchema.methods.addSettings = async function(settings: Record<string, any>): Promise<void> {
  this.settings = { ...this.settings || {}, ...settings };
  await this.save();
};

// Define interface for model type with methods
interface IProfileModel extends Model<IProfile> {
  // Add any static methods here if needed
}

export const ProfileModel: Model<IProfile> = mongoose.model<IProfile, IProfileModel>('Profile', ProfileSchema);
