import mongoose, { Document, Schema } from 'mongoose';

export interface IUserPlan extends Document {
  userId: mongoose.Types.ObjectId;
  planId: mongoose.Types.ObjectId;
  planName: 'Free' | 'Basic' | 'Plus' | 'Premium' | 'Custom';
  planType: 'free' | 'basic' | 'plus' | 'premium' | 'custom';
  billingCycle: 'monthly' | 'yearly';
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  
  // Payment Information
  paymentProvider: 'stripe' | 'flutterwave' | 'manual' | 'test';
  paymentStatus: 'pending' | 'active' | 'past_due' | 'canceled' | 'unpaid' | 'failed';
  subscriptionId?: string; // Stripe/Flutterwave subscription ID
  paymentMethodId?: string;
  lastPaymentDate?: Date;
  nextBillingDate?: Date;
  
  // Usage Statistics
  usageStats: {
    individualProfiles: number;
    secondaryAccounts: number;
    accessoryProfiles: number;
    groupProfiles: number;
    storageUsedMB: number;
    leadsGenerated: number;
    myPtsEarned: number;
    scansPerformed: number;
    qrCodesGenerated: number;
    nfcLinksCreated: number;
  };
  
  // Limits (copied from plan for historical accuracy)
  limits: {
    individualProfiles: number;
    secondaryAccounts: number;
    accessoryProfiles: number;
    groupProfiles: number;
    storageGB: number;
    nfcProductLinking: number;
  };
  
  // Features (copied from plan for historical accuracy)
  features: {
    insightsAccess: 'none' | 'basic' | 'advanced' | 'full' | 'custom';
    qrCodeAccess: 'basic' | 'branded' | 'animated' | 'skinned' | 'white-label';
    scannerAccess: 'limited' | 'standard' | 'smart' | 'smart-custom' | 'enterprise';
    communityAccess: 'view' | 'join' | 'join-admin' | 'full-manage' | 'private';
    myPtsRate: number;
    payoutPriority: 'none' | 'standard' | 'priority' | 'express' | 'vip';
    payoutDays: number;
    rewardSystemAccess: boolean;
    bonusTriggers: 'none' | 'manual' | 'automated' | 'event-driven' | 'api-integrated';
    gamifiedActivities: boolean;
    circleCreation: 'none' | 'join-only' | 'create-3' | 'unlimited' | 'private-role';
    leadsReferrals: 'none' | 'basic' | 'dashboard' | 'crm-tools' | 'crm-integrate';
    teamProxyRoles: boolean;
    affiliateProgram: 'none' | 'invite-only' | 'tiered' | 'full-dashboard' | 'partner-portal';
    addonMarketplace: boolean;
    supportType: 'community' | 'email' | 'priority-email' | 'live-chat' | 'account-manager';
  };
  
  // Metadata
  autoRenew: boolean;
  cancelReason?: string;
  upgradeHistory: Array<{
    fromPlan: string;
    toPlan: string;
    date: Date;
    reason?: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const UserPlanSchema = new Schema<IUserPlan>(
  {
    userId: { 
      type: Schema.Types.ObjectId, 
      ref: 'User', 
      required: true 
    },
    planId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Plan', 
      required: true 
    },
    planName: {
      type: String,
      enum: ['Free', 'Basic', 'Plus', 'Premium', 'Custom'],
      required: true,
    },
    planType: {
      type: String,
      enum: ['free', 'basic', 'plus', 'premium', 'custom'],
      required: true,
    },
    billingCycle: {
      type: String,
      enum: ['monthly', 'yearly'],
      default: 'monthly',
    },
    assignedAt: { 
      type: Date, 
      default: Date.now 
    },
    expiresAt: { 
      type: Date 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    
    // Payment Information
    paymentProvider: {
      type: String,
      enum: ['stripe', 'flutterwave', 'manual', 'test'],
      default: 'manual'
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'active', 'past_due', 'canceled', 'unpaid', 'failed'],
      default: 'pending'
    },
    subscriptionId: {
      type: String
    },
    paymentMethodId: {
      type: String
    },
    lastPaymentDate: {
      type: Date
    },
    nextBillingDate: {
      type: Date
    },
    
    // Usage Statistics
    usageStats: {
      individualProfiles: { type: Number, default: 0 },
      secondaryAccounts: { type: Number, default: 0 },
      accessoryProfiles: { type: Number, default: 0 },
      groupProfiles: { type: Number, default: 0 },
      storageUsedMB: { type: Number, default: 0 },
      leadsGenerated: { type: Number, default: 0 },
      myPtsEarned: { type: Number, default: 0 },
      scansPerformed: { type: Number, default: 0 },
      qrCodesGenerated: { type: Number, default: 0 },
      nfcLinksCreated: { type: Number, default: 0 }
    },
    
    // Limits (copied from plan)
    limits: {
      individualProfiles: { type: Number, required: true },
      secondaryAccounts: { type: Number, required: true },
      accessoryProfiles: { type: Number, required: true },
      groupProfiles: { type: Number, required: true },
      storageGB: { type: Number, required: true },
      nfcProductLinking: { type: Number, required: true }
    },
    
    // Features (copied from plan)
    features: {
      insightsAccess: {
        type: String,
        enum: ['none', 'basic', 'advanced', 'full', 'custom'],
        required: true
      },
      qrCodeAccess: {
        type: String,
        enum: ['basic', 'branded', 'animated', 'skinned', 'white-label'],
        required: true
      },

      scannerAccess: {
        type: String,
        enum: ['limited', 'standard', 'smart', 'smart-custom', 'enterprise'],
        required: true
      },
      communityAccess: {
        type: String,
        enum: ['view', 'join', 'join-admin', 'full-manage', 'private'],
        required: true
      },
      myPtsRate: { type: Number, required: true },
      payoutPriority: {
        type: String,
        enum: ['none', 'standard', 'priority', 'express', 'vip'],
        required: true
      },
      payoutDays: { type: Number, required: true },
      rewardSystemAccess: { type: Boolean, required: true },
      bonusTriggers: {
        type: String,
        enum: ['none', 'manual', 'automated', 'event-driven', 'api-integrated'],
        required: true
      },
      gamifiedActivities: { type: Boolean, required: true },
      circleCreation: {
        type: String,
        enum: ['none', 'join-only', 'create-3', 'unlimited', 'private-role'],
        required: true
      },
      leadsReferrals: {
        type: String,
        enum: ['none', 'basic', 'dashboard', 'crm-tools', 'crm-integrate'],
        required: true
      },
      teamProxyRoles: { type: Boolean, required: true },
      affiliateProgram: {
        type: String,
        enum: ['none', 'invite-only', 'tiered', 'full-dashboard', 'partner-portal'],
        required: true
      },
      addonMarketplace: { type: Boolean, required: true },
      supportType: {
        type: String,
        enum: ['community', 'email', 'priority-email', 'live-chat', 'account-manager'],
        required: true
      }
    },
    
    // Metadata
    autoRenew: { type: Boolean, default: true },
    cancelReason: { type: String },
    upgradeHistory: [{
      fromPlan: { type: String, required: true },
      toPlan: { type: String, required: true },
      date: { type: Date, default: Date.now },
      reason: { type: String }
    }]
  },
  {
    timestamps: true
  }
);

// Indexes for efficient queries
UserPlanSchema.index({ userId: 1, isActive: 1 });
UserPlanSchema.index({ planId: 1 });
UserPlanSchema.index({ paymentStatus: 1 });
UserPlanSchema.index({ expiresAt: 1 });
UserPlanSchema.index({ subscriptionId: 1 });

// Compound index for user's active plan
UserPlanSchema.index({ userId: 1, isActive: 1, expiresAt: 1 });

export default mongoose.model<IUserPlan>('UserPlan', UserPlanSchema); 