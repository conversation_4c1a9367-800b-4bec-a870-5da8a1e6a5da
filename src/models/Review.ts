import { Schema, model, Types, Document } from 'mongoose';

// Improvement Categories Schema
const ImprovementCategorySchema = new Schema({
  category: {
    type: String,
    enum: [
      'overall_service',
      'customer_support', 
      'speed_efficiency',
      'profile_setup_experience',
      'ease_of_use_navigation',
      'other'
    ],
    required: true
  }
});

// Review Interface
export interface IReview extends Document {
  userId: Types.ObjectId;

  // Basic Review Content
  rating: number;
  subject?: string;
  reason?: string;
  feedback: string; // renamed from content for clarity

  // Improvement Categories
  improvementCategories: {
    category: string;
  }[];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

// Review Schema
const ReviewSchema = new Schema<IReview>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },

  // Basic Review Content
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
    index: true
  },
  subject: {
    type: String,
    maxlength: 200,
    trim: true
  },
  reason: {
    type: String,
    maxlength: 500,
    trim: true
  },
  feedback: {
    type: String,
    required: true,
    maxlength: 2000,
    trim: true
  },

  // Improvement Categories
  improvementCategories: [ImprovementCategorySchema],

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes
ReviewSchema.index({ userId: 1 });
ReviewSchema.index({ rating: 1 });
ReviewSchema.index({ createdAt: -1 });

// Static methods
ReviewSchema.statics.getRatingStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    }
  ]);
};

const Review = model<IReview>('Review', ReviewSchema);

export default Review; 