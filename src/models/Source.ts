import mongoose, { Document, Schema } from 'mongoose';

export interface ISource extends Document {
  name: string;
  displayName: string;
  type: SourceType;
  category: SourceCategory;
  icon?: {
    url?: string;
    filename?: string;
    originalName?: string;
    mimeType?: string;
    size?: number;
    uploadedAt?: Date;
  };
  description?: string;
  isActive: boolean;
  isEnabled: boolean;
  requiresSetup: boolean;
  setupInstructions?: string;
  apiEndpoint?: string;
  apiKey?: string;
  apiSecret?: string;
  webhookUrl?: string;
  permissions: string[];
  dataFields: SourceDataField[];
  syncSettings: SourceSyncSettings;
  rateLimits: SourceRateLimits;
  metadata?: Record<string, any>;
  createdBy: mongoose.Types.ObjectId;
  updatedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export enum SourceType {
  PHONE = 'phone',
  GOOGLE = 'google',
  OUTLOOK = 'outlook',
  WHATSAPP = 'whatsapp',
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
  LINKEDIN = 'linkedin',
  TWITTER = 'twitter',
  TELEGRAM = 'telegram',
  EMAIL = 'email',
  SMS = 'sms',
  CUSTOM = 'custom'
}

export enum SourceCategory {
  SOCIAL_MEDIA = 'social_media',
  COMMUNICATION = 'communication',
  PRODUCTIVITY = 'productivity',
  CONTACT = 'contact',
  MESSAGING = 'messaging',
  CUSTOM = 'custom'
}

export interface SourceDataField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required: boolean;
  description?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    enum?: string[];
  };
  isSensitive?: boolean;
  isSearchable?: boolean;
}

export interface SourceSyncSettings {
  autoSync: boolean;
  syncInterval: number; // in minutes
  lastSyncAt?: Date;
  nextSyncAt?: Date;
  syncDirection: 'import' | 'export' | 'bidirectional';
  conflictResolution: 'source_wins' | 'target_wins' | 'manual';
  batchSize: number;
  retryAttempts: number;
  retryDelay: number; // in seconds
}

export interface SourceRateLimits {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit: number;
  cooldownPeriod: number; // in seconds
}

const sourceDataFieldSchema = new Schema<SourceDataField>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'date', 'array', 'object'],
    required: true
  },
  required: {
    type: Boolean,
    default: false
  },
  description: {
    type: String,
    trim: true
  },
  validation: {
    minLength: Number,
    maxLength: Number,
    pattern: String,
    enum: [String]
  },
  isSensitive: {
    type: Boolean,
    default: false
  },
  isSearchable: {
    type: Boolean,
    default: true
  }
});

const sourceSyncSettingsSchema = new Schema<SourceSyncSettings>({
  autoSync: {
    type: Boolean,
    default: false
  },
  syncInterval: {
    type: Number,
    default: 60, // 1 hour
    min: 1
  },
  lastSyncAt: {
    type: Date
  },
  nextSyncAt: {
    type: Date
  },
  syncDirection: {
    type: String,
    enum: ['import', 'export', 'bidirectional'],
    default: 'import'
  },
  conflictResolution: {
    type: String,
    enum: ['source_wins', 'target_wins', 'manual'],
    default: 'source_wins'
  },
  batchSize: {
    type: Number,
    default: 100,
    min: 1,
    max: 1000
  },
  retryAttempts: {
    type: Number,
    default: 3,
    min: 0,
    max: 10
  },
  retryDelay: {
    type: Number,
    default: 30,
    min: 1
  }
});

const sourceRateLimitsSchema = new Schema<SourceRateLimits>({
  requestsPerMinute: {
    type: Number,
    default: 60,
    min: 1
  },
  requestsPerHour: {
    type: Number,
    default: 1000,
    min: 1
  },
  requestsPerDay: {
    type: Number,
    default: 10000,
    min: 1
  },
  burstLimit: {
    type: Number,
    default: 10,
    min: 1
  },
  cooldownPeriod: {
    type: Number,
    default: 60,
    min: 0
  }
});

const sourceSchema = new Schema<ISource>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true
    },
    displayName: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: Object.values(SourceType),
      required: true
    },
    category: {
      type: String,
      enum: Object.values(SourceCategory),
      required: true
    },
    icon: {
      type: Object, // Changed to Object to handle file details
      default: {}
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isEnabled: {
      type: Boolean,
      default: false
    },
    requiresSetup: {
      type: Boolean,
      default: true
    },
    setupInstructions: {
      type: String,
      trim: true
    },
    apiEndpoint: {
      type: String,
      trim: true
    },
    apiKey: {
      type: String,
      trim: true,
      select: false // Don't include in queries by default for security
    },
    apiSecret: {
      type: String,
      trim: true,
      select: false // Don't include in queries by default for security
    },
    webhookUrl: {
      type: String,
      trim: true
    },
    permissions: [{
      type: String,
      trim: true
    }],
    dataFields: [sourceDataFieldSchema],
    syncSettings: {
      type: sourceSyncSettingsSchema,
      default: () => ({})
    },
    rateLimits: {
      type: sourceRateLimitsSchema,
      default: () => ({})
    },
    metadata: {
      type: Map,
      of: Schema.Types.Mixed
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  {
    timestamps: true
  }
);

// Indexes
sourceSchema.index({ name: 1 }, { unique: true });
sourceSchema.index({ type: 1 });
sourceSchema.index({ category: 1 });
sourceSchema.index({ isActive: 1 });
sourceSchema.index({ isEnabled: 1 });
sourceSchema.index({ 'syncSettings.autoSync': 1 });
sourceSchema.index({ 'syncSettings.nextSyncAt': 1 });
sourceSchema.index({ createdBy: 1 });
sourceSchema.index({ updatedAt: -1 });

// Virtual for full API configuration
sourceSchema.virtual('apiConfig').get(function() {
  if (!this.apiEndpoint) return null;
  
  return {
    endpoint: this.apiEndpoint,
    hasCredentials: !!(this.apiKey && this.apiSecret),
    hasWebhook: !!this.webhookUrl
  };
});

// Virtual for source status
sourceSchema.virtual('status').get(function() {
  if (!this.isActive) return 'inactive';
  if (!this.isEnabled) return 'disabled';
  if (this.requiresSetup && (!this.apiKey || !this.apiSecret)) return 'needs_setup';
  return 'active';
});

// Instance methods
sourceSchema.methods.canSync = function(): boolean {
  return this.isActive && this.isEnabled && !this.requiresSetup;
};

sourceSchema.methods.getNextSyncTime = function(): Date | null {
  if (!this.syncSettings.autoSync || !this.syncSettings.lastSyncAt) {
    return null;
  }
  
  const nextSync = new Date(this.syncSettings.lastSyncAt);
  nextSync.setMinutes(nextSync.getMinutes() + this.syncSettings.syncInterval);
  return nextSync;
};

sourceSchema.methods.updateSyncTime = function(): void {
  this.syncSettings.lastSyncAt = new Date();
  this.syncSettings.nextSyncAt = this.getNextSyncTime();
};

// Static methods
sourceSchema.statics.findByType = function(type: SourceType) {
  return this.find({ type, isActive: true });
};

sourceSchema.statics.findByCategory = function(category: SourceCategory) {
  return this.find({ category, isActive: true });
};

sourceSchema.statics.findEnabled = function() {
  return this.find({ isActive: true, isEnabled: true });
};

sourceSchema.statics.findReadyForSync = function() {
  return this.find({
    isActive: true,
    isEnabled: true,
    'syncSettings.autoSync': true,
    $or: [
      { 'syncSettings.nextSyncAt': { $lte: new Date() } },
      { 'syncSettings.nextSyncAt': { $exists: false } }
    ]
  });
};

// Pre-save middleware
sourceSchema.pre('save', function(next) {
  // Update nextSyncAt when sync settings change
  if (this.isModified('syncSettings.autoSync') || this.isModified('syncSettings.syncInterval')) {
    if (this.syncSettings.autoSync && this.syncSettings.lastSyncAt) {
      const nextSync = new Date(this.syncSettings.lastSyncAt);
      nextSync.setMinutes(nextSync.getMinutes() + this.syncSettings.syncInterval);
      this.syncSettings.nextSyncAt = nextSync;
    } else {
      this.syncSettings.nextSyncAt = undefined;
    }
  }
  
  // Ensure name is lowercase
  if (this.isModified('name')) {
    this.name = this.name.toLowerCase();
  }
  
  next();
});

export const Source = mongoose.model<ISource>('Source', sourceSchema); 