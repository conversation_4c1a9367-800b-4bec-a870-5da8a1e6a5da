import mongoose, { Document, Schema } from 'mongoose';

export interface IExchange extends Document {
  _id: mongoose.Types.ObjectId;
  exchangeId: string; // Human-readable ID like "EXCH8734KXA"
  senderProfileId: mongoose.Types.ObjectId; // Reference to sender's profile
  receiverProfileId: mongoose.Types.ObjectId; // Reference to receiver's profile
  senderSecondaryId?: string; // Secondary ID for easy reference
  receiverSecondaryId?: string; // Secondary ID for easy reference
  profileName?: string; // Optional custom label for the exchange
  exchangeReason?: string; // Reason for the exchange (e.g., "Met at BioTech 2025")
  channel: 'in_app' | 'nfc' | 'qr' | 'manual'; // How the exchange was initiated
  status: 'pending' | 'completed' | 'declined' | 'expired' | 'cancelled';
  
  // Exchange details
  exchangeData?: {
    senderProfile?: any; // Snapshot of sender's profile at time of exchange
    receiverProfile?: any; // Snapshot of receiver's profile at time of exchange
    sharedSections?: string[]; // Which sections were shared
    customMessage?: string; // Optional custom message
  };
  
  // Notification tracking
  notifications: {
    senderNotified: boolean;
    receiverNotified: boolean;
    senderNotificationId?: mongoose.Types.ObjectId;
    receiverNotificationId?: mongoose.Types.ObjectId;
  };
  
  // Analytics and tracking
  analytics?: {
    viewed: boolean;
    viewedAt?: Date;
    responded: boolean;
    respondedAt?: Date;
    myPtsAwarded: number;
    engagementScore?: number;
  };
  
  // Metadata
  metadata?: {
    location?: {
      latitude?: number;
      longitude?: number;
      city?: string;
      country?: string;
    };
    deviceInfo?: {
      userAgent?: string;
      ipAddress?: string;
      deviceType?: string;
    };
    tags?: string[]; // For categorization (e.g., ["conference", "networking", "business"])
  };
  
  // Timestamps
  expiresAt?: Date; // When the exchange expires
  completedAt?: Date; // When the exchange was completed
  declinedAt?: Date; // When the exchange was declined
  cancelledAt?: Date; // When the exchange was cancelled
  
  createdAt: Date;
  updatedAt: Date;
}

const exchangeSchema = new Schema<IExchange>(
  {
    exchangeId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    senderProfileId: {
      type: Schema.Types.ObjectId,
      ref: 'Profile',
      required: true,
      index: true,
    },
    receiverProfileId: {
      type: Schema.Types.ObjectId,
      ref: 'Profile',
      required: true,
      index: true,
    },
    senderSecondaryId: {
      type: String,
      index: true,
    },
    receiverSecondaryId: {
      type: String,
      index: true,
    },
    profileName: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    exchangeReason: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    channel: {
      type: String,
      enum: ['in_app', 'nfc', 'qr', 'manual'],
      required: true,
      default: 'in_app',
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'declined', 'expired', 'cancelled'],
      required: true,
      default: 'pending',
      index: true,
    },
    exchangeData: {
      senderProfile: {
        type: Schema.Types.Mixed,
      },
      receiverProfile: {
        type: Schema.Types.Mixed,
      },
      sharedSections: [{
        type: String,
      }],
      customMessage: {
        type: String,
        maxlength: 1000,
      },
    },
    notifications: {
      senderNotified: {
        type: Boolean,
        default: false,
      },
      receiverNotified: {
        type: Boolean,
        default: false,
      },
      senderNotificationId: {
        type: Schema.Types.ObjectId,
        ref: 'Notification',
      },
      receiverNotificationId: {
        type: Schema.Types.ObjectId,
        ref: 'Notification',
      },
    },
    analytics: {
      viewed: {
        type: Boolean,
        default: false,
      },
      viewedAt: {
        type: Date,
      },
      responded: {
        type: Boolean,
        default: false,
      },
      respondedAt: {
        type: Date,
      },
      myPtsAwarded: {
        type: Number,
        default: 0,
      },
      engagementScore: {
        type: Number,
        min: 0,
        max: 100,
      },
    },
    metadata: {
      location: {
        latitude: Number,
        longitude: Number,
        city: String,
        country: String,
      },
      deviceInfo: {
        userAgent: String,
        ipAddress: String,
        deviceType: String,
      },
      tags: [String],
    },
    expiresAt: {
      type: Date,
      default: function() {
        // Default expiry: 30 days from creation
        const expiry = new Date();
        expiry.setDate(expiry.getDate() + 30);
        return expiry;
      },
    },
    completedAt: Date,
    declinedAt: Date,
    cancelledAt: Date,
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
exchangeSchema.index({ senderProfileId: 1, createdAt: -1 });
exchangeSchema.index({ receiverProfileId: 1, createdAt: -1 });
exchangeSchema.index({ status: 1, createdAt: -1 });
exchangeSchema.index({ channel: 1, createdAt: -1 });
exchangeSchema.index({ 'metadata.tags': 1 });
exchangeSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for expired exchanges

// Pre-save middleware to generate exchangeId if not provided
exchangeSchema.pre('save', async function(next) {
  if (!this.exchangeId) {
    this.exchangeId = await generateExchangeId();
  }
  next();
});

// Static method to generate unique exchange ID
exchangeSchema.statics.generateExchangeId = async function(): Promise<string> {
  const prefix = 'EXCH';
  let exchangeId: string;
  let isUnique = false;
  
  while (!isUnique) {
    const randomPart = Math.random().toString(36).substring(2, 10).toUpperCase();
    exchangeId = `${prefix}${randomPart}`;
    
    const existingExchange = await this.findOne({ exchangeId });
    if (!existingExchange) {
      isUnique = true;
    }
  }
  
  return exchangeId;
};

// Instance method to check if exchange is expired
exchangeSchema.methods.isExpired = function(): boolean {
  return this.expiresAt && new Date() > this.expiresAt;
};

// Instance method to check if exchange can be cancelled
exchangeSchema.methods.canBeCancelled = function(): boolean {
  return this.status === 'pending' && !this.isExpired();
};

// Instance method to check if exchange can be declined
exchangeSchema.methods.canBeDeclined = function(): boolean {
  return this.status === 'pending' && !this.isExpired();
};

// Instance method to check if exchange can be completed
exchangeSchema.methods.canBeCompleted = function(): boolean {
  return this.status === 'pending' && !this.isExpired();
};

// Virtual for formatted timestamp
exchangeSchema.virtual('formattedTimestamp').get(function() {
  return this.createdAt.toISOString();
});

// Ensure virtuals are included in JSON output
exchangeSchema.set('toJSON', { virtuals: true });
exchangeSchema.set('toObject', { virtuals: true });

// Helper function to generate exchange ID
async function generateExchangeId(): Promise<string> {
  const Exchange = mongoose.model('Exchange');
  const prefix = 'EXCH';
  let exchangeId: string;
  let isUnique = false;
  
  while (!isUnique) {
    const randomPart = Math.random().toString(36).substring(2, 10).toUpperCase();
    exchangeId = `${prefix}${randomPart}`;
    
    const existingExchange = await Exchange.findOne({ exchangeId });
    if (!existingExchange) {
      isUnique = true;
    }
  }
  
  return exchangeId;
}

export const Exchange = mongoose.model<IExchange>('Exchange', exchangeSchema);
export default Exchange; 