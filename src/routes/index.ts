/**
 * @file index.ts
 * @description Central Route Configuration & Management
 * =================================================
 *
 * ----------------
 * - Separation of Concerns: Routes are modularized by domain
 * - Security First: Protected routes enforce authentication
 * - Scalability: Easy addition of new route modules
 * - Maintainability: Clear structure and organization
 *
 * Route Categories:
 * ---------------
 * 1. Public Routes
 *    - Authentication endpoints (login, register, password reset)
 *    - Health checks and public info
 *
 * 2. Protected Routes
 *    - Profile management
 *    - Connection handling
 *    - User-specific operations
 *
 * Security Features:
 * ----------------
 * - JWT authentication via middleware
 * - Role-based access control
 * - Request validation
 *
 * @version 1.0.0
 * @license MIT
 *
 * @example
 * // Adding a new route module:
 * import newFeatureRoutes from './newFeature.routes';
 * app.use('/api/new-feature', protect, newFeatureRoutes);
 */

import { Application } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes'
import profileRoutes from './profile.routes';
import profileConnectionRoutes from './profile-connection.routes';
import connectionAnalyticsRoutes from './connection-analytics.routes';
import contactRoutes from './contact.route';
import locationRoutes from './location.routes';
import panicRoutes from './panic.routes';
import RelationshipTypeRoutes from './relationshipType.routes';
import relationshipRoutes from './relationship.routes';
import logsRoutes from './logs.routes';
import activityLogsRoutes from './activity-logs.routes';
import myPtsRoutes from './my-pts.routes';
import myPtsValueRoutes from './my-pts-value.routes';
import myPtsHubRoutes from './my-pts-hub.routes';
import adminRoutes from './admin.routes';
import adminNotificationRoutes from './admin-notification.routes';
import adminUserRoutes from './admin-user.routes';
import adminSubscriptionRoutes from './admin-subscription.routes';
import subscriptionRoutes from './subscription.routes';
import adminVerificationRoutes from './admin/verification-admin.routes';
import verificationRoutes from './verification.routes';
import adminModuleRoutes from './admin/index';
import stripeRoutes from './stripe.routes';
import taskRoutes from './task.routes';
import profileDataRoutes from './data.routes';
import settingsRoutes from './settings.routes';
import contactSettingsRoutes from './contact-settings.routes';
import connectionSettingsRoutes from './connection-settings.routes';
import affiliationSettingsRoutes from './affiliation-settings.routes';
import listRoutes from './list.routes';
import eventRoutes from './event.routes';
import interactionRoutes from './interaction.routes';
import notificationRoutes from './notification.routes';
import notificationSchedulingRoutes from './notification-scheduling.routes';
import userNotificationPreferencesRoutes from './user-notification-preferences.routes';
import notificationTestRoutes from './notification-test.routes';
import userDeviceRoutes from './user-device.routes';
import profileReferralRoutes from './profile-referral.routes';
import presenceRoutes from './presence.routes';
import gamificationRoutes from './gamification.routes';
import analyticsDashboardRoutes from './analytics-dashboard.routes';
import sessionsRoutes from './sessions.routes';
import messageProfileRoutes from './message-profile.routes';
import scansRoutes from './scans.routes';
import nfcRoutes from './nfc.routes';
import vaultRoutes from './vault.routes';
import profileShareLinkRoutes, { publicRouter as profileShareLinkPublicRoutes, protectedRouter as profileShareLinkProtectedRoutes } from './profile-share-link.routes';
import { protect } from '../middleware/auth.middleware';
import { testRoutes } from './test.routes';
import session from 'express-session';
import passport from 'passport';
import socialAuthRoutes from './auth.social.routes';
import { RelationshipTypeController } from '../controllers/relationshipType.controller';
import participantRoutes from './participant.routes';
import reminderRoutes from './reminder.routes';
import plansRoutes from './plans.routes';
import communityRoutes from './community.routes';
import profileFullRoutes from './profile-full.routes';
import fraudRoutes from './fraud.routes';
import countryRoutes from './country.routes';
import accountBandsRoutes from './account-bands.routes';
import accountsRoutes from './accounts.routes';
import loyaltyRoutes from './loyalty.routes';
import adminCardTemplateRoutes from './admin/card-template.routes';
import userCardRoutes from './user-card.routes';
import shoppingListRoutes from './shopping-list.routes';
import inventoryRoutes from './inventory.routes';
import telnyxWebhookRoutes from './telnyx-webhooks';
import availabilityRoutes from './availability.routes';
import contentLogoImagesRoutes from './content-logo-images.routes';
import profileTemplateRoutes from './profile-template.routes';
import ticketRoutes from './ticket.routes';
import qualityValidationRoutes from './quality-validation.routes';
import userProfileDetailsRoutes from './user-profile-details.routes';
import rbacRoutes from './admin/rbac.routes';
import { createDynamicRBACMiddleware, getDynamicRBACManager } from '../middleware/dynamic-rbac.middleware';
import { RBAC_ROUTE_CONFIG } from '../config/rbac-routes.config';
import globalSearchRoutes from './global-search.routes';

// Source routes
import adminSourceRoutes from './admin/source.routes';
import userSourceRoutes from './user/source.routes';

// Product and merchandise routes
import merchandiseRoutes from './merchandiseRoutes';
import categoryRoutes from './categoryRoutes';
import categoryVariantsRoutes from './category-variants.routes';
import productVariantRoutes from './productVariantRoutes';
import materialRoutes from './materialRoutes';
import productConfigurationRoutes from './productConfiguration.routes';
import qrcodeRoutes from './qrcode.routes';
import inUseProductRoutes from './inUseProductRoutes';
import orderRoutes from './orderRoutes';
import folderRoutes from './folderRoutes';
import manufacturerRoutes from './manufacturerRoutes';
import manufacturingOrderRoutes from './manufacturingOrderRoutes';
import productPoolRoutes from './productPoolRoutes';
import shippingConfigRoutes from './shippingConfig.routes';
import enhancedOrderRoutes from './orders.routes';
import shippingServiceRoutes from './shipping.routes';
import shippingWebhookRoutes from './webhooks/shipping.webhooks.routes';
import adminShippingRoutes from './admin-shipping.routes';
import productReviewRoutes from './productReview.routes';
import leaderboardRoutes from './leaderboard.routes';
import milestoneRoutes from './milestone.routes';
import badgeRoutes from './badge.routes';
import rewardActivityRoutes from './reward-activity.routes';
import securityEventsRoutes from './security-events.routes';
import bonusRoutes from './bonus.routes';

// Profile relationship routes
import profileRelationshipRoutes from './profile-relationship.routes';
import rateReviewRoutes from './rate-review.routes';
import subscriptionPlansRoutes from './subscription-plans.routes';

// Exchange routes
import exchangeRoutes from './exchange.routes';

import helpCenterRoutes from './help-center.routes';
import helpCenterAdminRoutes from './admin/help-center-admin.routes';

/**
 * Configures and sets up all API routes for the application
 * @param app Express application instance
 * @description Initializes routes with their respective middleware chains
 */
export const setupRoutes = (app: Application): void => {
  // License validation removed

  // Initialize Dynamic RBAC Middleware
  const dynamicRBAC = createDynamicRBACMiddleware(RBAC_ROUTE_CONFIG);

  // Root route - serve landing page
  app.get('/', (req, res) => {
    res.sendFile('index.html', { root: 'public' });
  });

  //auth test
  app.get('/socials', (req, res) => {
    res.sendFile('authtest.html', { root: 'public' });
  });

  // Admin logs page
  app.get('/admin/logs', (req, res) => {
    res.sendFile('logs.html', { root: 'public' });
  });

  app.use(
    session({
      secret: process.env.COOKIE_SECRET || "some secret, here top secret",
      resave: false,
      saveUninitialized: false, // Changed to false to prevent creating empty sessions
      cookie: {
        secure: process.env.NODE_ENV === "production", // Only send cookie over HTTPS in production
        httpOnly: true, // Prevents JavaScript from reading the cookie
        sameSite: "lax", // Helps prevent CSRF attacks
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        path: "/",
      }
    })
  );

  app.use(passport.initialize());
  app.use(passport.session());

  // Direct route for Google OAuth callback to match what's configured in Google Developer Console
  // Import fraud detection middleware
  const {
    fraudDetectionMiddleware,
    deviceFingerprintMiddleware,
    suspiciousActivityLogger
  } = require('../middleware/fraudDetection.middleware');

  app.get('/api/auth/google/callback',
    deviceFingerprintMiddleware(),
    fraudDetectionMiddleware({
      blockOnCritical: true,
      requireVerificationOnHigh: false, // Social auth users are already verified by provider
      logAllAttempts: true,
      customThresholds: {
        block: 100,   // Block immediately if device already registered (score = 100)
        flag: 80,     // Flag if risk score >= 80
        verify: 60,   // Require verification if risk score >= 60
      }
    }),
    suspiciousActivityLogger(),
    async (req, res, next) => {
      console.log('Received Google callback at /api/auth/google/callback');

      // Check if fraud detection blocked the request
      if (req.fraudDetection && req.fraudDetection.shouldBlock) {
        const { logger } = require('../utils/logger');
        logger.warn('Google OAuth callback blocked due to fraud detection', {
          riskScore: req.fraudDetection.riskScore,
          flags: req.fraudDetection.flags,
        });

        // Get frontend URL from environment or default
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3001';
        const blockedUrl = `${frontendUrl}/auth/blocked?provider=google&reason=fraud_detection&riskScore=${req.fraudDetection.riskScore}&flags=${req.fraudDetection.flags.join(',')}`;

        logger.info('Redirecting blocked OAuth to frontend', { blockedUrl });
        return res.redirect(blockedUrl);
      }

      // Import the controller
      const { SocialAuthController } = require('../controllers/auth.social.controller');
      // Call the controller method directly
      await SocialAuthController.googleCallback(req, res, next);
    }
  );

  // Public routes (no RBAC protection needed)
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/auth/social', socialAuthRoutes);
  app.use('/api/profile-full', profileFullRoutes);
  app.use('/api/countries', countryRoutes);

  // Help Center routes
  app.use('/api/help', helpCenterRoutes);
  app.use('/api/admin/help', helpCenterAdminRoutes);

  // Profile share link routes - public routes for accessing shared profiles
  app.use('/api/profile-share-links', profileShareLinkPublicRoutes);

  // Public relationship type viewing route
  app.get('/api/allrelationship-types', RelationshipTypeController.getAllRelationshipTypes);

  // Public subscription plans routes
  app.use('/api/subscription-plans', subscriptionPlansRoutes);

  // Protected settings routes with Dynamic RBAC
  app.use('/api/settings', protect, dynamicRBAC, settingsRoutes);
  app.use('/api/contact-settings', protect, dynamicRBAC, contactSettingsRoutes);
  app.use('/api/connection-settings', protect, dynamicRBAC, connectionSettingsRoutes);
  app.use('/api/affiliation-settings', protect, dynamicRBAC, affiliationSettingsRoutes);

  // Global Search API - comprehensive search across all entities
  app.use('/api/search', globalSearchRoutes);

  // profile data routes - NOW PROTECTED
  app.use('/api/p/data', protect, dynamicRBAC, profileDataRoutes);

  // Protected routes with Dynamic RBAC
  app.use('/api/profiles', protect, dynamicRBAC, profileRoutes);
  app.use('/api/profiles', protect, dynamicRBAC, scansRoutes);
  app.use('/api/nfc', protect, dynamicRBAC, nfcRoutes);
  app.use('/api/vault', protect, dynamicRBAC, vaultRoutes);
  app.use('/api/profile-share-links', protect, dynamicRBAC, profileShareLinkProtectedRoutes);
  app.use('/api/p/connections', protect, dynamicRBAC, profileConnectionRoutes);
  app.use('/api/p/connections/analytics', protect, dynamicRBAC, connectionAnalyticsRoutes);
  app.use('/api/availability', protect, dynamicRBAC, availabilityRoutes); // NOW PROTECTED
  app.use('/api/contacts', protect, dynamicRBAC, contactRoutes);
  app.use('/api/locations', protect, dynamicRBAC, locationRoutes);
  app.use('/api/panic', protect, dynamicRBAC, panicRoutes); // NOW PROTECTED

  app.use('/api/admin/cards/templates', protect, dynamicRBAC, adminCardTemplateRoutes);
  app.use('/api/user-cards', protect, dynamicRBAC, userCardRoutes);
  app.use('/api/shopping-list', protect, dynamicRBAC, shoppingListRoutes);
  app.use('/api/inventory', protect, dynamicRBAC, inventoryRoutes);
  app.use('/api/tasks', protect, dynamicRBAC, taskRoutes);
  app.use('/api/lists', protect, dynamicRBAC, listRoutes);
  app.use('/api/events', protect, dynamicRBAC, eventRoutes);
  app.use('/api/interactions', protect, dynamicRBAC, interactionRoutes);
  app.use('/api/plans', protect, dynamicRBAC, plansRoutes);
  app.use('/api/relationship-types', protect, dynamicRBAC, RelationshipTypeRoutes);
  app.use('/api/relationship', protect, dynamicRBAC, relationshipRoutes);
  app.use('/api/profile-relationships', protect, dynamicRBAC, profileRelationshipRoutes);
  app.use('/api/exchange', protect, dynamicRBAC, exchangeRoutes);
  app.use('/api/logs', protect, dynamicRBAC, logsRoutes);
  app.use('/api/activity-logs', protect, dynamicRBAC, activityLogsRoutes);
  app.use('/api/my-pts', protect, dynamicRBAC, myPtsRoutes);
  app.use('/api/my-pts-value', protect, dynamicRBAC, myPtsValueRoutes);
  app.use('/api/my-pts-hub', protect, dynamicRBAC, myPtsHubRoutes);
  app.use('/api/admin', protect, dynamicRBAC, adminRoutes);
  app.use('/api/admin/notifications', protect, dynamicRBAC, adminNotificationRoutes);
  app.use('/api/admin/users', protect, dynamicRBAC, adminUserRoutes);
  app.use('/api/admin/subscriptions', protect, dynamicRBAC, adminSubscriptionRoutes);
  app.use('/api/admin/sources', protect, dynamicRBAC, adminSourceRoutes);
  app.use('/api/subscriptions', subscriptionRoutes);
  app.use('/api/admin/verification', protect, dynamicRBAC, adminVerificationRoutes);
  app.use('/api/verification', protect, dynamicRBAC, verificationRoutes); // NOW PROTECTED
  app.use('/api/admin', protect, dynamicRBAC, adminModuleRoutes);
  app.use('/api/admin/rbac', protect, dynamicRBAC, rbacRoutes); // NOW HAS FULL RBAC PROTECTION
  app.use('/api/fraud', protect, dynamicRBAC, fraudRoutes);
  app.use('/api/security-events', protect, dynamicRBAC, securityEventsRoutes);

  // Public webhook routes (must remain public for external services)
  app.use('/api/stripe', stripeRoutes); // Stripe webhooks need to be public
  app.use('/api/webhooks', telnyxWebhookRoutes); // Telnyx webhooks need to be public
  app.use('/api/webhooks/shipping', shippingWebhookRoutes); // Shipping carrier webhooks

  app.use('/api/notifications', protect, dynamicRBAC, notificationRoutes);
  app.use('/api/notifications/scheduling', protect, dynamicRBAC, notificationSchedulingRoutes);
  app.use('/api/user/notification-preferences', protect, dynamicRBAC, userNotificationPreferencesRoutes);
  app.use('/api/user/devices', protect, dynamicRBAC, userDeviceRoutes);
  app.use('/api/user/sources', protect, dynamicRBAC, userSourceRoutes);
  app.use('/api/test/notifications', protect, dynamicRBAC, notificationTestRoutes);
  app.use('/api/referrals', protect, dynamicRBAC, profileReferralRoutes); // NOW PROTECTED
  app.use('/api/presence', protect, dynamicRBAC, presenceRoutes);
  app.use('/api/community', protect, dynamicRBAC, communityRoutes);

  // Additional routes related to plans - NOW PROTECTED
  app.use('/api/participant', protect, dynamicRBAC, participantRoutes); // NOW PROTECTED
  app.use('/api/reminders', protect, dynamicRBAC, reminderRoutes); // NOW PROTECTED

  app.use('/api/gamification', protect, dynamicRBAC, gamificationRoutes);
  app.use('/api/leaderboard', protect, dynamicRBAC, leaderboardRoutes);
  app.use('/api/milestones', protect, dynamicRBAC, milestoneRoutes);
  app.use('/api/badges', protect, dynamicRBAC, badgeRoutes);
  app.use('/api/activities', protect, dynamicRBAC, rewardActivityRoutes);
  app.use('/api/bonuses', protect, dynamicRBAC, bonusRoutes);
  app.use('/api/analytics', protect, dynamicRBAC, analyticsDashboardRoutes);
  app.use('/api/sessions', protect, dynamicRBAC, sessionsRoutes);
  app.use('/api/message-profile', protect, dynamicRBAC, messageProfileRoutes);
  app.use('/api/account-bands', protect, dynamicRBAC, accountBandsRoutes); // NOW PROTECTED
  app.use('/api/accounts', protect, dynamicRBAC, accountsRoutes);
  app.use('/api/loyalty', protect, dynamicRBAC, loyaltyRoutes);

  // Content logo images routes - NOW PROTECTED (some routes may be public within the router itself)
  app.use('/api/content-logo-images', protect, dynamicRBAC, contentLogoImagesRoutes); // NOW PROTECTED

  // Profile template management routes (admin only)
  app.use('/api/admin/profile-templates', protect, dynamicRBAC, profileTemplateRoutes);

  // PUBLIC product browsing routes (no auth required for better UX)
  app.use('/api/categories', categoryRoutes);
  app.use('/api/product-variants', productVariantRoutes);
  app.use('/api/materials', materialRoutes);
  app.use('/api/product-pools', productPoolRoutes);
  app.use('/api/manufacturers', manufacturerRoutes); // Make manufacturers public for admin interface

  // Product review routes (public for reading, protected for writing)
  app.use('/api/product-reviews', productReviewRoutes);

  // Rate & Review routes (protected for all operations)
  app.use('/api/reviews', protect, dynamicRBAC, rateReviewRoutes);

  // PROTECTED category variants routes (auth required for admin functions)
  app.use('/api', protect, dynamicRBAC, categoryVariantsRoutes);

  // PROTECTED product management routes (auth required)
  app.use('/api/manufacturing-orders', protect,  manufacturingOrderRoutes);
  app.use('/api/merchandise', protect, dynamicRBAC, merchandiseRoutes);
  app.use('/api/product-configuration', protect, dynamicRBAC, productConfigurationRoutes);

  // Shipping configuration routes
  app.use('/api', protect, dynamicRBAC, shippingConfigRoutes);

  // Enhanced order management and shipping service routes
  app.use('/api/orders', protect, dynamicRBAC, enhancedOrderRoutes);
  app.use('/api/shipping', protect, dynamicRBAC, shippingServiceRoutes);
  // Admin shipping management routes
  app.use('/api/admin/shipping', adminShippingRoutes);

  // Quality validation routes
  app.use('/api/quality', protect, dynamicRBAC, qualityValidationRoutes);

  // Profile Share Link routes - Public and Protected
  app.use('/api/profile-share-link', protect, dynamicRBAC, profileShareLinkProtectedRoutes); // Protected routes

  app.use('/api/qrcodes', protect, dynamicRBAC, qrcodeRoutes);
  app.use('/api/in-use-products', protect, dynamicRBAC, inUseProductRoutes);
  app.use('/api/orders', protect, dynamicRBAC, orderRoutes);
  app.use('/api/folders', protect, dynamicRBAC, folderRoutes);

  // Ticket management routes
  app.use('/api/tickets', ticketRoutes);

  // User Profile Details routes
  app.use('/api/user-profile-details', userProfileDetailsRoutes);

  // Help Center routes
  // app.use('/api/help', helpCenterRoutes);
  // app.use('/api/admin/help', helpCenterAdminRoutes);

  // RBAC Configuration Management Endpoint
  app.get('/api/admin/rbac/config/summary', protect, async (req, res) => {
    try {
      const manager = getDynamicRBACManager();
      const summary = await manager.getConfigurationSummary();
      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to get RBAC configuration summary'
      });
    }
  });

  app.post('/api/admin/rbac/config/refresh', protect, async (req, res) => {
    try {
      const manager = getDynamicRBACManager();
      await manager.refreshConfiguration();
      res.json({
        success: true,
        message: 'RBAC configuration refreshed successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to refresh RBAC configuration'
      });
    }
  });

  // Test email route - NOW PROTECTED (admin only)
  app.get('/api/test/email', protect, dynamicRBAC, async (req, res) => {
    try {
      const EmailService = require('../services/email.service').default;
      const testEmail = req.query.email as string || '<EMAIL>';

      // Send a test verification email
      await EmailService.sendVerificationEmail(
        testEmail,
        '123456',
        { ipAddress: req.ip, userAgent: req.headers['user-agent'] }
      );

      res.status(200).json({
        success: true,
        message: `Test email sent to ${testEmail}`,
        details: {
          recipient: testEmail,
          code: '123456',
          sentAt: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Test email error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send test email',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Test routes for advanced tracking (development only) - NOW PROTECTED
  // Use a more specific prefix to avoid intercepting other API routes
  if (process.env.NODE_ENV !== 'production') {
    app.use('/api/test', protect, dynamicRBAC, testRoutes); // NOW PROTECTED
  }

  // Health check endpoint
  app.get('/api/health', (_req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
  });

  // Twilio voice webhook (returns empty response to disable voice functionality)
  app.post('/twilio/voice', (req, res) => {
    res.type('text/xml');
    res.send('<?xml version="1.0" encoding="UTF-8"?><Response></Response>');
  });

  // Register additional routes here
};
