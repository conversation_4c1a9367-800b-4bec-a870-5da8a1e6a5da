import { Router } from 'express';
import { AdminSourceController } from '../../controllers/admin/source.controller';
import { RoleType } from '../../models/Role';
import { protect } from '../../middleware';
import { requireRole } from '../../middleware/roleMiddleware';

const router = Router();

// Apply authentication and admin role middleware to all routes
router.use(protect);
router.use(requireRole([RoleType.ADMIN_USER, RoleType.MAJOR_ADMIN, RoleType.SUPRA_ADMIN, RoleType.SUPER_ADMIN]));

/**
 * @route   GET /api/admin/sources
 * @desc    Get all sources with pagination and filtering
 * @access  Admin only
 */
router.get('/', AdminSourceController.getAllSources);

/**
 * @route   GET /api/admin/sources/stats
 * @desc    Get source statistics
 * @access  Admin only
 */
router.get('/stats', AdminSourceController.getSourceStats);

/**
 * @route   GET /api/admin/sources/options
 * @desc    Get available source types and categories
 * @access  Admin only
 */
router.get('/options', AdminSourceController.getSourceOptions);

/**
 * @route   GET /api/admin/sources/:id
 * @desc    Get a single source by ID
 * @access  Admin only
 */
router.get('/:id', AdminSourceController.getSourceById);

/**
 * @route   POST /api/admin/sources
 * @desc    Create a new source
 * @access  Admin only
 */
router.post('/', AdminSourceController.createSource);

/**
 * @route   PUT /api/admin/sources/:id
 * @desc    Update a source
 * @access  Admin only
 */
router.put('/:id', AdminSourceController.updateSource);

/**
 * @route   PATCH /api/admin/sources/:id/status
 * @desc    Toggle source status (active/inactive, enabled/disabled)
 * @access  Admin only
 */
router.patch('/:id/status', AdminSourceController.toggleSourceStatus);

/**
 * @route   DELETE /api/admin/sources/:id
 * @desc    Delete a source
 * @access  Admin only
 */
router.delete('/:id', AdminSourceController.deleteSource);

export default router; 