import { Router } from 'express';
import { AccountController } from '../controllers/account.controller';
import { protect } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/roleMiddleware';
import { RoleType } from '../models/Role';

const router = Router();

// Apply authentication middleware to all routes
router.use(protect);

/**
 * @route POST /api/accounts
 * @desc Create a new account (Primary or Secondary)
 * @access Authenticated users
 * @body {
 *   "fullName": "John <PERSON>",
 *   "gender": "Male",
 *   "dateOfBirth": "1999-10-09",
 *   "email": "<EMAIL>",
 *   "phoneNumber": "+**********",
 *   "address": "1553 Park Forest Dr, TX",
 *   "country": "United States",
 *   "subscription": "Free",
 *   "dataSize": "1GB",
 *   "accountType": "primary"
 * }
 */
router.post('/', AccountController.createAccount);

/**
 * @route GET /api/accounts
 * @desc Get all accounts linked to a user
 * @access Admin only
 * @query {string} userId - User ID (optional, defaults to current user)
 * @query {number} limit - Items per page (default: 10)
 * @query {number} page - Page number (default: 1)
 * @query {string} filter - Filter by status (e.g., "active")
 */
router.get('/', requireRole([RoleType.ADMIN_USER, RoleType.REGULAR_USER]), AccountController.getAccounts);

/**
 * @route GET /api/accounts/manage
 * @desc Get management options for current user
 * @access Admin only
 */
router.get('/manage', requireRole([RoleType.ADMIN_USER, RoleType.REGULAR_USER]), AccountController.getManagementOptions);

/**
 * @route POST /api/accounts/export
 * @desc Export accounts to file or email
 * @access Admin only
 * @body {
 *   "format": "csv",
 *   "emailToSend": "<EMAIL>"
 * }
 */
router.post('/export', requireRole([RoleType.ADMIN_USER, RoleType.REGULAR_USER]), AccountController.exportAccounts);

/**
 * @route GET /api/accounts/:accountId
 * @desc Get detailed account information by ID
 * @access Authenticated users (own account) or Admin (any account)
 * @param {string} accountId - Account ID
 */
router.get('/:accountId', AccountController.getAccountById);

/**
 * @route PUT /api/accounts/:accountId
 * @desc Update account information
 * @access Authenticated users (own account) or Admin (any account)
 * @param {string} accountId - Account ID
 * @body {
 *   "fullName": "John H Doe (Brother)",
 *   "gender": "Male",
 *   "dateOfBirth": "2002-01-01",
 *   "email": "<EMAIL>",
 *   "phoneNumber": "+***********",
 *   "address": "New Address, TX",
 *   "subscription": "Free",
 *   "dataSize": "2GB"
 * }
 */
router.put('/:accountId', AccountController.updateAccount);

/**
 * @route PATCH /api/accounts/:accountId/deactivate
 * @desc Temporarily deactivate an account
 * @access Admin only
 * @param {string} accountId - Account ID
 * @body {
 *   "reason": "User request"
 * }
 */
router.patch('/:accountId/deactivate', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER, RoleType.REGULAR_USER]), AccountController.deactivateAccount);

/**
 * @route DELETE /api/accounts/:accountId
 * @desc Permanently close an account
 * @access Admin only
 * @param {string} accountId - Account ID
 * @body {
 *   "reason": "User request"
 * }
 */
router.delete('/:accountId', requireRole([RoleType.ADMIN_USER, RoleType.DEVELOPER, RoleType.REGULAR_USER]), AccountController.closeAccount);

/**
 * @route GET /api/accounts/transfer/logs
 * @desc Get transfer logs with icons
 * @access Authenticated users
 * @query {string} type - Filter by type: 'account' | 'profile'
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 20)
 * @query {string} userId - Filter by user ID
 */
router.get('/transfer/logs', AccountController.getTransferLogs);

/**
 * @route GET /api/accounts/transfer/stats
 * @desc Get transfer statistics
 * @access Authenticated users
 * @query {string} userId - Filter by user ID (optional)
 */
router.get('/transfer/stats', AccountController.getTransferStats);

/**
 * @route POST /api/accounts/:accountId/transfer
 * @desc Transfer account ownership (secondary accounts only)
 * @access Authenticated users
 * @param {string} accountId - Account ID
 * @body {
 *   "toUserId": "60d5ecb54b24c0001f5f3e8d",
 *   "reason": "Transfer to family member"
 * }
 */
router.post('/:accountId/transfer', AccountController.transferAccountOwnership);

/**
 * @route POST /api/accounts/:accountId/upgrade-subscription
 * @desc Upgrade subscription
 * @access Authenticated users (own account) or Admin (any account)
 * @param {string} accountId - Account ID
 * @body {
 *   "plan": "Pro",
 *   "paymentMethod": "stripe",
 *   "amount": 9.99
 * }
 */
router.post('/:accountId/upgrade-subscription', AccountController.upgradeSubscription);

/**
 * @route GET /api/accounts/:accountId/activities
 * @desc Fetch account activities
 * @access Admin only
 * @param {string} accountId - Account ID
 * @query {string} filter - Filter activities (recent|old|all)
 */
router.get('/:accountId/activities', requireRole([RoleType.ADMIN_USER, RoleType.DEVELOPER,RoleType.REGULAR_USER]), AccountController.getAccountActivities);

/**
 * @route POST /api/accounts/:accountId/activities
 * @desc Log a new account activity
 * @access Admin only
 * @param {string} accountId - Account ID
 * @body {
 *   "activity": "Updated profile",
 *   "timestamp": "2025-07-27T14:00:00Z"
 * }
 */
router.post('/:accountId/activities', requireRole([RoleType.ADMIN_USER, RoleType.DEVELOPER,RoleType.REGULAR_USER]), AccountController.logAccountActivity);

/**
 * @route GET /api/accounts/:accountId/options
 * @desc Get all available account-specific actions
 * @access Authenticated users (own account) or Admin (any account)
 * @param {string} accountId - Account ID
 */
router.get('/:accountId/options', AccountController.getAccountOptions);

export default router; 