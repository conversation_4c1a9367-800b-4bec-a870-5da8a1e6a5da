import express from 'express';
import {
  getAvailablePlans,
  getMySubscription,
  subscribeToPlan,
  upgradeSubscription,
  cancelSubscription,
  getUsageStats,
  getUpgradeRecommendations,
  checkActionAllowed,
  updateUsageStats,
  processWebhook,
  testWebhook,
  initializeDefaultPlans,
  getSubscriptionHistory,
  getBillingInfo,
  updateBillingPreferences,
  // Admin controllers
  createPlan,
  updatePlan,
  deletePlan,
  getAllPlans,
  getPlanById,
  togglePlanStatus,
  getPlanUsageStats,
  duplicatePlan
} from '../controllers/subscription.controller';
import { authenticateToken } from '../middleware/authMiddleware';
import { requireRole } from '../middleware/rbac.middleware';
import { RoleType } from '../models/Role';

const router = express.Router();

// Public routes (no authentication required)
router.get('/plans', getAvailablePlans);
router.post('/webhook/:provider', processWebhook);
router.post('/test-webhook', testWebhook);

// Private routes (authentication required)
router.use(authenticateToken);

// User subscription management
router.get('/my-subscription', getMySubscription);
router.post('/subscribe', subscribeToPlan);
router.put('/upgrade', upgradeSubscription);
router.delete('/cancel', cancelSubscription);
router.get('/usage', getUsageStats);
router.get('/upgrade-recommendations', getUpgradeRecommendations);
router.post('/check-action', checkActionAllowed);
router.post('/update-usage', updateUsageStats);
router.get('/history', getSubscriptionHistory);
router.get('/billing', getBillingInfo);
router.put('/billing-preferences', updateBillingPreferences);

// Admin routes (admin role required)
router.post('/initialize-plans', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), initializeDefaultPlans);

// Admin Plan Management Routes
router.post('/admin/plans', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), createPlan);
router.get('/admin/plans', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), getAllPlans);
router.get('/admin/plans/:planId', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), getPlanById);
router.put('/admin/plans/:planId', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), updatePlan);
router.delete('/admin/plans/:planId', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), deletePlan);
router.patch('/admin/plans/:planId/toggle', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), togglePlanStatus);
router.get('/admin/plans/:planId/stats', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), getPlanUsageStats);
router.post('/admin/plans/:planId/duplicate', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), duplicatePlan);

export default router;  