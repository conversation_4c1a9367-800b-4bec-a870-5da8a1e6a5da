import { Router } from 'express';
import { RateReviewController } from '../controllers/rate-review.controller';
import { protect } from '../middleware/auth.middleware';

import { requireRole } from '../middleware/roleMiddleware';
import { RoleType } from '../models/Role';

const router = Router();

// Apply authentication middleware to all routes
router.use(protect);

/**
 * @route POST /api/reviews
 * @desc Create a new review
 * @access Authenticated users
 */
router.post('/', RateReviewController.createReview);

/**
 * @route GET /api/reviews
 * @desc Get all reviews with filtering and pagination
 * @access Authenticated users
 */
router.get('/', RateReviewController.getReviews);

/**
 * @route GET /api/reviews/:reviewId
 * @desc Get a specific review by ID
 * @access Authenticated users
 */
router.get('/:reviewId', RateReviewController.getReviewById);

/**
 * @route PUT /api/reviews/:reviewId
 * @desc Update a review (only by the review author)
 * @access Authenticated users (review author only)
 */
router.put('/:reviewId', RateReviewController.updateReview);

/**
 * @route DELETE /api/reviews/:reviewId
 * @desc Delete a review (only by the review author)
 * @access Authenticated users (review author only)
 */
router.delete('/:reviewId', RateReviewController.deleteReview);

/**
 * @route GET /api/reviews/stats
 * @desc Get review statistics
 * @access Authenticated users
 */
router.get('/stats', RateReviewController.getReviewStats);

/**
 * @route GET /api/reviews/user/me
 * @desc Get current user's reviews
 * @access Authenticated users
 */
router.get('/user/me', RateReviewController.getUserReviews);

/**
 * @route GET /api/reviews/admin/all
 * @desc Get all reviews from all users (admin only)
 * @access Admin users only
 */
router.get('/admin/all', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), RateReviewController.getAllReviewsForAdmin);

/**
 * @route POST /api/reviews/:reviewId/report
 * @desc Report a review
 * @access Authenticated users
 */
// router.post('/:reviewId/report', RateReviewController.reportReview);

// Admin-only routes
/**
 * @route GET /api/reviews/admin/pending
 * @desc Get pending reviews for moderation
 * @access Admin only
 */
// router.get('/admin/pending', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), RateReviewController.getPendingReviews);

/**
 * @route PATCH /api/reviews/admin/:reviewId/moderate
 * @desc Moderate a review
 * @access Admin only
 */
// router.patch('/admin/:reviewId/moderate', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), RateReviewController.moderateReview);

/**
 * @route POST /api/reviews/admin/bulk-moderate
 * @desc Bulk moderate reviews
 * @access Admin only
 */
// router.post('/admin/bulk-moderate', requireRole([RoleType.ADMIN_USER, RoleType.SUPER_ADMIN, RoleType.DEVELOPER]), RateReviewController.bulkModerateReviews);

export default router; 