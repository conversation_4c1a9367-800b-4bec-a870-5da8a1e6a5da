import { Router } from 'express';
import { UserSourceController } from '../../controllers/user/source.controller';
import { protect } from '../../middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(protect);    

/**
 * @route   GET /api/user/sources
 * @desc    Get all available sources for users
 * @access  Authenticated users
 */
router.get('/', UserSourceController.getAvailableSources);

/**
 * @route   GET /api/user/sources/featured
 * @desc    Get featured/popular sources
 * @access  Authenticated users
 */
router.get('/featured', UserSourceController.getFeaturedSources);

/**
 * @route   GET /api/user/sources/categories
 * @desc    Get available categories
 * @access  Authenticated users
 */
router.get('/categories', UserSourceController.getAvailableCategories);

/**
 * @route   GET /api/user/sources/types
 * @desc    Get available types
 * @access  Authenticated users
 */
router.get('/types', UserSourceController.getAvailableTypes);

/**
 * @route   GET /api/user/sources/search
 * @desc    Search sources
 * @access  Authenticated users
 */
router.get('/search', UserSourceController.searchSources);

/**
 * @route   GET /api/user/sources/category/:category
 * @desc    Get sources by category
 * @access  Authenticated users
 */
router.get('/category/:category', UserSourceController.getSourcesByCategory);

/**
 * @route   GET /api/user/sources/type/:type
 * @desc    Get sources by type
 * @access  Authenticated users
 */
router.get('/type/:type', UserSourceController.getSourcesByType);

/**
 * @route   GET /api/user/sources/:id
 * @desc    Get source details by ID
 * @access  Authenticated users
 */
router.get('/:id', UserSourceController.getSourceDetails);

/**
 * @route   GET /api/user/sources/:id/setup
 * @desc    Get source setup instructions
 * @access  Authenticated users
 */
router.get('/:id/setup', UserSourceController.getSourceSetupInstructions);

export default router; 