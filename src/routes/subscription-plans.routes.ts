import express from 'express';
import {
    getAllSubscriptionPlans,
    getSubscriptionPlanById,
    getSubscriptionPlanByName,
    getSubscriptionPlanComparison
} from '../controllers/subscription-plans.controller';

const router = express.Router();

// @route   GET /api/subscription-plans
// @desc    Get all subscription plans
// @access  Public
router.get('/', getAllSubscriptionPlans);

// @route   GET /api/subscription-plans/compare
// @desc    Get subscription plan comparison
// @access  Public
router.get('/compare', getSubscriptionPlanComparison);

// @route   GET /api/subscription-plans/name/:name
// @desc    Get subscription plan by name
// @access  Public
router.get('/name/:name', getSubscriptionPlanByName);

// @route   GET /api/subscription-plans/:id
// @desc    Get subscription plan by ID
// @access  Public
router.get('/:id', getSubscriptionPlanById);

export default router; 