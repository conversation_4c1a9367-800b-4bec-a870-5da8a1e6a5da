import express from 'express';
import { <PERSON><PERSON><PERSON>roller } from '../controllers/exchange.controller';
import { authenticateToken } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';

const router = express.Router();

/**
 * Exchange Routes
 * 
 * All routes require JWT authentication
 * Base path: /api/exchange
 */

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Apply rate limiting to sensitive endpoints
router.use('/initiate', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: 'Too many exchange initiation attempts, please try again later.'
}));

/**
 * @route POST /api/exchange/initiate
 * @desc Initiate an exchange with another profile
 * @access Private
 */
router.post('/initiate', ExchangeController.initiateExchange);

/**
 * @route GET /api/exchange/history
 * @desc Get exchange history for a profile
 * @access Private
 */
router.get('/history', ExchangeController.getExchangeHistory);

/**
 * @route GET /api/exchange/:exchangeId
 * @desc Get detailed info for a specific exchange
 * @access Private
 */
router.get('/:exchangeId', ExchangeController.getExchangeDetails);

/**
 * @route POST /api/exchange/:exchangeId/complete
 * @desc Complete an exchange (accept)
 * @access Private
 */
router.post('/:exchangeId/complete', ExchangeController.completeExchange);

/**
 * @route POST /api/exchange/:exchangeId/decline
 * @desc Decline an exchange
 * @access Private
 */
router.post('/:exchangeId/decline', ExchangeController.declineExchange);

/**
 * @route DELETE /api/exchange/:exchangeId
 * @desc Cancel/delete an exchange
 * @access Private
 */
router.delete('/:exchangeId', ExchangeController.cancelExchange);

export default router; 