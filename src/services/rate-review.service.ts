import Review, { IReview } from '../models/Review';
import { User } from '../models/User';
import { logger } from '../utils/logger';
import createHttpError from 'http-errors';
import mongoose from 'mongoose';

export interface IReviewData {
  rating: number;
  subject?: string;
  reason?: string;
  feedback: string;
  improvementCategories?: Array<{
    category: 'overall_service' | 'customer_support' | 'speed_efficiency' | 'profile_setup_experience' | 'ease_of_use_navigation' | 'other';
  }>;
}

export interface IReviewResponse {
  _id: string;
  userId: string;
  rating: number;
  subject?: string;
  reason?: string;
  feedback: string;
  improvementCategories: Array<{
    category: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface IReviewStats {
  averageRating: number;
  totalReviews: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface IReviewFilters {
  rating?: number;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'rating' | 'recent' | 'oldest';
  sortOrder?: 'asc' | 'desc';
}

export class RateReviewService {
  /**
   * Create a new review
   */
  static async createReview(data: IReviewData, userId: string): Promise<IReviewResponse> {
    try {
      // Validate rating
      if (data.rating < 1 || data.rating > 5) {
        throw createHttpError(400, 'Rating must be between 1 and 5');
      }

      // Create review
      const reviewData = {
        userId: new mongoose.Types.ObjectId(userId),
        rating: data.rating,
        subject: data.subject,
        reason: data.reason,
        feedback: data.feedback,
        improvementCategories: data.improvementCategories || []
      };

      const review = new Review(reviewData);
      await review.save();

      logger.info('Review created successfully', {
        reviewId: review._id,
        userId
      });

      return this.formatReviewResponse(review);
    } catch (error) {
      logger.error('Error creating review:', error);
      throw error;
    }
  }

  /**
   * Get reviews with filtering and pagination
   */
  static async getReviews(
    filters: IReviewFilters = {},
    options: { page?: number; limit?: number } = {}
  ): Promise<{ reviews: IReviewResponse[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 10 } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};

      if (filters.rating) {
        query.rating = filters.rating;
      }

      if (filters.dateRange) {
        query.createdAt = {
          $gte: filters.dateRange.start,
          $lte: filters.dateRange.end
        };
      }

      // Build sort
      let sort: any = { createdAt: -1 }; // Default sort by recent

      if (filters.sortBy) {
        switch (filters.sortBy) {
          case 'rating':
            sort = { rating: filters.sortOrder === 'asc' ? 1 : -1 };
            break;
          case 'recent':
            sort = { createdAt: -1 };
            break;
          case 'oldest':
            sort = { createdAt: 1 };
            break;
        }
      }

      // Execute query
      const [reviews, total] = await Promise.all([
        Review.find(query)
          .populate('userId', 'fullName username')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Review.countDocuments(query)
      ]);

      const formattedReviews = reviews.map(review => this.formatReviewResponse(review as IReview));

      return {
        reviews: formattedReviews,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error getting reviews:', error);
      throw createHttpError(500, 'Failed to get reviews');
    }
  }

  /**
   * Get a review by ID
   */
  static async getReviewById(reviewId: string): Promise<IReviewResponse> {
    try {
      const review = await Review.findById(reviewId)
        .populate('userId', 'fullName username')
        .lean();

      if (!review) {
        throw createHttpError(404, 'Review not found');
      }

      return this.formatReviewResponse(review as IReview);
    } catch (error) {
      logger.error('Error getting review by ID:', error);
      throw error;
    }
  }

  /**
   * Update a review
   */
  static async updateReview(
    reviewId: string,
    userId: string,
    updates: Partial<IReviewData>
  ): Promise<IReviewResponse> {
    try {
      const review = await Review.findById(reviewId);

      if (!review) {
        throw createHttpError(404, 'Review not found');
      }

      // Check if user owns the review
      if (review.userId.toString() !== userId) {
        throw createHttpError(403, 'You can only update your own reviews');
      }

      // Validate rating if provided
      if (updates.rating && (updates.rating < 1 || updates.rating > 5)) {
        throw createHttpError(400, 'Rating must be between 1 and 5');
      }

      // Update fields
      if (updates.rating !== undefined) review.rating = updates.rating;
      if (updates.subject !== undefined) review.subject = updates.subject;
      if (updates.reason !== undefined) review.reason = updates.reason;
      if (updates.feedback !== undefined) review.feedback = updates.feedback;
      if (updates.improvementCategories !== undefined) {
        review.improvementCategories = updates.improvementCategories;
      }

      await review.save();

      logger.info('Review updated successfully', {
        reviewId,
        userId,
        updatedFields: Object.keys(updates)
      });

      return this.formatReviewResponse(review);
    } catch (error) {
      logger.error('Error updating review:', error);
      throw error;
    }
  }

  /**
   * Delete a review
   */
  static async deleteReview(reviewId: string, userId: string): Promise<{ success: boolean; message: string }> {
    try {
      const review = await Review.findById(reviewId);

      if (!review) {
        throw createHttpError(404, 'Review not found');
      }

      // Check if user owns the review
      if (review.userId.toString() !== userId) {
        throw createHttpError(403, 'You can only delete your own reviews');
      }

      await Review.findByIdAndDelete(reviewId);

      logger.info('Review deleted successfully', {
        reviewId,
        userId
      });

      return {
        success: true,
        message: 'Review deleted successfully'
      };
    } catch (error) {
      logger.error('Error deleting review:', error);
      throw error;
    }
  }

  /**
   * Get review statistics
   */
  static async getReviewStats(): Promise<IReviewStats> {
    try {
      const stats = await (Review as any).getRatingStats();

      if (!stats || stats.length === 0) {
        return {
          averageRating: 0,
          totalReviews: 0,
          distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
      }

      const result = stats[0];
      const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

      // Calculate distribution
      if (result.ratingDistribution) {
        result.ratingDistribution.forEach((rating: number) => {
          distribution[rating as keyof typeof distribution]++;
        });
      }

      return {
        averageRating: Math.round(result.averageRating * 100) / 100,
        totalReviews: result.totalReviews,
        distribution
      };
    } catch (error) {
      logger.error('Error getting review stats:', error);
      throw createHttpError(500, 'Failed to get review statistics');
    }
  }

  /**
   * Get user's reviews
   */
  static async getUserReviews(
    userId: string,
    options: { page?: number; limit?: number } = {}
  ): Promise<{ reviews: IReviewResponse[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 10 } = options;
      const skip = (page - 1) * limit;

      const [reviews, total] = await Promise.all([
        Review.find({ userId: new mongoose.Types.ObjectId(userId) })
          .populate('userId', 'fullName username')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Review.countDocuments({ userId: new mongoose.Types.ObjectId(userId) })
      ]);

      const formattedReviews = reviews.map(review => this.formatReviewResponse(review as IReview));

      return {
        reviews: formattedReviews,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error getting user reviews:', error);
      throw createHttpError(500, 'Failed to get user reviews');
    }
  }

  /**
   * Get all reviews for admin (all users)
   */
  static async getAllReviewsForAdmin(
    filters: IReviewFilters = {},
    options: { page?: number; limit?: number } = {}
  ): Promise<{ reviews: IReviewResponse[]; total: number; page: number; limit: number }> {
    try {
      const { page = 1, limit = 20 } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};

      if (filters.rating) {
        query.rating = filters.rating;
      }

      if (filters.dateRange) {
        query.createdAt = {
          $gte: filters.dateRange.start,
          $lte: filters.dateRange.end
        };
      }

      // Build sort
      let sort: any = { createdAt: -1 }; // Default sort by recent

      if (filters.sortBy) {
        switch (filters.sortBy) {
          case 'rating':
            sort = { rating: filters.sortOrder === 'asc' ? 1 : -1 };
            break;
          case 'recent':
            sort = { createdAt: -1 };
            break;
          case 'oldest':
            sort = { createdAt: 1 };
            break;
        }
      }

      // Execute query with user population
      const [reviews, total] = await Promise.all([
        Review.find(query)
          .populate('userId', 'fullName username email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Review.countDocuments(query)
      ]);

      const formattedReviews = reviews.map(review => this.formatReviewResponse(review as IReview));

      return {
        reviews: formattedReviews,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error getting all reviews for admin:', error);
      throw createHttpError(500, 'Failed to get all reviews');
    }
  }

  /**
   * Format review response
   */
  private static formatReviewResponse(review: IReview): IReviewResponse {
    return {
      _id: (review._id as any).toString(),
      userId: (review.userId as any).toString(),
      rating: review.rating,
      subject: review.subject,
      reason: review.reason,
      feedback: review.feedback,
      improvementCategories: review.improvementCategories,
      createdAt: review.createdAt,
      updatedAt: review.updatedAt
    };
  }
} 