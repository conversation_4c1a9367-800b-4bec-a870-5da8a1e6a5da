import { User } from '../models/User';
import { ProfileModel, ProfileDocument } from '../models/profile.model';
import { TransferLogModel, TransferLogDocument } from '../models/transfer-log.model';
import { logger } from '../utils/logger';
import createHttpError from 'http-errors';
import mongoose from 'mongoose';
import { getClientInfo } from '../utils/controllerUtils';
import { subscriptionService } from './subscription.service';

export interface IAccountData {
  userId: string;
  firstName: string;
  lastName: string;
  username: string;
  gender?: string;
  dob: string;
  accountType: 'primary' | 'secondary';
  address: string;
  country: string;
  profileImageUrl?: string;
}

export interface ISubscriptionData {
  plan: string;
  dataUsage: string;
  dataLimit: string;
  dataRemaining: string;
  creationDate: string;
  referralCode: string;
  uplineAccount?: string;
  downlineReferrals: number;
  billingCycle: string;
  paymentMethod: string;
  autoRenew: boolean;
  nextBillingDate: string;
  trialEndDate?: string;
  invoiceHistory: Array<{
    invoiceId: string;
    amount: string;
    date: string;
    status: string;
    invoiceFileUrl: string;
  }>;
  downlineAccounts: Array<{
    userId: string;
    username: string;
    dateReferred: string;
    profileType: string;
  }>;
}

export interface IProfileData {
  type: string;
  profileId: string;
  status: string;
  lastUpdated: string;
}

export interface ILoginHistory {
  device: string;
  ip: string;
  location: string;
  timestamp: string;
}

export interface IAccountResponse {
  account: IAccountData;
  subscription: ISubscriptionData;
  profiles: IProfileData[];
  loginHistory: ILoginHistory[];
}

export interface ICreateAccountData {
  fullName: string;
  gender?: string;
  dateOfBirth: string;
  email: string;
  phoneNumber: string;
  address: string;
  country: string;
  subscription: string;
  dataSize: string;
  accountType: 'primary' | 'secondary';
}

export interface IUpdateAccountData {
  fullName?: string;
  username?: string;
  gender?: string;
  dateOfBirth?: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  subscription?: string;
  dataSize?: string;
}

export interface ITransferLog {
   type: 'account' | 'profile';
  user: string;
  action: string;
  icon: string;
  date: string;
  myPts: number;
  details?: {
    fromUserId?: string;
    toUserId?: string;
    profileId?: string;
    accountId?: string;
    reason?: string;
  };
}

export interface ITransferLogsResponse {
  logs: ITransferLog[];
  total: number;
  page: number;
  limit: number;
}

export class AccountService {
  /**
   * Create a new account
   */
  static async createAccount(data: ICreateAccountData, userId?: string): Promise<IAccountResponse> {
    try {
      // Generate username from full name
      const username = this.generateUsername(data.fullName);
      
      // Create user document
      const userData: any = {
        email: data.email,
        password: this.generateTemporaryPassword(), // Will be changed by user
        fullName: data.fullName,
        username: username,
        dateOfBirth: new Date(data.dateOfBirth),
        countryOfResidence: data.country,
        phoneNumber: data.phoneNumber,
        accountType: data.accountType === 'primary' ? 'MYSELF' : 'SOMEONE_ELSE',
        accountCategory: data.accountType === 'primary' ? 'PRIMARY_ACCOUNT' : 'SECONDARY_ACCOUNT',
        verificationMethod: 'EMAIL',
        isEmailVerified: false,
        isPhoneVerified: false,
        role: 'user',
        subscription: {
          plan: this.validatePlanType(data.subscription.toLowerCase()),
          features: [],
          limitations: this.getPlanLimitations(data.subscription),
          startDate: new Date(),
          autoRenew: true,
          paymentStatus: 'active'
        }
      };

      // If this is a secondary account, link to primary user
      if (data.accountType === 'secondary' && userId) {
        userData.referredBy = new mongoose.Types.ObjectId(userId);
      }

      const user = new User(userData);
      await user.save();

      logger.info(`Created new ${data.accountType} account`, {
        userId: user._id,
        email: user.email,
        accountType: data.accountType
      });

      return await this.getAccountById(user._id.toString());
    } catch (error) {
      logger.error('Error creating account:', error);
      throw createHttpError(500, 'Failed to create account');
    }
  }

  /**
   * Get all accounts for a user
   */
  static async getAccounts(userId: string, options: {
    limit?: number;
    page?: number;
    filter?: string;
  } = {}): Promise<{ accounts: IAccountResponse[]; total: number; page: number; limit: number }> {
    try {
      const { limit = 10, page = 1, filter } = options;
      const skip = (page - 1) * limit;

      // Build query
      let query: any = {};
      
      if (filter === 'active') {
        query.isBanned = false;
        query.isAccountLocked = false;
      }

      // Get primary account and any secondary accounts
      const primaryUser = await User.findById(userId);
      if (!primaryUser) {
        throw createHttpError(404, 'User not found');
      }

      // Get all accounts (primary + secondary accounts created by this user)
      const accounts = await User.find({
        $or: [
          { _id: userId },
          { referredBy: userId }
        ],
        ...query
      })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

      const total = await User.countDocuments({
        $or: [
          { _id: userId },
          { referredBy: userId }
        ],
        ...query
      });

      // Convert to account responses
      const accountResponses = await Promise.all(
        accounts.map(account => this.getAccountById(account._id.toString()))
      );

      return {
        accounts: accountResponses,
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error getting accounts:', error);
      throw createHttpError(500, 'Failed to get accounts');
    }
  }

  /**
   * Get account by ID (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async getAccountById(accountId: string): Promise<IAccountResponse> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId)
          .populate('profiles')
          .populate('referredBy', 'username email');
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId })
          .populate('profiles')
          .populate('referredBy', 'username email');
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() })
          .populate('profiles')
          .populate('referredBy', 'username email');
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Get profiles
      const profiles = await ProfileModel.find({ creator: user._id })
        .select('profileType secondaryId updatedAt')
        .sort({ updatedAt: -1 });

      // Get login history from sessions
      const loginHistory = this.extractLoginHistory(user.sessions || []);

      // Calculate subscription data
      const subscription = await this.calculateSubscriptionData(user);

      // Build account data
      const account: IAccountData = {
        userId: user.secondaryId || user._id.toString().slice(-8).toUpperCase(),
        firstName: user.firstName || user.fullName.split(' ')[0],
        lastName: user.lastName || user.fullName.split(' ').slice(1).join(' ') || '',
        username: `#${user.username}`,
        gender: this.determineGender(user.fullName),
        dob: user.dateOfBirth ? user.dateOfBirth.toISOString().split('T')[0] : '',
        accountType: user.accountCategory === 'PRIMARY_ACCOUNT' ? 'primary' : 'secondary',
        address: `${user.countryOfResidence || 'Unknown'}`,
        country: user.countryOfResidence || 'Unknown',
        profileImageUrl: user.profileImage
      };

      // Build profiles data
      const profilesData: IProfileData[] = profiles.map((profile: ProfileDocument) => ({
        type: profile.profileType,
        profileId: profile.secondaryId || profile._id.toString(),
        status: 'active', // You might want to add a status field to Profile model
        lastUpdated: profile.profileInformation.updatedAt.toISOString()
      }));

      return {
        account,
        subscription,
        profiles: profilesData,
        loginHistory
      };
    } catch (error) {
      logger.error('Error getting account by ID:', error);
      throw createHttpError(500, 'Failed to get account');
    }
  }

  /**
   * Update account (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async updateAccount(accountId: string, data: IUpdateAccountData): Promise<IAccountResponse> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Update fields
      if (data.fullName) {
        user.fullName = data.fullName;
        // Update firstName and lastName if provided
        const nameParts = data.fullName.split(' ');
        user.firstName = nameParts[0];
        user.lastName = nameParts.slice(1).join(' ');
      }
      
      // Handle username updates with validation
      if (data.username !== undefined) {
        if (typeof data.username !== 'string' || data.username.trim() === '') {
          throw createHttpError(400, 'Username must be a non-empty string');
        }

        // Check if username is already taken by another user
        const existingUser = await User.findOne({ 
          username: data.username.toLowerCase().trim(),
          _id: { $ne: user._id }
        });
        
        if (existingUser) {
          throw createHttpError(400, 'Username is already taken');
        }

        // Update username (convert to lowercase and trim)
        user.username = data.username.toLowerCase().trim();
      }
      
      if (data.dateOfBirth) user.dateOfBirth = new Date(data.dateOfBirth);
      if (data.email) user.email = data.email;
      if (data.phoneNumber) user.phoneNumber = data.phoneNumber;
      if (data.address) user.countryOfResidence = data.address;
      if (data.subscription) {
        user.subscription.plan = this.validatePlanType(data.subscription.toLowerCase());
        user.subscription.limitations = this.getPlanLimitations(data.subscription);
      }

      await user.save();

      logger.info(`Updated account ${accountId}`, {
        updatedFields: Object.keys(data)
      });

      return await this.getAccountById(user._id.toString());
    } catch (error) {
      logger.error('Error updating account:', error);
      throw createHttpError(500, 'Failed to update account');
    }
  }

  /**
   * Deactivate account (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async deactivateAccount(accountId: string, reason: string): Promise<{ success: boolean; message: string }> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Set account as locked (deactivated)
      user.isAccountLocked = true;
      user.lockReason = reason;
      user.lockDate = new Date();

      await user.save();

      logger.info(`Deactivated account ${accountId}`, {
        reason,
        deactivatedAt: new Date()
      });

      return {
        success: true,
        message: 'Account deactivated successfully'
      };
    } catch (error) {
      logger.error('Error deactivating account:', error);
      throw createHttpError(500, 'Failed to deactivate account');
    }
  }

  /**
   * Close account permanently (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async closeAccount(accountId: string, reason: string): Promise<{ success: boolean; message: string }> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Mark as banned (permanently closed)
      user.isBanned = true;
      user.banReason = reason;
      user.banDate = new Date();
      user.bannedAt = new Date();

      await user.save();

      logger.info(`Permanently closed account ${accountId}`, {
        reason,
        closedAt: new Date()
      });

      return {
        success: true,
        message: 'Account closed permanently'
      };
    } catch (error) {
      logger.error('Error closing account:', error);
      throw createHttpError(500, 'Failed to close account');
    }
  }

  /**
   * Upgrade subscription (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async upgradeSubscription(accountId: string, plan: string, paymentMethod: string, amount: number): Promise<{ success: boolean; message: string }> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Update subscription
      user.subscription.plan = this.validatePlanType(plan.toLowerCase());
      user.subscription.limitations = this.getPlanLimitations(plan);
      user.subscription.startDate = new Date();
      user.subscription.paymentStatus = 'active';

      await user.save();

      logger.info(`Upgraded subscription for account ${accountId}`, {
        newPlan: plan,
        amount,
        paymentMethod
      });

      return {
        success: true,
        message: 'Subscription upgraded successfully'
      };
    } catch (error) {
      logger.error('Error upgrading subscription:', error);
      throw createHttpError(500, 'Failed to upgrade subscription');
    }
  }

  /**
   * Get account activities (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async getAccountActivities(accountId: string, filter: 'recent' | 'old' | 'all' = 'all'): Promise<any[]> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // Get login activities from sessions
      const sessions = user.sessions || [];
      let activities = sessions.map(session => ({
        activityType: 'login',
        status: 'success',
        timestamp: session.lastUsed.toISOString()
      }));

      // Apply filter
      if (filter === 'recent') {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        activities = activities.filter(activity => 
          new Date(activity.timestamp) >= thirtyDaysAgo
        );
      } else if (filter === 'old') {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        activities = activities.filter(activity => 
          new Date(activity.timestamp) < thirtyDaysAgo
        );
      }

      return activities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      logger.error('Error getting account activities:', error);
      throw createHttpError(500, 'Failed to get account activities');
    }
  }

  /**
   * Log account activity (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async logAccountActivity(accountId: string, activity: string, timestamp?: string): Promise<{ success: boolean }> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      // For now, we'll just log the activity
      // In a real implementation, you might want to store activities in a separate collection
      logger.info(`Account activity logged`, {
        accountId,
        activity,
        timestamp: timestamp || new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      logger.error('Error logging account activity:', error);
      throw createHttpError(500, 'Failed to log account activity');
    }
  }

  /**
   * Get management options
   */
  static async getManagementOptions(): Promise<string[]> {
    return [
      "Add",
      "Select", 
      "Filter",
      "Export",
      "Display",
      "Account Settings"
    ];
  }

  /**
   * Get account-specific options (supports MongoDB ObjectId, secondaryId, or username)
   */
  static async getAccountOptions(accountId: string): Promise<string[]> {
    try {
      // Validate accountId
      if (!accountId || typeof accountId !== 'string') {
        throw createHttpError(400, 'Invalid account ID provided');
      }

      // Try to find user by different ID types
      let user = null;
      
      // First, try to find by MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(accountId)) {
        user = await User.findById(accountId);
      }
      
      // If not found by ObjectId, try by secondaryId
      if (!user) {
        user = await User.findOne({ secondaryId: accountId });
      }
      
      // If still not found, try by username (only if accountId is a valid string)
      if (!user && accountId && accountId.trim()) {
        user = await User.findOne({ username: accountId.toLowerCase().trim() });
      }

      if (!user) {
        throw createHttpError(404, 'Account not found');
      }

      const options = ["Edit Info", "View Activities"];

      if (!user.isAccountLocked) {
        options.push("Deactivate");
      }

      if (!user.isBanned) {
        options.push("Close");
      }

      return options;
    } catch (error) {
      logger.error('Error getting account options:', error);
      throw createHttpError(500, 'Failed to get account options');
    }
  }

  /**
   * Export accounts
   */
  static async exportAccounts(userId: string, format: 'csv' | 'json', emailToSend?: string): Promise<{ success: boolean; message: string }> {
    try {
      const accounts = await this.getAccounts(userId);
      
      // In a real implementation, you would generate the file and send via email
      logger.info(`Exporting accounts for user ${userId}`, {
        format,
        emailToSend,
        accountCount: accounts.accounts.length
      });

      return {
        success: true,
        message: `Accounts exported successfully in ${format.toUpperCase()} format`
      };
    } catch (error) {
      logger.error('Error exporting accounts:', error);
      throw createHttpError(500, 'Failed to export accounts');
    }
  }

  /**
   * Get transfer logs with icons
   */
  static async getTransferLogs(
    type?: 'account' | 'profile',
    options: {
      page?: number;
      limit?: number;
      userId?: string;
    } = {}
  ): Promise<ITransferLogsResponse> {
    try {
      const { page = 1, limit = 20, userId } = options;

      // Use the TransferLog model to get real data
      const result = await (TransferLogModel as any).getTransferLogs({
        type,
        page,
        limit,
        userId
      });

      // Transform the logs to match the API response format
      const transformedLogs: ITransferLog[] = result.logs.map((log: any) => ({
        type: log.type,
        user: log.toUserName,
        action: log.actionText,
        icon: log.icon,
        date: log.createdAt,
        myPts: log.myPts,
        details: {
          fromUserId: log.fromUserId.toString(),
          toUserId: log.toUserId.toString(),
          profileId: log.details?.profileId?.toString(),
          accountId: log.details?.accountId?.toString(),
          reason: log.details?.reason,
          profileType: log.details?.profileType,
          accountType: log.details?.accountType
        }
      }));

      return {
        logs: transformedLogs,
        total: result.total,
        page: result.page,
        limit: result.limit
      };
    } catch (error) {
      logger.error('Error getting transfer logs:', error);
      throw createHttpError(500, 'Failed to get transfer logs');
    }
  }

  /**
   * Log a transfer event
   */
  static async logTransfer(
    type: 'account' | 'profile',
    action: 'claimed' | 'transferred' | 'ownership_change',
    fromUserId: string,
    toUserId: string,
    myPts: number,
    details?: {
      profileId?: string;
      accountId?: string;
      reason?: string;
      profileType?: string;
      accountType?: string;
    }
  ): Promise<{ success: boolean; logId: string }> {
    try {
      const fromUser = await User.findById(fromUserId);
      const toUser = await User.findById(toUserId);

      if (!fromUser || !toUser) {
        throw createHttpError(404, 'User not found');
      }

      // Create transfer log entry
      const transferLog = new TransferLogModel({
        type,
        action,
        fromUserId: new mongoose.Types.ObjectId(fromUserId),
        toUserId: new mongoose.Types.ObjectId(toUserId),
        fromUserName: fromUser.fullName,
        toUserName: toUser.fullName,
        icon: this.generateTransferIcon(type, action),
        actionText: this.generateActionText(type, action),
        myPts,
        details: {
          profileId: details?.profileId ? new mongoose.Types.ObjectId(details.profileId) : undefined,
          accountId: details?.accountId ? new mongoose.Types.ObjectId(details.accountId) : undefined,
          reason: details?.reason,
          profileType: details?.profileType,
          accountType: details?.accountType
        }
      });

      await transferLog.save();

      logger.info('Transfer logged to database:', {
        logId: (transferLog._id as any).toString(),
        type,
        action,
        fromUserId,
        toUserId,
        myPts
      });

      return {
        success: true,
        logId: (transferLog._id as any).toString()
      };
    } catch (error) {
      logger.error('Error logging transfer:', error);
      throw error;
    }
  }

  /**
   * Transfer account ownership (secondary accounts only)
   */
  static async transferAccountOwnership(
    accountId: string,
    fromUserId: string,
    toUserId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const fromUser = await User.findById(fromUserId);
      const toUser = await User.findById(toUserId);

      if (!fromUser || !toUser) {
        throw createHttpError(404, 'User not found');
      }

      // Check if the account is a secondary account
      if (fromUser.accountCategory !== 'SECONDARY_ACCOUNT') {
        throw createHttpError(400, 'Only secondary accounts can be transferred');
      }

      // Check if the receiving user has the right subscription to receive accounts
      const toUserPlan = await subscriptionService.getUserActivePlan(toUserId);
      if (!toUserPlan || toUserPlan.usageStats.secondaryAccounts >= toUserPlan.limits.secondaryAccounts) {
        throw createHttpError(400, 'Receiving user has reached their secondary account limit');
      }

      // Update account ownership
      await User.findByIdAndUpdate(accountId, {
        accountCategory: 'SECONDARY_ACCOUNT',
        // Add any other ownership transfer logic
      });

      // Log the transfer
      await this.logTransfer(
        'account',
        'ownership_change',
        fromUserId,
        toUserId,
        25, // myPts for account ownership transfer
        {
          accountId,
          reason,
          accountType: fromUser.accountCategory || 'SECONDARY_ACCOUNT'
        }
      );

      return {
        success: true,
        message: 'Account ownership transferred successfully'
      };
    } catch (error) {
      logger.error('Error transferring account ownership:', error);
      throw error;
    }
  }

  /**
   * Get transfer statistics
   */
  static async getTransferStats(userId?: string): Promise<any> {
    try {
      const stats = await (TransferLogModel as any).getTransferStats(userId);
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      logger.error('Error getting transfer stats:', error);
      throw createHttpError(500, 'Failed to get transfer statistics');
    }
  }

  // Helper methods
  private static generateUsername(fullName: string): string {
    const name = fullName.toLowerCase().replace(/[^a-z]/g, '');
    const randomSuffix = Math.random().toString(36).substring(2, 6);
    return `${name}${randomSuffix}`;
  }

  private static generateTemporaryPassword(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  private static getPlanLimitations(plan: string): any {
    const limitations = {
      free: {
        maxProfiles: 1,
        maxContacts: 5,
        maxDocuments: 1,
        maxLinks: 1,
        maxGalleryItems: 10,
        maxFollowers: 100,
        analyticsAccess: 'none',
        customQR: false,
        removeAds: false,
        prioritySupport: false,
        apiAccess: false,
        communityModeration: false,
        exportAnalytics: false,
        customTemplates: false
      },
      basic: {
        maxProfiles: 3,
        maxContacts: 20,
        maxDocuments: 5,
        maxLinks: 10,
        maxGalleryItems: 50,
        maxFollowers: 500,
        analyticsAccess: 'basic',
        customQR: true,
        removeAds: true,
        prioritySupport: false,
        apiAccess: false,
        communityModeration: false,
        exportAnalytics: false,
        customTemplates: false
      },
      plus: {
        maxProfiles: 10,
        maxContacts: 100,
        maxDocuments: 20,
        maxLinks: 50,
        maxGalleryItems: 200,
        maxFollowers: 2000,
        analyticsAccess: 'advanced',
        customQR: true,
        removeAds: true,
        prioritySupport: true,
        apiAccess: true,
        communityModeration: false,
        exportAnalytics: true,
        customTemplates: true
      },
      premium: {
        maxProfiles: -1, // unlimited
        maxContacts: -1,
        maxDocuments: -1,
        maxLinks: -1,
        maxGalleryItems: -1,
        maxFollowers: -1,
        analyticsAccess: 'full',
        customQR: true,
        removeAds: true,
        prioritySupport: true,
        apiAccess: true,
        communityModeration: true,
        exportAnalytics: true,
        customTemplates: true
      }
    };

    return limitations[plan.toLowerCase() as keyof typeof limitations] || limitations.free;
  }

  private static determineGender(fullName: string): string {
    // Simple gender determination based on common names
    // In a real implementation, you might want to use a more sophisticated approach
    const maleNames = ['john', 'mike', 'david', 'james', 'robert', 'william', 'richard', 'joseph', 'thomas', 'christopher'];
    const femaleNames = ['jane', 'mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan', 'jessica', 'sarah'];
    
    const firstName = fullName.toLowerCase().split(' ')[0];
    
    if (maleNames.includes(firstName)) return 'Male';
    if (femaleNames.includes(firstName)) return 'Female';
    return 'Other';
  }

  private static async calculateSubscriptionData(user: any): Promise<ISubscriptionData> {
    const plan = this.validatePlanType(user.subscription?.plan || 'free');
    const dataUsage = '465 MB'; // Mock data
    const dataLimit = '20 GB'; // Mock data
    const dataRemaining = '19.535 GB'; // Mock data
    const creationDate = user.createdAt ? user.createdAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const referralCode = user.referralCode || 'C412WEWW';
    
    // Calculate next billing date (30 days from start)
    const nextBillingDate = new Date(user.subscription?.startDate || new Date());
    nextBillingDate.setDate(nextBillingDate.getDate() + 30);

    // Mock invoice history
    const invoiceHistory = [
      {
        invoiceId: 'INV001',
        amount: '$0.00',
        date: creationDate,
        status: 'Paid',
        invoiceFileUrl: 'https://example.com/invoices/INV001.pdf'
      }
    ];

    // Get user's profiles as downline accounts
    const profiles = await ProfileModel.find({ creator: user._id })
      .select('profileType secondaryId createdAt')
      .sort({ createdAt: -1 });

    const downlineAccounts: ISubscriptionData['downlineAccounts'] = profiles.map(profile => ({
      userId: profile._id.toString(),
      username: profile.profileInformation?.username || profile.secondaryId || profile._id.toString(),
      dateReferred: profile.profileInformation.createdAt.toISOString().split('T')[0],
      profileType: profile.profileType
    }));

    return {
      plan: plan.charAt(0).toUpperCase() + plan.slice(1),
      dataUsage,
      dataLimit,
      dataRemaining,
      creationDate,
      referralCode,
      uplineAccount: user.referredBy ? user.referredBy.username : undefined,
      downlineReferrals: downlineAccounts.length,
      billingCycle: 'monthly',
      paymentMethod: 'none',
      autoRenew: user.subscription?.autoRenew || true,
      nextBillingDate: nextBillingDate.toISOString().split('T')[0],
      trialEndDate: user.subscription?.endDate ? user.subscription.endDate.toISOString().split('T')[0] : undefined,
      invoiceHistory,
      downlineAccounts
    };
  }

  private static extractLoginHistory(sessions: any[]): ILoginHistory[] {
    return sessions
      .filter(session => session.isActive)
      .map(session => ({
        device: session.deviceInfo?.deviceType || 'Unknown Device',
        ip: session.deviceInfo?.ip || 'Unknown IP',
        location: 'Unknown Location', // You might want to add geolocation service
        timestamp: session.lastUsed.toISOString()
      }))
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10); // Limit to last 10 logins
  }

  private static validatePlanType(plan: string): 'free' | 'basic' | 'plus' | 'premium' {
    const validPlans = ['free', 'basic', 'plus', 'premium'] as const;
    const normalizedPlan = plan.toLowerCase();
    
    if (validPlans.includes(normalizedPlan as any)) {
      return normalizedPlan as 'free' | 'basic' | 'plus' | 'premium';
    }
    
    // Default to free if invalid plan
    return 'free';
  }

  /**
   * Generate transfer icon based on type and action
   */
  private static generateTransferIcon(type: 'account' | 'profile', action: 'claimed' | 'transferred' | 'ownership_change'): string {
    const icons = {
      profile: {
        claimed: '👤✅',
        transferred: '🔁👤',
        ownership_change: '🔄👤'
      },
      account: {
        claimed: '💼✅',
        transferred: '🔁💼',
        ownership_change: '🔄🔐'
      }
    };

    return icons[type][action] || '📋';
  }

  /**
   * Generate action text based on type and action
   */
  private static generateActionText(type: 'account' | 'profile', action: 'claimed' | 'transferred' | 'ownership_change'): string {
    const actions = {
      profile: {
        claimed: 'Claimed Personal Profile',
        transferred: 'Transferred Personal Profile',
        ownership_change: 'Changed Profile Ownership'
      },
      account: {
        claimed: 'Claimed Secondary Account',
        transferred: 'Transferred Secondary Account',
        ownership_change: 'Transferred Account Ownership'
      }
    };

    return actions[type][action] || 'Transfer Action';
  }
} 