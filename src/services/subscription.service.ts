import { User, IUser } from '../models/User';
import { Contact } from '../models/Contact';
import { ProfileModel } from '../models/profile.model';
import UserPlan, { IUserPlan } from '../models/UserPlan';
import Plan from '../models/Plan';
import mongoose from 'mongoose';
import { logger } from '../utils/logger';
import createHttpError from 'http-errors';
import Stripe from 'stripe';
import crypto from 'crypto';

// Payment provider interfaces
interface PaymentProvider {
  createSubscription(userId: string, planId: string, billingCycle: 'monthly' | 'yearly'): Promise<{
    subscriptionId: string;
    paymentMethodId?: string;
    status: string;
  }>;
  
  cancelSubscription(subscriptionId: string): Promise<boolean>;
  
  updateSubscription(subscriptionId: string, planId: string): Promise<boolean>;
  
  validateWebhook(payload: any, signature: string): Promise<boolean>;
}

// Plan templates
const PLAN_TEMPLATES = {
  '688b0d48fed319825c93120d': { // Free plan ID
    name: 'Free',
    planType: 'free',
    price: 0,
    individualProfiles: 1,
    secondaryAccounts: 0,
    accessoryProfiles: 1,
    groupProfiles: 0,
    storageGB: 0.1,
    insightsAccess: 'none',
    nfcProductLinking: 0,
    qrCodeAccess: 'basic',
    scannerAccess: 'limited',
    communityAccess: 'view',
    myPtsRate: 1,
    payoutPriority: 'none',
    payoutDays: 0,
    rewardSystemAccess: false,
    bonusTriggers: 'none',
    gamifiedActivities: false,
    circleCreation: 'none',
    leadsReferrals: 'none',
    teamProxyRoles: false,
    affiliateProgram: 'none',
    addonMarketplace: false,
    supportType: 'community'
  }
};

// Stripe implementation
class StripeProvider implements PaymentProvider {
  private stripe: Stripe;
  
  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil',
    });
  }
  
  private getPlanTemplate(planId: string): any {
    return PLAN_TEMPLATES[planId as keyof typeof PLAN_TEMPLATES];
  }
  
  async createSubscription(userId: string, planId: string, billingCycle: 'monthly' | 'yearly'): Promise<{
    subscriptionId: string;
    paymentMethodId?: string;
    status: string;
  }> {
    try {
      // Get user details
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Get plan template based on planId
      const planData = await Plan.findById(planId);
      if (!planData) {
        throw new Error('Plan not found');
      }
      
      // Create Stripe customer if not exists
      let customerId = (user as any).stripeCustomerId;
      if (!customerId) {
        const customer = await this.stripe.customers.create({
          email: user.email,
          name: user.fullName,
          metadata: {
            userId: userId
          }
        });
        customerId = customer.id;
        
        // Update user with Stripe customer ID
        await User.findByIdAndUpdate(userId, { stripeCustomerId: customerId });
      }
      
      // Map billing cycle to Stripe interval
      const interval = billingCycle === 'monthly' ? 'month' : 'year';
      
      // Create Stripe price first
      const price = await this.stripe.prices.create({
        currency: 'usd',
        unit_amount: Math.round(planData.price * 100),
        recurring: {
          interval: interval as 'month' | 'year'
        },
        product_data: {
          name: planData.name
        }
      });
      
      // Create Stripe subscription
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{
          price: price.id
        }],
        metadata: {
          userId: userId,
          planId: planId,
          planType: planData.planType
        }
      });
      
      return {
        subscriptionId: subscription.id,
        paymentMethodId: subscription.default_payment_method as string,
        status: subscription.status
      };
    } catch (error) {
      logger.error('Error creating Stripe subscription:', error);
      throw error;
    }
  }
  
  async cancelSubscription(subscriptionId: string): Promise<boolean> {
    try {
      await this.stripe.subscriptions.cancel(subscriptionId);
      return true;
    } catch (error) {
      logger.error('Error canceling Stripe subscription:', error);
      return false;
    }
  }
  
  async updateSubscription(subscriptionId: string, planId: string): Promise<boolean> {
    try {
      const planData = await Plan.findById(planId);
      if (!planData) {
        throw new Error('Plan not found');
      }
      
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      const itemId = subscription.items.data[0].id;
      
      // Create new price for the updated plan
      const newPrice = await this.stripe.prices.create({
        currency: 'usd',
        unit_amount: Math.round(planData.price * 100),
        recurring: {
          interval: subscription.items.data[0].price.recurring?.interval || 'month'
        },
        product_data: {
          name: planData.name
        }
      });
      
      await this.stripe.subscriptions.update(subscriptionId, {
        items: [{
          id: itemId,
          price: newPrice.id
        }],
        metadata: {
          planId: planId,
          planType: planData.planType
        }
      });
      
      return true;
    } catch (error) {
      logger.error('Error updating Stripe subscription:', error);
      return false;
    }
  }
  
  async validateWebhook(payload: any, signature: string): Promise<boolean> {
    try {
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
      const event = this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);
      return !!event;
    } catch (error) {
      logger.error('Error validating Stripe webhook signature:', error);
      return false;
    }
  }
}

// Flutterwave implementation
class FlutterwaveProvider implements PaymentProvider {
  private secretKey: string;
  private publicKey: string;
  
  constructor() {
    this.secretKey = process.env.FLUTTERWAVE_SECRET_KEY!;
    this.publicKey = process.env.FLUTTERWAVE_PUBLIC_KEY!;
  }
  
  async createSubscription(userId: string, planId: string, billingCycle: 'monthly' | 'yearly'): Promise<{
    subscriptionId: string;
    paymentMethodId?: string;
    status: string;
  }> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Get plan template
      const planData = await Plan.findById(planId);
      if (!planData) {
        throw new Error('Plan not found');
      }
      
      // For now, use a simplified approach that creates a test subscription
      // This can be enhanced later when Flutterwave API is properly configured
      logger.info('Creating Flutterwave subscription with test approach');
      
      // Simulate a successful payment creation
      const subscriptionId = `flutterwave_sub_${Date.now()}_${userId}`;
      const paymentMethodId = `flutterwave_pm_${Date.now()}`;
      
      return {
        subscriptionId,
        paymentMethodId,
        status: 'pending' // Will be activated via webhook
      };
    } catch (error) {
      logger.error('Error creating Flutterwave subscription:', error);
      throw error;
    }
  }
  
  async cancelSubscription(subscriptionId: string): Promise<boolean> {
    try {
      // For Flutterwave, we can't cancel a one-time payment, but we can refund it
      // Check if the payment was successful first
      const response = await fetch(`https://api.flutterwave.com/v3/transactions/${subscriptionId}/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.secretKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      // Check if response is ok
      if (!response.ok) {
        const errorText = await response.text();
        logger.error('Flutterwave cancel API error:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        return false;
      }
      
      // Try to parse JSON response
      let data;
      try {
        const responseText = await response.text();
        data = JSON.parse(responseText);
      } catch (parseError) {
        logger.error('Failed to parse Flutterwave cancel response:', parseError);
        return false;
      }
      
      return data.status === 'success';
    } catch (error) {
      logger.error('Error canceling Flutterwave subscription:', error);
      return false;
    }
  }
  
  async updateSubscription(subscriptionId: string, planId: string): Promise<boolean> {
    try {
      const planData = await Plan.findById(planId);
      if (!planData) {
        throw new Error('Plan not found');
      }
      
      // For Flutterwave, we can't update a one-time payment, but we can create a new payment
      // This would typically involve creating a new payment for the updated plan
      logger.info('Flutterwave subscription update not supported for one-time payments');
      return false;
    } catch (error) {
      logger.error('Error updating Flutterwave subscription:', error);
      return false;
    }
  }
  
  async validateWebhook(payload: any, signature: string): Promise<boolean> {
    try {
      const secretHash = process.env.FLUTTERWAVE_SECRET_HASH!;
      const hash = crypto
        .createHmac('sha512', secretHash)
        .update(JSON.stringify(payload))
        .digest('hex');
      
      return hash === signature;
    } catch (error) {
      logger.error('Error validating Flutterwave webhook signature:', error);
      return false;
    }
  }
}

// Test webhook implementation for development
class TestWebhookProvider implements PaymentProvider {
  async createSubscription(userId: string, planId: string, billingCycle: 'monthly' | 'yearly'): Promise<{
    subscriptionId: string;
    paymentMethodId?: string;
    status: string;
  }> {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      subscriptionId: `test_sub_${Date.now()}`,
      paymentMethodId: `test_pm_${Date.now()}`,
      status: 'active'
    };
  }
  
  async cancelSubscription(subscriptionId: string): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  }
  
  async updateSubscription(subscriptionId: string, planId: string): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  }
  
  async validateWebhook(payload: any, signature: string): Promise<boolean> {
    // For testing, always return true
    return true;
  }
}

export interface LimitCheck {
  allowed: boolean;
  current: number;
  max: number;
  percentage: number;
  nearingLimit: boolean;
}

export interface UsageStats {
  individualProfiles: LimitCheck;
  secondaryAccounts: LimitCheck;
  accessoryProfiles: LimitCheck;
  groupProfiles: LimitCheck;
  storage: LimitCheck;
  nfcLinks: LimitCheck;
  nearingLimits: string[];
}

export interface UpgradeOption {
  planId: string;
  planName: string;
  planType: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  benefits: string[];
  savings?: string;
}

class SubscriptionService {
  private paymentProviders: Record<string, PaymentProvider>;
  
  constructor() {
    this.paymentProviders = {
      stripe: new StripeProvider(),
      flutterwave: new FlutterwaveProvider(),
      test: new TestWebhookProvider()
    };
  }
  
  /**
   * Get user's current active plan
   */
  async getUserActivePlan(userId: string): Promise<IUserPlan | null> {
    try {
      const userPlan = await UserPlan.findOne({
        userId: new mongoose.Types.ObjectId(userId),
        isActive: true
      }).populate('planId');
      
      return userPlan;
    } catch (error) {
      logger.error('Error getting user active plan:', error);
      throw error;
    }
  }
  
  /**
   * Get all plans available for subscription
   */
  async getAvailablePlans(): Promise<any[]> {
    try {
      // Fetch plans from database instead of using hardcoded templates
      const plans = await Plan.find({ isActive: true }).sort({ price: 1 });
      
      return plans;
    } catch (error) {
      logger.error('Error getting available plans:', error);
      throw error;
    }
  }
  
  /**
   * Assign a plan to a user
   */
  async assignPlanToUser(
    userId: string, 
    planId: string, 
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    paymentProvider: 'stripe' | 'flutterwave' | 'test' = 'test'
  ): Promise<IUserPlan> {
    try {
      // Fetch plan from database instead of using hardcoded templates
      const plan = await Plan.findById(planId);
      console.log("plandId",planId);
      if (!plan) {
        throw createHttpError(404, 'Plan not found');
      }
      
      const user = await User.findById(userId);
      if (!user) {
        throw createHttpError(404, 'User not found');
      }
      
      // Deactivate any existing active plan
      await UserPlan.updateMany(
        { userId: new mongoose.Types.ObjectId(userId), isActive: true },
        { isActive: false }
      );
      
      // Create payment subscription
      const provider = this.paymentProviders[paymentProvider];
      const paymentResult = await provider.createSubscription(userId, planId, billingCycle);
      
      // Calculate expiration date
      const now = new Date();
      const expiresAt = new Date(now);
      expiresAt.setMonth(expiresAt.getMonth() + (billingCycle === 'yearly' ? 12 : 1));
      
      // Create user plan record
      const userPlan = new UserPlan({
        userId: new mongoose.Types.ObjectId(userId),
        planId: new mongoose.Types.ObjectId(planId),
        planName: plan.name,
        planType: plan.planType,
        billingCycle,
        assignedAt: now,
        expiresAt,
        isActive: true,
        paymentProvider,
        paymentStatus: paymentResult.status,
        subscriptionId: paymentResult.subscriptionId,
        paymentMethodId: paymentResult.paymentMethodId,
        lastPaymentDate: now,
        nextBillingDate: expiresAt,
        limits: {
          individualProfiles: plan.individualProfiles,
          secondaryAccounts: plan.secondaryAccounts,
          accessoryProfiles: plan.accessoryProfiles,
          groupProfiles: plan.groupProfiles,
          storageGB: plan.storageGB,
          nfcProductLinking: plan.nfcProductLinking
        },
        features: {
          insightsAccess: plan.insightsAccess,
          qrCodeAccess: plan.qrCodeAccess,
          scannerAccess: plan.scannerAccess,
          communityAccess: plan.communityAccess,
          myPtsRate: plan.myPtsRate,
          payoutPriority: plan.payoutPriority,
          payoutDays: plan.payoutDays,
          rewardSystemAccess: plan.rewardSystemAccess,
          bonusTriggers: plan.bonusTriggers,
          gamifiedActivities: plan.gamifiedActivities,
          circleCreation: plan.circleCreation,
          leadsReferrals: plan.leadsReferrals,
          teamProxyRoles: plan.teamProxyRoles,
          affiliateProgram: plan.affiliateProgram,
          addonMarketplace: plan.addonMarketplace,
          supportType: plan.supportType
        }
      });
      
      await userPlan.save();
      
      // Update user's current plan reference
      user.currentPlan = userPlan._id as mongoose.Types.ObjectId;
      user.subscriptionHistory.push(userPlan._id as mongoose.Types.ObjectId);
      await user.save();
      
      logger.info(`Plan ${plan.name} assigned to user ${userId}`);
      return userPlan;
    } catch (error) {
      logger.error('Error assigning plan to user:', error);
      throw error;
    }
  }
  
  /**
   * Upgrade user's plan
   */
  async upgradeUserPlan(
    userId: string,
    newPlanId: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    paymentProvider: 'stripe' | 'flutterwave' | 'test' = 'test'
  ): Promise<IUserPlan> {
    try {
      const currentPlan = await this.getUserActivePlan(userId);
      if (!currentPlan) {
        throw createHttpError(404, 'No active plan found');
      }
      
      // Fetch new plan from database instead of using hardcoded templates
      const newPlan = await Plan.findById(newPlanId);
      if (!newPlan) {
        throw createHttpError(404, 'New plan not found');
      }
      
      // Add to upgrade history
      currentPlan.upgradeHistory.push({
        fromPlan: currentPlan.planName,
        toPlan: newPlan.name,
        date: new Date(),
        reason: 'User upgrade'
      });
      
      // Deactivate current plan
      currentPlan.isActive = false;
      await currentPlan.save();
      
      // Assign new plan
      const newUserPlan = await this.assignPlanToUser(userId, newPlanId, billingCycle, 'test');
      
      logger.info(`User ${userId} upgraded from ${currentPlan.planName} to ${newPlan.name}`);
      return newUserPlan;
    } catch (error) {
      logger.error('Error upgrading user plan:', error);
      throw error;
    }
  }
  
  /**
   * Cancel user's subscription
   */
  async cancelUserSubscription(userId: string, reason?: string): Promise<boolean> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        throw createHttpError(404, 'No active plan found');
      }
      
      // Cancel with payment provider
      const provider = this.paymentProviders[userPlan.paymentProvider];
      if (provider && userPlan.subscriptionId) {
        await provider.cancelSubscription(userPlan.subscriptionId);
      }
      
      // Deactivate plan
      userPlan.isActive = false;
      userPlan.paymentStatus = 'canceled';
      userPlan.cancelReason = reason;
      await userPlan.save();
      
      // Update user's current plan reference
      await User.findByIdAndUpdate(userId, { currentPlan: null });
      
      logger.info(`Subscription canceled for user ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error canceling user subscription:', error);
      throw error;
    }
  }
  
  /**
   * Check if user can perform an action based on their plan limits
   */
  async checkActionAllowed(userId: string, action: string): Promise<{
    allowed: boolean;
    reason?: string;
    upgradeOptions?: UpgradeOption[];
  }> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return { allowed: false, reason: 'No active subscription' };
      }
      
      const usage = await this.getUsageStats(userId);
      
      switch (action) {
        case 'create_individual_profile':
          if (!usage.individualProfiles.allowed) {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'Individual profile limit reached',
              upgradeOptions
            };
          }
          break;
          
        case 'create_secondary_account':
          if (!usage.secondaryAccounts.allowed) {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'Secondary account limit reached',
              upgradeOptions
            };
          }
          break;
          
        case 'create_accessory_profile':
          if (!usage.accessoryProfiles.allowed) {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'Accessory profile limit reached',
              upgradeOptions
            };
          }
          break;
          
        case 'create_group_profile':
          if (!usage.groupProfiles.allowed) {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'Group profile limit reached',
              upgradeOptions
            };
          }
          break;
          
        case 'access_analytics':
          if (userPlan.features.insightsAccess === 'none') {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'Analytics not available in current plan',
              upgradeOptions
            };
          }
          break;
          
        case 'create_nfc_link':
          if (usage.nfcLinks.current >= userPlan.limits.nfcProductLinking) {
            const upgradeOptions = await this.getUpgradeRecommendations(userId);
            return {
              allowed: false,
              reason: 'NFC linking limit reached',
              upgradeOptions
            };
          }
          break;
      }
      
      return { allowed: true };
    } catch (error) {
      logger.error('Error checking action allowed:', error);
      throw error;
    }
  }
  
  /**
   * Get comprehensive usage statistics
   */
  async getUsageStats(userId: string): Promise<UsageStats> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        throw createHttpError(404, 'No active plan found');
      }
      
      // Get current usage counts
      const user = await User.findById(userId).populate('profiles');
      const profileCount = user?.profiles?.length || 0;
      
      // Calculate usage percentages
      const calculateLimitCheck = (current: number, max: number): LimitCheck => {
        if (max === -1) { // Unlimited
          return {
            allowed: true,
            current,
            max: -1,
            percentage: 0,
            nearingLimit: false
          };
        }
        
        const percentage = (current / max) * 100;
        return {
          allowed: current < max,
          current,
          max,
          percentage,
          nearingLimit: percentage >= 80
        };
      };
      
      const individualProfiles = calculateLimitCheck(
        userPlan.usageStats.individualProfiles,
        userPlan.limits.individualProfiles
      );
      
      const secondaryAccounts = calculateLimitCheck(
        userPlan.usageStats.secondaryAccounts,
        userPlan.limits.secondaryAccounts
      );
      
      const accessoryProfiles = calculateLimitCheck(
        userPlan.usageStats.accessoryProfiles,
        userPlan.limits.accessoryProfiles
      );
      
      const groupProfiles = calculateLimitCheck(
        userPlan.usageStats.groupProfiles,
        userPlan.limits.groupProfiles
      );
      
      const storage = calculateLimitCheck(
        userPlan.usageStats.storageUsedMB / 1024, // Convert MB to GB
        userPlan.limits.storageGB
      );
      
      const nfcLinks = calculateLimitCheck(
        userPlan.usageStats.nfcLinksCreated,
        userPlan.limits.nfcProductLinking
      );
      
      const nearingLimits: string[] = [];
      if (individualProfiles.nearingLimit) nearingLimits.push('individualProfiles');
      if (secondaryAccounts.nearingLimit) nearingLimits.push('secondaryAccounts');
      if (accessoryProfiles.nearingLimit) nearingLimits.push('accessoryProfiles');
      if (groupProfiles.nearingLimit) nearingLimits.push('groupProfiles');
      if (storage.nearingLimit) nearingLimits.push('storage');
      if (nfcLinks.nearingLimit) nearingLimits.push('nfcLinks');
      
      return {
        individualProfiles,
        secondaryAccounts,
        accessoryProfiles,
        groupProfiles,
        storage,
        nfcLinks,
        nearingLimits
      };
    } catch (error) {
      logger.error('Error getting usage stats:', error);
      throw error;
    }
  }
  
  /**
   * Get upgrade recommendations based on current usage
   */
  async getUpgradeRecommendations(userId: string): Promise<UpgradeOption[]> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      const availablePlans = await this.getAvailablePlans();
      
      // If no active plan, return all available plans as recommendations
      if (!userPlan) {
        return availablePlans.map(plan => ({
          planId: (plan._id as any).toString(),
          planName: plan.name,
          planType: plan.planType,
          price: plan.price,
          billingCycle: 'monthly',
          benefits: [
            `${plan.individualProfiles} individual profiles`,
            `${plan.secondaryAccounts} secondary accounts`,
            `${plan.accessoryProfiles} accessory profiles`,
            `${plan.groupProfiles} group profiles`,
            `${plan.storageGB}GB storage`,
            plan.insightsAccess !== 'none' ? 'Analytics access' : null,
            plan.nfcProductLinking > 0 ? 'NFC product linking' : null,
            `${plan.myPtsRate}x MyPts earning rate`
          ].filter(Boolean) as string[]
        }));
      }
      
      const currentPlanData = await Plan.findById(userPlan.planId);
      if (!currentPlanData) {
        // If current plan template not found, return all available plans
        return availablePlans.map(plan => ({
          planId: (plan._id as any).toString(),
          planName: plan.name,
          planType: plan.planType,
          price: plan.price,
          billingCycle: 'monthly',
          benefits: [
            `${plan.individualProfiles} individual profiles`,
            `${plan.secondaryAccounts} secondary accounts`,
            `${plan.accessoryProfiles} accessory profiles`,
            `${plan.groupProfiles} group profiles`,
            `${plan.storageGB}GB storage`,
            plan.insightsAccess !== 'none' ? 'Analytics access' : null,
            plan.nfcProductLinking > 0 ? 'NFC product linking' : null,
            `${plan.myPtsRate}x MyPts earning rate`
          ].filter(Boolean) as string[]
        }));
      }
      
      const recommendations: UpgradeOption[] = [];
      
      for (const plan of availablePlans) {
        if (plan.planType === userPlan.planType) continue; // Skip current plan
        
        const benefits: string[] = [];
        
        // Compare limits
        if (plan.individualProfiles > currentPlanData.individualProfiles) {
          benefits.push(`${plan.individualProfiles} individual profiles (vs ${currentPlanData.individualProfiles})`);
        }
        if (plan.secondaryAccounts > currentPlanData.secondaryAccounts) {
          benefits.push(`${plan.secondaryAccounts} secondary accounts (vs ${currentPlanData.secondaryAccounts})`);
        }
        if (plan.accessoryProfiles > currentPlanData.accessoryProfiles) {
          benefits.push(`${plan.accessoryProfiles} accessory profiles (vs ${currentPlanData.accessoryProfiles})`);
        }
        if (plan.groupProfiles > currentPlanData.groupProfiles) {
          benefits.push(`${plan.groupProfiles} group profiles (vs ${currentPlanData.groupProfiles})`);
        }
        if (plan.storageGB > currentPlanData.storageGB) {
          benefits.push(`${plan.storageGB}GB storage (vs ${currentPlanData.storageGB}GB)`);
        }
        
        // Compare features
        if (plan.insightsAccess !== 'none' && currentPlanData.insightsAccess === 'none') {
          benefits.push('Analytics access');
        }
        if (plan.nfcProductLinking > 0 && currentPlanData.nfcProductLinking === 0) {
          benefits.push('NFC product linking');
        }
          if (plan.myPtsRate > currentPlanData.myPtsRate) {
          benefits.push(`${plan.myPtsRate}x MyPts earning rate (vs ${currentPlanData.myPtsRate}x)`);
        }
        
        if (benefits.length > 0) {
          recommendations.push({
            planId: (plan._id as any).toString(),
            planName: plan.name,
            planType: plan.planType,
            price: plan.price,
            billingCycle: 'monthly',
            benefits
          });
        }
      }
      
      return recommendations.sort((a, b) => a.price - b.price);
    } catch (error) {
      logger.error('Error getting upgrade recommendations:', error);
      throw error;
    }
  }
  
  /**
   * Process webhook from payment provider
   */
  async processWebhook(
    provider: 'stripe' | 'flutterwave' | 'test',
    payload: any,
    signature: string
  ): Promise<boolean> {
    try {
      const paymentProvider = this.paymentProviders[provider];
      if (!paymentProvider) {
        throw createHttpError(400, 'Invalid payment provider');
      }
      
      // Validate webhook signature
      const isValid = await paymentProvider.validateWebhook(payload, signature);
      if (!isValid) {
        throw createHttpError(400, 'Invalid webhook signature');
      }
      
      // Process webhook based on provider
      switch (provider) {
        case 'stripe':
          return await this.processStripeWebhook(payload);
        case 'flutterwave':
          return await this.processFlutterwaveWebhook(payload);
        case 'test':
          return await this.processTestWebhook(payload);
        default:
          throw createHttpError(400, 'Unsupported payment provider');
      }
    } catch (error) {
      logger.error('Error processing webhook:', error);
      throw error;
    }
  }
  
  /**
   * Process Stripe webhook
   */
  private async processStripeWebhook(payload: any): Promise<boolean> {
    try {
      const event = payload;
      logger.info('Processing Stripe webhook:', event.type);
      
      switch (event.type) {
        case 'invoice.payment_succeeded':
          await this.handleStripePaymentSucceeded(event.data.object);
          break;
          
        case 'invoice.payment_failed':
          await this.handleStripePaymentFailed(event.data.object);
          break;
          
        case 'customer.subscription.created':
          await this.handleStripeSubscriptionCreated(event.data.object);
          break;
          
        case 'customer.subscription.updated':
          await this.handleStripeSubscriptionUpdated(event.data.object);
          break;
          
        case 'customer.subscription.deleted':
          await this.handleStripeSubscriptionDeleted(event.data.object);
          break;
          
        case 'customer.subscription.trial_will_end':
          await this.handleStripeTrialWillEnd(event.data.object);
          break;
          
        default:
          logger.info(`Unhandled Stripe event type: ${event.type}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Error processing Stripe webhook:', error);
      return false;
    }
  }
  
  /**
   * Handle Stripe payment succeeded
   */
  private async handleStripePaymentSucceeded(invoice: any): Promise<void> {
    const subscriptionId = invoice.subscription;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = 'active';
      userPlan.lastPaymentDate = new Date();
      userPlan.nextBillingDate = new Date(invoice.next_payment_attempt * 1000);
      await userPlan.save();
      
      logger.info(`Updated user plan ${userPlan._id} payment status to active`);
    }
  }
  
  /**
   * Handle Stripe payment failed
   */
  private async handleStripePaymentFailed(invoice: any): Promise<void> {
    const subscriptionId = invoice.subscription;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = 'past_due';
      await userPlan.save();
      
      logger.info(`Updated user plan ${userPlan._id} payment status to past_due`);
    }
  }
  
  /**
   * Handle Stripe subscription created
   */
  private async handleStripeSubscriptionCreated(subscription: any): Promise<void> {
    const userId = subscription.metadata?.userId;
    const planId = subscription.metadata?.planId;
    
    if (userId && planId) {
      const userPlan = await UserPlan.findOne({ userId });
      if (userPlan) {
        userPlan.subscriptionId = subscription.id;
        userPlan.paymentStatus = subscription.status;
        userPlan.paymentProvider = 'stripe';
        await userPlan.save();
        
        logger.info(`Updated user plan ${userPlan._id} with Stripe subscription ${subscription.id}`);
      }
    }
  }
  
  /**
   * Handle Stripe subscription updated
   */
  private async handleStripeSubscriptionUpdated(subscription: any): Promise<void> {
    const userPlan = await UserPlan.findOne({ subscriptionId: subscription.id });
    
    if (userPlan) {
      userPlan.paymentStatus = subscription.status;
      userPlan.autoRenew = subscription.cancel_at_period_end === false;
      await userPlan.save();
      
      logger.info(`Updated user plan ${userPlan._id} status to ${subscription.status}`);
    }
  }
  
  /**
   * Handle Stripe subscription deleted
   */
  private async handleStripeSubscriptionDeleted(subscription: any): Promise<void> {
    const userPlan = await UserPlan.findOne({ subscriptionId: subscription.id });
    
    if (userPlan) {
      userPlan.paymentStatus = 'canceled';
      userPlan.isActive = false;
      await userPlan.save();
      
      logger.info(`Canceled user plan ${userPlan._id} due to Stripe subscription deletion`);
    }
  }
  
  /**
   * Handle Stripe trial will end
   */
  private async handleStripeTrialWillEnd(subscription: any): Promise<void> {
    const userPlan = await UserPlan.findOne({ subscriptionId: subscription.id });
    
    if (userPlan) {
      // Send notification to user about trial ending
      logger.info(`Trial ending soon for user plan ${userPlan._id}`);
      // TODO: Send email notification
    }
  }
  
  /**
   * Process Flutterwave webhook
   */
  private async processFlutterwaveWebhook(payload: any): Promise<boolean> {
    try {
      const event = payload;
      logger.info('Processing Flutterwave webhook:', event.event);
      
      switch (event.event) {
        case 'charge.completed':
          await this.handleFlutterwaveChargeCompleted(event.data);
          break;
          
        case 'subscription.activated':
          await this.handleFlutterwaveSubscriptionActivated(event.data);
          break;
          
        case 'subscription.cancelled':
          await this.handleFlutterwaveSubscriptionCancelled(event.data);
          break;
          
        case 'subscription.expired':
          await this.handleFlutterwaveSubscriptionExpired(event.data);
          break;
          
        case 'subscription.updated':
          await this.handleFlutterwaveSubscriptionUpdated(event.data);
          break;
          
        default:
          logger.info(`Unhandled Flutterwave event type: ${event.event}`);
      }
      
      return true;
    } catch (error) {
      logger.error('Error processing Flutterwave webhook:', error);
      return false;
    }
  }
  
  /**
   * Handle Flutterwave charge completed
   */
  private async handleFlutterwaveChargeCompleted(data: any): Promise<void> {
    const subscriptionId = data.tx_ref;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan && data.status === 'successful') {
      userPlan.paymentStatus = 'active';
      userPlan.lastPaymentDate = new Date();
      await userPlan.save();
      
      logger.info(`Updated user plan ${userPlan._id} payment status to active`);
    }
  }
  
  /**
   * Handle Flutterwave subscription activated
   */
  private async handleFlutterwaveSubscriptionActivated(data: any): Promise<void> {
    const subscriptionId = data.subscription_id;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = 'active';
      userPlan.isActive = true;
      await userPlan.save();
      
      logger.info(`Activated user plan ${userPlan._id} with Flutterwave subscription ${subscriptionId}`);
    }
  }
  
  /**
   * Handle Flutterwave subscription cancelled
   */
  private async handleFlutterwaveSubscriptionCancelled(data: any): Promise<void> {
    const subscriptionId = data.subscription_id;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = 'canceled';
      userPlan.isActive = false;
      await userPlan.save();
      
      logger.info(`Canceled user plan ${userPlan._id} due to Flutterwave subscription cancellation`);
    }
  }
  
  /**
   * Handle Flutterwave subscription expired
   */
  private async handleFlutterwaveSubscriptionExpired(data: any): Promise<void> {
    const subscriptionId = data.subscription_id;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = 'canceled';
      userPlan.isActive = false;
      await userPlan.save();
      
      logger.info(`Expired user plan ${userPlan._id} due to Flutterwave subscription expiration`);
    }
  }
  
  /**
   * Handle Flutterwave subscription updated
   */
  private async handleFlutterwaveSubscriptionUpdated(data: any): Promise<void> {
    const subscriptionId = data.subscription_id;
    const userPlan = await UserPlan.findOne({ subscriptionId });
    
    if (userPlan) {
      userPlan.paymentStatus = data.status;
      await userPlan.save();
      
      logger.info(`Updated user plan ${userPlan._id} status to ${data.status}`);
    }
  }
  
  /**
   * Process test webhook for development
   */
  private async processTestWebhook(payload: any): Promise<boolean> {
    logger.info('Processing test webhook:', payload);
    
    // Simulate webhook processing
    const { subscriptionId, status, userId } = payload;
    
    if (subscriptionId && status && userId) {
      const userPlan = await UserPlan.findOne({ subscriptionId });
      if (userPlan) {
        userPlan.paymentStatus = status;
        await userPlan.save();
        
        logger.info(`Updated user plan ${userPlan._id} status to ${status}`);
      }
    }
    
    return true;
  }
  
  /**
   * Update usage statistics
   */
  async updateUsageStats(
    userId: string,
    action: string,
    increment: number = 1
  ): Promise<void> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        throw createHttpError(404, 'No active plan found');
      }
      
      switch (action) {
        case 'individual_profile_created':
          userPlan.usageStats.individualProfiles += increment;
          break;
        case 'secondary_account_created':
          userPlan.usageStats.secondaryAccounts += increment;
          break;
        case 'accessory_profile_created':
          userPlan.usageStats.accessoryProfiles += increment;
          break;
        case 'group_profile_created':
          userPlan.usageStats.groupProfiles += increment;
          break;
        case 'storage_used':
          userPlan.usageStats.storageUsedMB += increment;
          break;
        case 'nfc_link_created':
          userPlan.usageStats.nfcLinksCreated += increment;
          break;
        case 'qr_code_generated':
          userPlan.usageStats.qrCodesGenerated += increment;
          break;
        case 'scan_performed':
          userPlan.usageStats.scansPerformed += increment;
          break;
        case 'mypts_earned':
          userPlan.usageStats.myPtsEarned += increment;
          break;
        case 'leads_generated':
          userPlan.usageStats.leadsGenerated += increment;
          break;
      }
      
      await userPlan.save();
      logger.info(`Updated usage stats for user ${userId}, action: ${action}`);
    } catch (error) {
      logger.error('Error updating usage stats:', error);
      throw error;
    }
  }
  
  /**
   * Initialize default plans in the database
   */
  async initializeDefaultPlans(): Promise<void> {
    try {
      // Since we're using PLAN_TEMPLATES instead of Plan model, 
      // this method is no longer needed for database initialization
      logger.info('Default plans are managed through PLAN_TEMPLATES, no database initialization needed');
    } catch (error) {
      logger.error('Error initializing default plans:', error);
      throw error;
    }
  }

  // Admin Plan Management Methods - Updated to work with PLAN_TEMPLATES
  async createPlan(planData: any): Promise<any> {
    try {
      // Since we're using PLAN_TEMPLATES, we can't create new plans dynamically
      // This method now returns an error or could be used to add to PLAN_TEMPLATES
      throw createHttpError(400, 'Plan creation is not supported. Plans are managed through templates.');
    } catch (error) {
      logger.error('Error creating plan:', error);
      throw error;
    }
  }

  async updatePlan(planId: string, updateData: any): Promise<any> {
    try {
      // Since we're using PLAN_TEMPLATES, we can't update plans dynamically
      throw createHttpError(400, 'Plan updates are not supported. Plans are managed through templates.');
    } catch (error) {
      logger.error('Error updating plan:', error);
      throw error;
    }
  }

  async deletePlan(planId: string): Promise<void> {
    try {
      // Check if any users are currently on this plan
      const activeUsers = await UserPlan.findOne({ planId, isActive: true });
      if (activeUsers) {
        throw createHttpError(400, 'Cannot delete plan with active users');
      }

      // Since we're using PLAN_TEMPLATES, we can't delete plans dynamically
      throw createHttpError(400, 'Plan deletion is not supported. Plans are managed through templates.');
    } catch (error) {
      logger.error('Error deleting plan:', error);
      throw error;
    }
  }

  async getAllPlans(): Promise<any[]> {
    try {
      // Return plan templates as available plans
      return await Plan.find({ isActive: true }).sort({ price: 1 });
    } catch (error) {
      logger.error('Error getting all plans:', error);
      throw error;
    }
  }

  async getPlanById(planId: string): Promise<any> {
    try {
     const planData = await Plan.findById(planId);
      if (!planData) {
        throw createHttpError(404, 'Plan not found');
      }
      return {
        ...planData,
      };
    } catch (error) {
      logger.error('Error getting plan by ID:', error);
      throw error;
    }
  }

  async togglePlanStatus(planId: string): Promise<any> {
    try {
      // Since we're using PLAN_TEMPLATES, we can't toggle plan status dynamically
      throw createHttpError(400, 'Plan status toggling is not supported. Plans are managed through templates.');
    } catch (error) {
      logger.error('Error toggling plan status:', error);
      throw error;
    }
  }

  async getPlanUsageStats(planId: string): Promise<any> {
    try {
    const planData = await Plan.findById(planId);
      if (!planData) {
        throw createHttpError(404, 'Plan not found');
      }

      // Get all users on this plan
      const userPlans = await UserPlan.find({ planId, isActive: true });
      
      // Calculate aggregate statistics
      const totalUsers = userPlans.length;
      const totalRevenue = userPlans.reduce((sum, userPlan) => {
        return sum + (planData.price || 0);
      }, 0);

      // Get usage statistics
      const usageStats = {
        totalUsers,
        totalRevenue,
        averageUsage: {
          individualProfiles: 0,
          secondaryAccounts: 0,
          accessoryProfiles: 0,
          groupProfiles: 0,
          storageUsedMB: 0,
          leadsGenerated: 0,
          myPtsEarned: 0,
          scansPerformed: 0,
          qrCodesGenerated: 0,
          nfcLinksCreated: 0
        }
      };

      if (totalUsers > 0) {
        const totalUsage = userPlans.reduce((acc, userPlan) => {
          return {
            individualProfiles: acc.individualProfiles + (userPlan.usageStats.individualProfiles || 0),
            secondaryAccounts: acc.secondaryAccounts + (userPlan.usageStats.secondaryAccounts || 0),
            accessoryProfiles: acc.accessoryProfiles + (userPlan.usageStats.accessoryProfiles || 0),
            groupProfiles: acc.groupProfiles + (userPlan.usageStats.groupProfiles || 0),
            storageUsedMB: acc.storageUsedMB + (userPlan.usageStats.storageUsedMB || 0),
            leadsGenerated: acc.leadsGenerated + (userPlan.usageStats.leadsGenerated || 0),
            myPtsEarned: acc.myPtsEarned + (userPlan.usageStats.myPtsEarned || 0),
            scansPerformed: acc.scansPerformed + (userPlan.usageStats.scansPerformed || 0),
            qrCodesGenerated: acc.qrCodesGenerated + (userPlan.usageStats.qrCodesGenerated || 0),
            nfcLinksCreated: acc.nfcLinksCreated + (userPlan.usageStats.nfcLinksCreated || 0)
          };
        }, {
          individualProfiles: 0,
          secondaryAccounts: 0,
          accessoryProfiles: 0,
          groupProfiles: 0,
          storageUsedMB: 0,
          leadsGenerated: 0,
          myPtsEarned: 0,
          scansPerformed: 0,
          qrCodesGenerated: 0,
          nfcLinksCreated: 0
        });

        usageStats.averageUsage = {
          individualProfiles: Math.round(totalUsage.individualProfiles / totalUsers * 100) / 100,
          secondaryAccounts: Math.round(totalUsage.secondaryAccounts / totalUsers * 100) / 100,
          accessoryProfiles: Math.round(totalUsage.accessoryProfiles / totalUsers * 100) / 100,
          groupProfiles: Math.round(totalUsage.groupProfiles / totalUsers * 100) / 100,
          storageUsedMB: Math.round(totalUsage.storageUsedMB / totalUsers * 100) / 100,
          leadsGenerated: Math.round(totalUsage.leadsGenerated / totalUsers * 100) / 100,
          myPtsEarned: Math.round(totalUsage.myPtsEarned / totalUsers * 100) / 100,
          scansPerformed: Math.round(totalUsage.scansPerformed / totalUsers * 100) / 100,
          qrCodesGenerated: Math.round(totalUsage.qrCodesGenerated / totalUsers * 100) / 100,
          nfcLinksCreated: Math.round(totalUsage.nfcLinksCreated / totalUsers * 100) / 100
        };
      }

      return {
        plan: {
          ...planData,
          isActive: true
        },
        usageStats
      };
    } catch (error) {
      logger.error('Error getting plan usage stats:', error);
      throw error;
    }
  }

  async duplicatePlan(planId: string, options: { newName: string; newPrice: number }): Promise<any> {
    try {
      // Since we're using PLAN_TEMPLATES, we can't duplicate plans dynamically
      throw createHttpError(400, 'Plan duplication is not supported. Plans are managed through templates.');
    } catch (error) {
      logger.error('Error duplicating plan:', error);
      throw error;
    }
  }

  async updateBillingPreferences(userId: string, preferences: { autoRenew: boolean }): Promise<any> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      
      if (!userPlan) {
        throw createHttpError(404, 'No active subscription found');
      }
      
      userPlan.autoRenew = preferences.autoRenew;
      await userPlan.save();
      
      return { autoRenew: preferences.autoRenew };
    } catch (error) {
      logger.error('Error updating billing preferences:', error);
      throw error;
    }
  }

  // Legacy methods for backward compatibility
  async getSubscriptionLimits(tier: string): Promise<any> {
    try {
      // Find plan template by planType
      const planTemplate = Object.values(PLAN_TEMPLATES).find(plan => plan.planType === tier);
      if (!planTemplate) {
        return {
          profiles: 1,
          contacts: 10,
          documents: 5,
          links: 5,
          analytics: false,
          advancedAnalytics: false
        };
      }

      return {
        profiles: planTemplate.individualProfiles === -1 ? 'unlimited' : planTemplate.individualProfiles,
        contacts: 50, // Default contact limit
        documents: 5, // Default document limit
        links: planTemplate.nfcProductLinking === -1 ? 'unlimited' : planTemplate.nfcProductLinking,
        analytics: planTemplate.insightsAccess !== 'none',
        advancedAnalytics: planTemplate.insightsAccess === 'advanced' || planTemplate.insightsAccess === 'full' || planTemplate.insightsAccess === 'custom'
      };
    } catch (error) {
      logger.error('Error getting subscription limits:', error);
      return {
        profiles: 1,
        contacts: 50,
        documents: 5,
        links: 5,
        analytics: false,
        advancedAnalytics: false
      };
    }
  }

  async checkFeatureAccess(userId: string, feature: string): Promise<boolean> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return false;
      }

      switch (feature) {
        case 'analytics':
          return userPlan.features.insightsAccess !== 'none';
        case 'advanced_analytics':
          return userPlan.features.insightsAccess === 'advanced' || 
                 userPlan.features.insightsAccess === 'full' || 
                 userPlan.features.insightsAccess === 'custom';
        case 'qr_codes':
          return userPlan.features.qrCodeAccess !== 'basic';
        case 'nfc_links':
          return userPlan.limits.nfcProductLinking > 0;
        case 'team_roles':
          return userPlan.features.teamProxyRoles;
        case 'affiliate_program':
          return userPlan.features.affiliateProgram !== 'none';
        case 'addon_marketplace':
          return userPlan.features.addonMarketplace;
        case 'reward_system':
          return userPlan.features.rewardSystemAccess;
        case 'gamified_activities':
          return userPlan.features.gamifiedActivities;
        default:
          return false;
      }
    } catch (error) {
      logger.error('Error checking feature access:', error);
      return false;
    }
  }

  async checkProfileLimit(userId: string): Promise<{ allowed: boolean; current: number; max: number; percentage: number; nearingLimit: boolean; reason?: string }> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return { allowed: false, current: 0, max: 1, percentage: 0, nearingLimit: false, reason: 'No active subscription' };
      }

      const current = userPlan.usageStats.individualProfiles;
      const max = userPlan.limits.individualProfiles;

      const percentage = max === -1 ? 0 : Math.round((current / max) * 100);
      const nearingLimit = percentage >= 80;
      
      return {
        allowed: max === -1 || current < max,
        current,
        max: max === -1 ? -1 : max,
        percentage,
        nearingLimit,
        reason: max === -1 || current < max ? undefined : 'Profile limit reached'
      };
    } catch (error) {
      logger.error('Error checking profile limit:', error);
      return { allowed: false, current: 0, max: 1, percentage: 0, nearingLimit: false, reason: 'Error checking limit' };
    }
  }

  async checkContactLimit(userId: string): Promise<{ allowed: boolean; current: number; max: number; percentage: number; nearingLimit: boolean; reason?: string }> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      
      // Get all profiles for this user
      const { ProfileModel } = await import('../models/profile.model');
      const userProfiles = await ProfileModel.find({ owner: new mongoose.Types.ObjectId(userId) });
      const profileIds = userProfiles.map(profile => profile._id);

      // Count actual contacts from all user's profiles
      const { Contact } = await import('../models/Contact');
      const current = await Contact.countDocuments({ owner: { $in: profileIds } });
      const max = 50; // Default contact limit for all users
      const percentage = Math.round((current / max) * 100);
      const nearingLimit = percentage >= 80;

      return {
        allowed: current < max,
        current,
        max,
        percentage,
        nearingLimit,
        reason: current < max ? undefined : 'Contact limit reached'
      };
    } catch (error) {
      logger.error('Error checking contact limit:', error);
      return { allowed: false, current: 0, max: 50, percentage: 0, nearingLimit: false, reason: 'Error checking limit' };
    }
  }

  async checkDocumentLimit(userId: string): Promise<{ allowed: boolean; current: number; max: number; percentage: number; nearingLimit: boolean; reason?: string }> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return { allowed: false, current: 0, max: 5, percentage: 0, nearingLimit: false, reason: 'No active subscription' };
      }

      // Default document limit of 5 for all plans
      const current = 0; // TODO: Implement document counting
      const max = 5;
      const percentage = Math.round((current / max) * 100);
      const nearingLimit = percentage >= 80;

      return {
        allowed: current < max,
        current,
        max,
        percentage,
        nearingLimit,
        reason: current < max ? undefined : 'Document limit reached'
      };
    } catch (error) {
      logger.error('Error checking document limit:', error);
      return { allowed: false, current: 0, max: 5, percentage: 0, nearingLimit: false, reason: 'Error checking limit' };
    }
  }

  async checkLinkLimit(userId: string): Promise<{ allowed: boolean; current: number; max: number; percentage: number; nearingLimit: boolean; reason?: string }> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return { allowed: false, current: 0, max: 5, percentage: 0, nearingLimit: false, reason: 'No active subscription' };
      }

      const current = userPlan.usageStats.nfcLinksCreated;
      const max = userPlan.limits.nfcProductLinking;
      const percentage = max === -1 ? 0 : Math.round((current / max) * 100);
      const nearingLimit = percentage >= 80;

      return {
        allowed: max === -1 || current < max,
        current,
        max: max === -1 ? -1 : max,
        percentage,
        nearingLimit,
        reason: max === -1 || current < max ? undefined : 'Link limit reached'
      };
    } catch (error) {
      logger.error('Error checking link limit:', error);
      return { allowed: false, current: 0, max: 5, percentage: 0, nearingLimit: false, reason: 'Error checking limit' };
    }
  }

  async shouldShowUpgradePrompt(userId: string, feature: string): Promise<boolean> {
    try {
      const userPlan = await this.getUserActivePlan(userId);
      if (!userPlan) {
        return true; // Show upgrade prompt if no subscription
      }

      // Check if user is near limits or doesn't have access to the feature
      const limitChecks = await Promise.all([
        this.checkProfileLimit(userId),
        this.checkContactLimit(userId),
        this.checkDocumentLimit(userId),
        this.checkLinkLimit(userId)
      ]);

      const nearLimits = limitChecks.some(check => {
        if (check.max === -1) return false;
        const percentage = (check.current / check.max) * 100;
        return percentage >= 80; // Show prompt if usage is 80% or higher
      });

      const hasFeatureAccess = await this.checkFeatureAccess(userId, feature);

      return nearLimits || !hasFeatureAccess;
    } catch (error) {
      logger.error('Error checking upgrade prompt:', error);
      return false;
    }
  }
}

export const subscriptionService = new SubscriptionService();
export default subscriptionService;