import QRCode from 'qrcode';
import { ProfileModel } from '../models/profile.model';
import { AnalyticsService } from './analytics.service';
import { logger } from '../utils/logger';
import mongoose from 'mongoose';
import { createCanvas, loadImage } from 'canvas';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import CloudinaryService from './cloudinary.service';

export class SharingService {
  private analyticsService: AnalyticsService;
  private qrStoragePath: string;
  private cloudinaryService: CloudinaryService;

  constructor() {
    this.analyticsService = new AnalyticsService();
    this.qrStoragePath = path.join(process.cwd(), 'uploads', 'qr-codes');
    this.cloudinaryService = new CloudinaryService();
    this.ensureQRDirectory();
  }

  private async ensureQRDirectory() {
    try {
      await fs.mkdir(this.qrStoragePath, { recursive: true });
    } catch (error) {
      logger.error('Error creating QR code directory:', error);
    }
  }

  async generateProfileQR(
    profileId: mongoose.Types.ObjectId,
    options: {
      size?: number;
      color?: string;
      logo?: string;
      style?: 'standard' | 'dot' | 'square';
      type?: 'dynamic' | 'static';
    } = {}
  ) {
    try {
      // Populate the creator information to get fullName, firstName, lastName
      const profile = await ProfileModel.findById(profileId)
        .populate('profileInformation.creator', 'fullName firstName lastName email');
      
      if (!profile) {
        throw new Error('Profile not found');
      }

      const {
        size = 300,
        color = '#000000',
        style = 'standard',
        type = 'dynamic'
      } = options;

      let qrCodeBuffer: Buffer;
      let profileUrl: string;

      if (type === 'static') {
        // Generate static QR code with hardcoded profile information
        const staticData = this.generateStaticProfileData(profile);
        qrCodeBuffer = await QRCode.toBuffer(staticData, {
          errorCorrectionLevel: 'H' as const,
          type: 'png' as const,
          margin: 1,
          color: {
            dark: color,
            light: '#ffffff00',
          },
          width: size,
        });
        profileUrl = staticData; // For static QR, the URL is the data itself
      } else {
        // Generate dynamic QR code with share token for access control
        try {
          // Import ProfileShareLinkService to create a share token
          const { ProfileShareLinkService } = await import('./profile-share-link.service');
          const shareService = new ProfileShareLinkService();
          
          // Create a basic share link for QR code access
          const shareLink = await shareService.createShareLink(
            profileId.toString(),
            profile.profileInformation.creator.toString(),
            {
              accessLevel: 'basic',
              allowedSections: ['basic', 'social', 'contact'],
              customPermissions: {
                canViewContactInfo: true,
                canViewSocialLinks: true,
                canViewAnalytics: false,
                canViewVault: false,
                canDownload: false,
                canShare: true
              }
            }
          );
          
          // Use share token URL instead of direct profile URL
          profileUrl = `${process.env.FRONTEND_URL}/profile/shared/${shareLink.shareToken}`;
          logger.info(`Generated dynamic QR with share token: ${shareLink.shareToken}`);
          
        } catch (shareError) {
          // Fallback to direct URL if share link creation fails
          logger.warn(`Failed to create share link for QR code, using direct URL:`, shareError);
          profileUrl = `${process.env.FRONTEND_URL}/view/profile/${profile.profileInformation?.username || profile.profileInformation?.profileLink || profile._id}`;
        }
        
        qrCodeBuffer = await QRCode.toBuffer(profileUrl, {
          errorCorrectionLevel: 'H' as const,
          type: 'png' as const,
          margin: 1,
          color: {
            dark: color,
            light: '#ffffff00',
          },
          width: size,
        });
      }

      // Add logo if provided
      let finalQRCode = qrCodeBuffer;
      if (options.logo) {
        finalQRCode = await this.addLogoToQR(qrCodeBuffer, options.logo, size);
      }

      // Apply style
      if (style !== 'standard') {
        finalQRCode = await this.applyQRStyle(finalQRCode, style);
      }

      // For static QR codes, create a designed version with profile information
      if (type === 'static') {
        finalQRCode = await this.createDesignedStaticQR(finalQRCode, profile, size);
      }

      // Save QR code to Cloudinary using username for better organization
      const username = profile.profileInformation?.username || profile.profileInformation?.profileLink || profileId.toString();
      const qrType = type === 'static' ? 'static' : 'dynamic';
      const fileName = `qr-${qrType}-${username}-${Date.now()}`;
      const qrCodeBase64 = `data:image/png;base64,${finalQRCode.toString('base64')}`;
      
      logger.info(`Uploading ${qrType} QR code to Cloudinary for profile ${username} (${profileId})`);
      const uploadResult = await this.cloudinaryService.uploadImage(qrCodeBase64, {
        folder: 'qr-codes',
        tags: ['qr-code', 'profile', username, profileId.toString(), qrType]
      });
      
      logger.info(`${qrType} QR code uploaded to Cloudinary: ${uploadResult.secure_url}`);

      return {
        url: uploadResult.secure_url,
        profileUrl,
        type,
        staticData: type === 'static' ? this.generateStaticProfileData(profile) : null
      };
    } catch (error) {
      logger.error('Error generating QR code:', error);
      throw error;
    }
  }

  /**
   * Generate a profile link with "all" permissions using preview format
   * @param profileId The profile ID to generate link for
   * @returns Object containing profileLink and shareUrl
   */
  async generateProfileLink(profileId: mongoose.Types.ObjectId): Promise<{
    profileLink: string;
    shareUrl: string;
  }> {
    try {
      const profile = await ProfileModel.findById(profileId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      // Import ProfileShareLinkService to create a share token with "all" permissions
      const { ProfileShareLinkService } = await import('./profile-share-link.service');
      const shareService = new ProfileShareLinkService();
      
      // Create a share link with "all" permissions
      const shareLink = await shareService.createShareLink(
        profileId.toString(),
        profile.profileInformation.creator.toString(),
        {
          accessLevel: 'full',
          allowedSections: ['basic', 'contact', 'social', 'professional', 'education', 'experience', 'skills', 'projects', 'gallery'],
          customPermissions: {
            canViewContactInfo: true,
            canViewSocialLinks: true,
            canViewAnalytics: true,
            canViewVault: true,
            canDownload: true,
            canShare: true
          }
        }
      );

      // Generate profile link using preview format with share token
      const profileLink = `${process.env.FRONTEND_URL}/preview/${shareLink.shareToken}`;
      
      // Generate share URL using view format with share token
      const shareUrl = `${process.env.FRONTEND_URL}/view/${shareLink.shareToken}`;

      return {
        profileLink,
        shareUrl
      };
    } catch (error) {
      logger.error('Error generating profile link:', error);
      throw error;
    }
  }

  private async addLogoToQR(qrBuffer: Buffer, logoPath: string, size: number) {
    try {
      const canvas = createCanvas(size, size);
      const ctx = canvas.getContext('2d');

      // Draw QR code
      const qrImage = await loadImage(qrBuffer);
      ctx.drawImage(qrImage, 0, 0, size, size);

      // Load and draw logo
      const logo = await loadImage(logoPath);
      const logoSize = size * 0.2; // Logo takes up 20% of QR code
      const logoX = (size - logoSize) / 2;
      const logoY = (size - logoSize) / 2;
      ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);

      return canvas.toBuffer('image/png');
    } catch (error) {
      logger.error('Error adding logo to QR:', error);
      throw error;
    }
  }



  private async applyQRStyle(qrBuffer: Buffer, style: 'dot' | 'square') {
    try {
      const image = sharp(qrBuffer);

      if (style === 'dot') {
        // Apply circular mask to each QR code module
        return image
          .composite([{
            input: Buffer.from(`
              <svg>
                <defs>
                  <filter id="circular">
                    <feGaussianBlur stdDeviation="2" />
                    <feColorMatrix type="matrix"
                      values="1 0 0 0 0
                              0 1 0 0 0
                              0 0 1 0 0
                              0 0 0 9 -4"/>
                    <feComposite operator="atop" in="SourceGraphic"/>
                  </filter>
                </defs>
                <rect width="100%" height="100%" filter="url(#circular)"/>
              </svg>`
            ),
            blend: 'dest-in'
          }])
          .toBuffer();
      }

      // For square style, just return the original
      return qrBuffer;
    } catch (error) {
      logger.error('Error applying QR style:', error);
      throw error;
    }
  }

  async generateSharingImage(
    profileId: mongoose.Types.ObjectId,
    template: 'standard' | 'professional' | 'creative' = 'standard'
  ) {
    try {
      const profile = await ProfileModel.findById(profileId)
        .populate('profileInformation.creator', 'fullName firstName lastName email');
      
      if (!profile) {
        throw new Error('Profile not found');
      }

      const canvas = createCanvas(1200, 630); // Standard social sharing size
      const ctx = canvas.getContext('2d');

      // Apply template-specific styling
      await this.applyTemplate(ctx, template, profile);

      // Use username in filename for better organization
      const username = profile.profileInformation?.username || profile.profileInformation?.profileLink || profileId.toString();
      const fileName = `share-${username}-${Date.now()}.png`;
      const filePath = path.join(this.qrStoragePath, fileName);

      const buffer = canvas.toBuffer('image/png');
      await fs.writeFile(filePath, buffer);

      return {
        url: `/uploads/qr-codes/${fileName}`,
        socialMeta: this.generateSocialMeta(profile),
      };
    } catch (error) {
      logger.error('Error generating sharing image:', error);
      throw error;
    }
  }

  private async applyTemplate(
    ctx: any,
    template: string,
    profile: any
  ) {
    // Base styling
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, 1200, 630);

    switch (template) {
      case 'professional':
        await this.applyProfessionalTemplate(ctx, profile);
        break;
      case 'creative':
        await this.applyCreativeTemplate(ctx, profile);
        break;
      default:
        await this.applyStandardTemplate(ctx, profile);
    }
  }

  private async applyStandardTemplate(ctx: any, profile: any) {
    // Background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, 1200, 630);

    // Profile info
    const creator = profile.profileInformation?.creator as any;
    const creatorFullName = creator?.fullName || `${creator?.firstName || 'User'} ${creator?.lastName || ''}`.trim();
    
    ctx.font = 'bold 48px Arial';
    ctx.fillStyle = '#000000';
    ctx.fillText(creatorFullName, 60, 100);

    ctx.font = '32px Arial';
    ctx.fillStyle = '#666666';
    ctx.fillText(profile.profileInformation?.title || 'Profile', 60, 160);

    // Add profile image if available
    if (profile.ProfileFormat?.profileImage) {
      const img = await loadImage(profile.ProfileFormat.profileImage);
      ctx.drawImage(img, 800, 100, 300, 300);
    }
  }

  private async applyProfessionalTemplate(ctx: any, profile: any) {
    // Gradient background
    const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
    gradient.addColorStop(0, '#2c3e50');
    gradient.addColorStop(1, '#3498db');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1200, 630);

    // White text
    const creator = profile.profileInformation?.creator as any;
    const creatorFullName = creator?.fullName || `${creator?.firstName || 'User'} ${creator?.lastName || ''}`.trim();
    
    ctx.font = 'bold 56px Arial';
    ctx.fillStyle = '#ffffff';
    ctx.fillText(creatorFullName, 60, 120);

    ctx.font = '36px Arial';
    ctx.fillText(profile.profileInformation?.title || 'Profile', 60, 180);
  }

  private async applyCreativeTemplate(ctx: any, profile: any) {
    // Playful background
    ctx.fillStyle = '#ff6b6b';
    ctx.fillRect(0, 0, 1200, 630);

    // Add decorative elements
    ctx.beginPath();
    ctx.fillStyle = '#4ecdc4';
    ctx.arc(1000, 100, 80, 0, Math.PI * 2);
    ctx.fill();

    // White text with shadow
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 10;
    ctx.shadowOffsetX = 5;
    ctx.shadowOffsetY = 5;

    const creator = profile.profileInformation?.creator as any;
    const creatorFirstName = creator?.firstName || 'User';
    const creatorLastName = creator?.lastName || '';
    
    ctx.font = 'bold 64px Arial';
    ctx.fillStyle = '#ffffff';
    ctx.fillText(creatorFirstName, 60, 120);
    ctx.fillText(creatorLastName, 60, 200);
  }

  private generateSocialMeta(profile: any) {
    const creator = profile.profileInformation?.creator as any;
    const creatorFullName = creator?.fullName || `${creator?.firstName || 'User'} ${creator?.lastName || ''}`.trim();
    
    return {
      title: `${creatorFullName} - ${profile.profileInformation?.title || 'Profile'}`,
      description: profile.profileInformation?.title || `Check out this professional profile`,
      image: profile.ProfileFormat?.profileImage || '',
      url: `${process.env.FRONTEND_URL}/view/profile/${profile.profileInformation?.username || profile.profileInformation?.profileLink || profile._id}`,
    };
  }

  async trackShare(
    profileId: mongoose.Types.ObjectId,
    platform: 'linkedin' | 'twitter' | 'facebook' | 'email' | 'whatsapp' | 'qr',
    userId?: mongoose.Types.ObjectId
  ) {
    try {
      const profile = await ProfileModel.findById(profileId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      // Track share as an engagement
      await this.analyticsService.trackEngagement(
        profileId,
        profile.profileInformation?.creator,
        userId || profile.profileInformation?.creator,
        'share',
        { platform }
      );

      return true;
    } catch (error) {
      logger.error('Error tracking share:', error);
      throw error;
    }
  }

  /**
   * Generate static profile data for QR code (limited to essential info only)
   * Includes: profile information (name, username, title), essential links, and location
   */
  private generateStaticProfileData(profile: any): string {
    const profileInfo = profile.profileInformation || {};
    const creator = profileInfo.creator as any;
    
    // Get creator name information from populated user object
    const creatorFullName = creator?.fullName || `${creator?.firstName || 'User'} ${creator?.lastName || ''}`.trim();
    const creatorFirstName = creator?.firstName || 'User';
    const creatorLastName = creator?.lastName || '';
    
    // Start with basic vCard format - ESSENTIAL INFO ONLY
    const vCardLines = [
      'BEGIN:VCARD',
      'VERSION:3.0',
      `FN:${creatorFullName}`,
      `N:${creatorLastName};${creatorFirstName};;;`,
    ];

    // Add profile information (name, username, title)
    if (profileInfo.title) {
      vCardLines.push(`ORG:${profileInfo.title}`);
      vCardLines.push(`TITLE:${profileInfo.title}`);
    }
    
    // Profile URL
    vCardLines.push(`URL:${process.env.FRONTEND_URL}/view/profile/${profileInfo.username || profile._id}`);
    
    // Add creator email if available
    if (creator?.email) {
      vCardLines.push(`EMAIL:${creator.email}`);
    }

    // Add location information (if available)
    if (profile.profileLocation) {
      const location = profile.profileLocation;
      if (location.city || location.stateOrProvince || location.country) {
        const addressParts = [];
        if (location.city) addressParts.push(location.city);
        if (location.stateOrProvince) addressParts.push(location.stateOrProvince);
        if (location.country) addressParts.push(location.country);
        vCardLines.push(`ADR:;;${addressParts.join(', ')};;;;`);
      }
    }

    // Add basic contact information from profile sections (contact section only)
    if (profile.sections && Array.isArray(profile.sections)) {
      const contactSection = profile.sections.find((section: any) => 
        section && (section.key === 'contact' || section.key === 'basic')
      );
      
      if (contactSection && contactSection.fields && Array.isArray(contactSection.fields)) {
        contactSection.fields.forEach((field: any) => {
          if (field && field.enabled && field.value && field.value !== '') {
            // Only include essential contact fields
            switch (field.key) {
              case 'phone':
              case 'phoneNumber':
                vCardLines.push(`TEL:${field.value}`);
                break;
              case 'email':
                vCardLines.push(`EMAIL:${field.value}`);
                break;
              case 'website':
              case 'url':
                vCardLines.push(`URL:${field.value}`);
                break;
              case 'contactInformation':
                if (typeof field.value === 'object') {
                  if (field.value.phone) vCardLines.push(`TEL:${field.value.phone}`);
                  if (field.value.email) vCardLines.push(`EMAIL:${field.value.email}`);
                }
                break;
            }
          }
        });
      }
    }

    // Add essential links (limit to top 5 active links)
    if (profile.links && Array.isArray(profile.links)) {
      const activeLinks = profile.links
        .filter((link: any) => link && link.active && link.value && link.value.trim() !== '')
        .slice(0, 5); // Limit to first 5 active links
      
      activeLinks.forEach((link: any, index: number) => {
        // Use standard vCard fields for common link types, custom X- fields for others
        switch (link.name?.toLowerCase()) {
          case 'linkedin':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=linkedin:${link.value}`);
            break;
          case 'twitter':
          case 'x':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=twitter:${link.value}`);
            break;
          case 'facebook':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=facebook:${link.value}`);
            break;
          case 'instagram':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=instagram:${link.value}`);
            break;
          case 'youtube':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=youtube:${link.value}`);
            break;
          case 'website':
          case 'portfolio':
            vCardLines.push(`URL:${link.value}`);
            break;
          case 'github':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=github:${link.value}`);
            break;
          case 'whatsapp':
            vCardLines.push(`X-SOCIALPROFILE;TYPE=whatsapp:${link.value}`);
            break;
          default:
            // Generic link with label
            vCardLines.push(`X-LINK-${index + 1}:${link.value}`);
            if (link.label) {
              vCardLines.push(`X-LINK-${index + 1}-LABEL:${link.label}`);
            }
            break;
        }
      });
    }

    // Add essential custom fields (profile info)
    vCardLines.push(`X-PROFILE-ID:${profile._id}`);
    if (profileInfo.username) {
      vCardLines.push(`X-USERNAME:${profileInfo.username}`);
    }
    if (profileInfo.name) {
      vCardLines.push(`X-PROFILE-NAME:${profileInfo.name}`);
    }
    if (profile.profileType) {
      vCardLines.push(`X-PROFILE-TYPE:${profile.profileType}`);
    }

    // End vCard
    vCardLines.push('END:VCARD');

    return vCardLines.join('\r\n');
  }

  /**
   * Create a designed static QR code with profile information displayed
   */
  private async createDesignedStaticQR(qrBuffer: Buffer, profile: any, size: number): Promise<Buffer> {
    try {
      const profileInfo = profile.profileInformation || {};
      const creator = profileInfo.creator as any;
      
      // Get creator name information from populated user object
      const creatorFullName = creator?.fullName || `${creator?.firstName || 'User'} ${creator?.lastName || ''}`.trim();
      const creatorFirstName = creator?.firstName || 'User';
      const creatorLastName = creator?.lastName || '';
      
      // Create a larger canvas to accommodate the design
      const canvasWidth = size + 200; // Extra space for design
      const canvasHeight = size + 150;
      const canvas = createCanvas(canvasWidth, canvasHeight);
      const ctx = canvas.getContext('2d');

      // Background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // Add a subtle gradient background
      const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight);
      gradient.addColorStop(0, '#f8f9fa');
      gradient.addColorStop(1, '#e9ecef');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // Draw QR code in the center
      const qrImage = await loadImage(qrBuffer);
      const qrX = (canvasWidth - size) / 2;
      const qrY = 20;
      ctx.drawImage(qrImage, qrX, qrY, size, size);

      // Add profile information below the QR code
      ctx.fillStyle = '#000000';
      ctx.font = 'bold 18px Arial';
      ctx.textAlign = 'center';
      
      // Use the full name from the populated creator object
      ctx.fillText(creatorFullName, canvasWidth / 2, qrY + size + 30);

      if (profileInfo.title) {
        ctx.font = '14px Arial';
        ctx.fillStyle = '#666666';
        ctx.fillText(profileInfo.title, canvasWidth / 2, qrY + size + 50);
      }

      // Add username
      if (profileInfo.username) {
        ctx.font = '12px Arial';
        ctx.fillStyle = '#888888';
        ctx.fillText(`@${profileInfo.username}`, canvasWidth / 2, qrY + size + 70);
      }

      // Add a border around the QR code
      ctx.strokeStyle = '#dee2e6';
      ctx.lineWidth = 2;
      ctx.strokeRect(qrX - 5, qrY - 5, size + 10, size + 10);

      // Add a small logo or icon if available
      if (profile.ProfileFormat?.profileImage) {
        try {
          const profileImg = await loadImage(profile.ProfileFormat.profileImage);
          const logoSize = 40;
          const logoX = qrX + size - logoSize - 10;
          const logoY = qrY + 10;
          
          // Create circular mask for profile image
          ctx.save();
          ctx.beginPath();
          ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
          ctx.clip();
          ctx.drawImage(profileImg, logoX, logoY, logoSize, logoSize);
          ctx.restore();
          
          // Add border around profile image
          ctx.strokeStyle = '#ffffff';
          ctx.lineWidth = 3;
          ctx.beginPath();
          ctx.arc(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 0, Math.PI * 2);
          ctx.stroke();
        } catch (error) {
          logger.warn('Could not load profile image for QR design:', error);
        }
      }

      return canvas.toBuffer('image/png');
    } catch (error) {
      logger.error('Error creating designed static QR:', error);
      // Return original QR code if design fails
      return qrBuffer;
    }
  }
}
