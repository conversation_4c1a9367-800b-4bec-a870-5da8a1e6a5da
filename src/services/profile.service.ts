import { ProfileModel as Profile, ProfileDocument } from '../models/profile.model';
import { isValidObjectId } from 'mongoose';
import createHttpError from 'http-errors';
import { logger } from '../utils/logger';
import { User } from '../models/User';
import { ProfileTemplate, ProfileType, ProfileCategory } from '../models/profiles/profile-template';
import { generateUniqueConnectLink, generateReferralCode, generateSecondaryId } from '../utils/crypto';
import { generateProfileGradient } from '../utils/gradient-generator';
import mongoose from 'mongoose';
import { ITemplateField } from '../models/profiles/profile-template';
import { FieldWidget } from '../models/profiles/profile-template';
import geoip from 'geoip-lite';
import { ProfileFilter } from '../types/profiles';
import { getDefaultProfileSettings, UpdateDefaultProfileSettings } from '../models/profile-types/default-settings';
import { ActivityTrackingService } from './activity-tracking.service';
import { getActivityTracker } from './activity-tracker.service';
import { UnavailablePeriodExpansionService } from './unavailable-period-expansion.service';
import { SlotGenerationService, TimeSlot } from './slot-generation.service';
import { AvailabilityService } from './availability.service';
import { getContentLogoImageUrl } from '../models/ContentLogoImages';
import CloudinaryService from './cloudinary.service';
import { RoleType } from '../models/Role';
// import { SharingService } from './sharing.service'; // Commented out to avoid Sharp dependency issues at startup
import { AccountService } from './account.service';
import { subscriptionService } from './subscription.service';

export class ProfileService {
  private availabilityService: AvailabilityService;

  constructor() {
    this.availabilityService = new AvailabilityService();
  }

  /**
   * Creates a profile with content in one step
   * @param userId The user ID creating the profile
   * @param templateId The template ID to base the profile on
   * @param profileInformation Basic profile information
   * @param sections Optional sections with field values and enabled status
   * @param members Optional array of member IDs for group profiles
   * @param location Optional location information for the profile
   * @param ip Optional IP address for geolocation
   * @param groups Optional array of group IDs for group profiles
   * @returns The created profile document
   */
  async createProfileWithContent(
    userId: string,
    templateId: string,
    profileInformation: {
      username: string;
      title?: string;
      accountHolder?: string;
      pid?: string;
      relationshipToAccountHolder?: string;
    },
    sections?: Array<{
      key: string;
      label: string;
      fields: Array<{
        key: string;
        value: any;
        enabled: boolean;
      }>;
    }>,
    members?: string[],
    location?: {
      city?: string;
      stateOrProvince?: string;
      country?: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    },
    ip?: string,
    groups?: string[]
  ): Promise<ProfileDocument> {
    logger.info(`Creating profile with content for user ${userId} using template ${templateId}`);

    // Create the base profile with all parameters
    const profile = await this.createProfile(userId, templateId, members, location, ip, groups);

    // Update profile information
    if (profileInformation) {
      profile.profileInformation.username = profileInformation.username;

      if (profileInformation.title) profile.profileInformation.title = profileInformation.title;
      if (profileInformation.accountHolder) profile.profileInformation.accountHolder = profileInformation.accountHolder;
      if (profileInformation.pid) profile.profileInformation.pid = profileInformation.pid;
      if (profileInformation.relationshipToAccountHolder)
        profile.profileInformation.relationshipToAccountHolder = profileInformation.relationshipToAccountHolder;
    }

    // Update sections and fields if provided
    if (sections && sections.length > 0) {
      // For each provided section
      for (const providedSection of sections) {
        // Find matching section in profile
        const profileSection = profile.sections.find(s => s.key === providedSection.key);
        if (profileSection) {
          // Update fields in the section
          for (const providedField of providedSection.fields) {
            const profileField = profileSection.fields.find(f => f.key === providedField.key);
            if (profileField) {
              // Use type assertion to allow property assignment
              (profileField as any).value = providedField.value;
              profileField.enabled = providedField.enabled;
            }
          }
        }
      }
    }

    // Save the updated profile
    await profile.save();

    const defaultProfileSettings = getDefaultProfileSettings(profile.profileType);
    // Use type assertion to access _id since we know it exists after save
    await UpdateDefaultProfileSettings((profile as any)._id.toString(), defaultProfileSettings);

    logger.info(`Profile with content created successfully: ${(profile as any)._id}`);
    return profile;
  }

  /**
   * Creates a new profile based on a template
   * @param userId The user ID creating the profile
   * @param templateId The template ID to base the profile on
   * @param members Optional array of member IDs for group profiles
   * @param location Optional location information for the profile
   * @param ip Optional IP address for geolocation
   * @param groups Optional array of group IDs for group profiles
   * @returns The created profile document
   */
  async createProfile(
    userId: string,
    templateId: string,
    members: string[] = [],
    location?: {
      city?: string;
      stateOrProvince?: string;
      country?: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    },
    ip?: string,
    groups?: string[]
  ): Promise<ProfileDocument> {
    logger.info(`Creating new profile for user ${userId} using template ${templateId}`);

    // Validate inputs
    if (!isValidObjectId(userId) || !isValidObjectId(templateId)) {
      throw createHttpError(400, 'Invalid user ID or template ID');
    }

    // Get the template
    const template = await ProfileTemplate.findById(templateId).lean();
    if (!template) {
      throw createHttpError(404, 'Template not found');
    }

    // Generate unique links
    const [connectLink, profileLink] = await Promise.all([
      generateUniqueConnectLink(),
      generateUniqueConnectLink()
    ]);

    // Generate a unique referral link
    const referralCode = generateReferralCode();
    const referralLink = `mypts-ref-${referralCode}`;

    // Generate a unique secondary ID
    const secondaryId = await generateSecondaryId(async (id: string) => {
      const existingProfile = await Profile.findOne({ secondaryId: id });
      return !existingProfile;
    });

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      throw createHttpError(404, 'User not found');
    }

    // Get user's location if not provided
    let profileLocation = location;
    if (!profileLocation) {
      profileLocation = {
        country: user.countryOfResidence
      };

      // Try to get coordinates from IP if available
      if (ip) {
        const geo = geoip.lookup(ip);
        if (geo) {
          profileLocation = {
            ...profileLocation,
            city: geo.city,
            stateOrProvince: geo.region,
            country: geo.country || undefined,
            coordinates: {
              latitude: geo.ll[0],
              longitude: geo.ll[1]
            }
          };
        }
      }
    }

    // Get user data for profile username and title
    const rawProfileUsername = user?.fullName || user?.username || '';
    // Clean username by removing spaces and special characters, but keep it readable
    const profileUsername = rawProfileUsername
      .replace(/[^a-zA-Z0-9]/g, '') // Remove special characters
      .toLowerCase(); // Make lowercase for consistency

    // For personal profiles, set the title to the creator's full name
    const profileTitle = template.profileType === 'personal' 
      ? (user?.fullName || user?.username || 'Personal Profile')
      : '';

    // For personal profiles, also set the name field to the creator's full name
    const profileName = template.profileType === 'personal' 
      ? (user?.fullName || user?.username || 'Personal Profile')
      : undefined;

    // Get country information from user
    const userCountry = user?.countryOfResidence || '';
    // Simple country code mapping for common countries (can be expanded)
    const countryCodeMap: Record<string, string> = {
      'United States': 'US',
      'Canada': 'CA',
      'United Kingdom': 'GB',
      'Australia': 'AU',
      'Germany': 'DE',
      'France': 'FR',
      'Italy': 'IT',
      'Spain': 'ES',
      'Japan': 'JP',
      'China': 'CN',
      'India': 'IN',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'South Africa': 'ZA',
      'Nigeria': 'NG',
      'Kenya': 'KE',
      'Ghana': 'GH',
      'Cameroon': 'CM'
    };
    const countryCode = countryCodeMap[userCountry] || '';

    // Generate a unique gradient background based on the username
    const { gradient, primaryColor, secondaryColor } = generateProfileGradient(profileUsername);

    // Create initial sections from template with all fields disabled by default
    const initialSections = template.categories.map(category => ({
      key: category.name,
      label: category.label || category.name,
      icon: category.icon || '',
      collapsible: category.collapsible !== false,
      fields: category.fields.map(field => ({
        key: field.name,
        label: field.label || field.name,
        widget: field.widget || 'text',
        required: field.required || false,
        placeholder: field.placeholder || '',
        enabled: field.enabled !== false,
        value: field.default || null,
        options: field.options || [],
        validation: field.validation || {},
        icon: field.icon || "",
        order: field.order || 0,
        isFavourite: field.isFavourite || false
      }))
    }));

    // Add members and groups fields to info section for group profiles
    if (template.profileCategory === 'group' || template.profileType === 'group') {
      const infoSection = initialSections.find(s => s.key === 'info');
      if (infoSection) {
        // Add members field if it doesn't exist
        if (!infoSection.fields.some(f => f.key === 'members')) {
          infoSection.fields.push({
            key: 'members',
            label: 'Members',
            widget: 'multiselect',
            required: false,
            placeholder: '',
            enabled: true,
            value: [],
            options: [],
            validation: {},
            icon: "",
            order: 0,
            isFavourite: false
          });
        }
        // Add groups field if it doesn't exist
        if (!infoSection.fields.some(f => f.key === 'groups')) {
          infoSection.fields.push({
            key: 'groups',
            label: 'Groups',
            widget: 'multiselect',
            required: false,
            placeholder: '',
            enabled: true,
            value: [],
            options: [],
            validation: {},
            icon: "",
            order: 0,
            isFavourite: false
          });
        }
      }
    }

    // Create initial links from template - flatten to match Profile model schema
    const initialLinks: Array<{
      name: string;
      label: string;
      category: string;
      icon?: string;
      baseUrl?: string;
      urlPattern?: string;
      placeholder?: string;
      value?: string;
      enabled: boolean;
      active: boolean;
      order: number;
      description?: string;
      isFavourite?: boolean;
    }> = [];
    
    // Debug logging for template links
    logger.info(`Template ID: ${templateId}, Template found: ${!!template}`);
    logger.info(`Template links: ${JSON.stringify(template.links)}`);
    logger.info(`Template links length: ${template.links ? template.links.length : 'undefined'}`);
    
    // Flatten template links from nested groups to individual link objects
    if (template.links && Array.isArray(template.links)) {
      logger.info(`Processing ${template.links.length} link groups from template`);
      template.links.forEach((linkGroup, groupIndex) => {
        logger.info(`Processing link group ${groupIndex}: ${JSON.stringify(linkGroup)}`);
        if (linkGroup && linkGroup.category && linkGroup.links && Array.isArray(linkGroup.links)) {
          logger.info(`Link group ${groupIndex} has category "${linkGroup.category}" with ${linkGroup.links.length} links`);
          linkGroup.links.forEach(link => {
            const flattenedLink = {
              name: link.name,
              label: link.label,
              category: linkGroup.category, // Category from the parent group
              icon: link.icon,
              baseUrl: link.baseUrl,
              urlPattern: link.urlPattern,
              placeholder: link.placeholder,
              value: "", // Empty value initially
              enabled: link.enabled !== false,
              active: false, // Not active by default until user sets a value
              order: link.order || 0,
              description: link.description,
              isFavourite: link.isFavourite || false
            };
            initialLinks.push(flattenedLink);
            logger.info(`Added link "${link.name}" with category "${linkGroup.category}" to profile`);
          });
        } else {
          logger.warn(`Link group ${groupIndex} is invalid: category=${linkGroup?.category}, has links array=${Array.isArray(linkGroup?.links)}`);
        }
      });
    } else {
      logger.warn(`Template has no links or links is not an array: ${typeof template.links}`);
    }
    
    logger.info(`Total profile links created: ${initialLinks.length}`);
    logger.info(`Profile links structure: ${JSON.stringify(initialLinks)}`);

    // Sync icons with latest URLs from ContentLogoImage database
    await this.syncIconsWithDatabase(initialSections, initialLinks);

    // Create the profile with appropriate group/member handling
    const profile = new Profile({
      profileCategory: template.profileCategory,
      profileType: template.profileType,
      secondaryId,
      templatedId: (template._id as mongoose.Types.ObjectId),
      profileInformation: {
        username: profileUsername,
        title: profileTitle,
        name: profileName, // Set name for personal profiles
        profileLink: profileLink,
        creator: new mongoose.Types.ObjectId(userId),
        connectLink,
        followLink: profileLink,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      ProfileFormat: {
        profileImage: '', // Initialize with empty string
        customization: {
          theme: {
            background: '#ffffff',
            text: '#212121',
          },
          layout: {
            gridStyle: 'centered' // Default layout style
          }
        },
        updatedAt: new Date()
      },
      profileLocation: {
        ...profileLocation,
        country: userCountry,
        countryCode: countryCode
      },
      ProfileReferal: {
        referalLink: referralLink,
        referals: 0
      },
      sections: initialSections,
      links: initialLinks,
      members: [], // Initialize empty members array
      groups: [] // Initialize empty groups array
    }) as ProfileDocument & { members: mongoose.Types.ObjectId[]; groups: mongoose.Types.ObjectId[] };



    // Handle group profiles and members
    if (template.profileCategory === 'group' || template.profileType === 'group') {
      // Add the creator as a member by default
      profile.members = [new mongoose.Types.ObjectId(userId)];

      // Add any provided members from the members parameter
      if (members && members.length > 0) {
        const validMemberIds = members.filter(id => isValidObjectId(id));
        profile.members = [...profile.members, ...validMemberIds.map(id => new mongoose.Types.ObjectId(id))];

        // Also add to the members field in info section if it exists
        const infoSection = profile.sections.find(s => s.key === 'info');
        if (infoSection) {
          const membersField = infoSection.fields.find(f => f.key === 'members');
          if (membersField) {
            (membersField as any).value = profile.members;
          }
        }
      }

      // Add any provided groups from the groups parameter
      if (groups && groups.length > 0) {
        const validGroupIds = groups.filter(id => isValidObjectId(id));
        profile.groups = validGroupIds.map(id => new mongoose.Types.ObjectId(id));

        // Also add to the groups field in info section if it exists
        const infoSection = profile.sections.find(s => s.key === 'info');
        if (infoSection) {
          const groupsField = infoSection.fields.find(f => f.key === 'groups');
          if (groupsField) {
            (groupsField as any).value = profile.groups;
          }
        }
      }
    }

    await profile.save();
    logger.info(`Profile created successfully: ${profile._id}`);

    // Generate QR code for the profile (optional - won't fail profile creation if Sharp is not available)
    try {
      // Dynamically import SharingService to avoid startup issues if Sharp is not available
      const { SharingService } = await import('./sharing.service');
      const sharingService = new SharingService();
      
      // Generate both dynamic and static QR codes
      const [dynamicQR, staticQR] = await Promise.all([
        sharingService.generateProfileQR(profile._id, {
          size: 300,
          color: '#000000',
          style: 'standard',
          type: 'dynamic'
        }),
        sharingService.generateProfileQR(profile._id, {
          size: 300,
          color: '#000000',
          style: 'standard',
          type: 'static'
        })
      ]);

      // Update the profile with both QR code URLs
      profile.ProfileQrCode = {
        ...(profile.ProfileQrCode || {}),
        qrCode: dynamicQR.url,
        staticQrCode: staticQR.url
      };
      await profile.save();
      
      logger.info(`Dynamic and static QR codes generated and saved to Cloudinary for profile: ${profile._id}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.warn(`QR code generation skipped for profile ${profile._id} (dependency issue):`, errorMessage);
      // Don't throw the error to avoid disrupting profile creation
      // The profile is still created successfully, just without QR code
    }

    return profile;
  }

  /**
   * Enables/disables fields in a profile
   * @param profileId The profile ID
   * @param userId The user ID
   * @param enabledFields Array of field keys to enable/disable
   * @returns The updated profile document
   */
  async setEnabledFields(
    profileId: string,
    userId: string,
    enabledFields: Array<{
      sectionKey: string;
      fieldKey: string;
      enabled: boolean;
    }>
  ): Promise<ProfileDocument> {
    logger.info(`Updating enabled fields for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get user data to check if they're an admin
    const user = await User.findById(userId);

    // // Verify user has permission to update (either creator or admin)
    // if (profile.profileInformation.creator.toString() !== userId &&
    //     (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
    //   throw createHttpError(403, 'You do not have permission to update this profile');
    // }

    // Update field enabled status
    enabledFields.forEach(({ sectionKey, fieldKey, enabled }) => {
      const section = profile.sections.find(s => s.key === sectionKey);
      if (section) {
        const field = section.fields.find(f => f.key === fieldKey);
        if (field) {
          field.enabled = enabled;
        }
      }
    });

    // CRITICAL FIX: Mark sections as modified so Mongoose saves the changes
    profile.markModified('sections');
    profile.profileInformation.updatedAt = new Date();
    await profile.save();
    logger.info(`Enabled fields updated for profile ${profileId}`);
    return profile;
  }

  /**
   * Updates profile content for enabled fields
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param updates Object containing field updates
   * @returns The updated profile document
   */
  async updateProfileContent(
    profileId: string,
    userId: string,
    updates: Array<{
      sectionKey: string;
      fieldKey: string;
      value: any;
    }>
  ): Promise<ProfileDocument> {
    logger.info(`Updating content for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get user data to check if they're an admin
    const user = await User.findById(userId);

    console.log(profile.profileInformation.creator.toString(), userId);
    // Verify user has permission to update (either creator or admin)
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Get template for validation
    const template = await ProfileTemplate.findById(profile.templatedId);
    if (!template) {
      throw createHttpError(404, 'Template not found');
    }

    // Validate and apply updates
    console.log('===== DEBUG UPDATES =====');
    console.log('Updates received:', updates);
    console.log('Updates type:', typeof updates);
    console.log('Updates is array:', Array.isArray(updates));
    console.log('Updates length:', updates?.length);
    if (updates && updates.length > 0) {
      console.log('First update item:', updates[0]);
      console.log('First update keys:', Object.keys(updates[0] || {}));
    }
    console.log('========================');

    updates.forEach(({ sectionKey, fieldKey, value }) => {
      const section = profile.sections.find(s => s.key === sectionKey);
      if (!section) {
        throw createHttpError(400, `Invalid section: ${sectionKey}`);
      }

      const field = section.fields.find(f => f.key === fieldKey);
      if (!field) {
        throw createHttpError(400, `Invalid field: ${fieldKey} in section ${sectionKey}`);
      }

      // Auto-enable field if it's not enabled
      if (!field.enabled) {
        logger.info(`Auto-enabling field ${fieldKey} in section ${sectionKey} for profile ${profileId}`);
        field.enabled = true;
      }

      // Validate against template field type if needed
      const templateSection = template.categories.find(c => c.name === sectionKey);
      const templateField = templateSection?.fields.find(f => f.name === fieldKey);
      if (templateField) {
        this.validateFieldValue(fieldKey, value, templateField);
      }

      // Type assertion to allow value assignment
      (field as any).value = value;
    });

    // Mark the sections array as modified so Mongoose saves the changes
    profile.markModified('sections');
    profile.profileInformation.updatedAt = new Date();
    await profile.save();
    logger.info(`Profile content updated for ${profileId}`);
    return profile;
  }

  /**
   * Updates profile information (username, title, accountHolder, etc.)
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param profileInformation Basic profile information to update
   * @returns The updated profile document
   */
  async updateProfileInformation(
    profileId: string,
    userId: string,
    profileInformation: {
      username?: string;
      title?: string;
      accountHolder?: string;
      relationshipToAccountHolder?: string;
    }
  ): Promise<ProfileDocument> {
    logger.info(`Updating profile information for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Verify user has permission to update
    const user = await User.findById(userId);
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Update profile information fields
    const updates = {
      username: profileInformation.username,
      title: profileInformation.title,
      accountHolder: profileInformation.accountHolder,
      relationshipToAccountHolder: profileInformation.relationshipToAccountHolder
    };

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        (profile.profileInformation as any)[key] = value;
      }
    });

    profile.profileInformation.updatedAt = new Date();
    await profile.save();
    logger.info(`Profile information updated for ${profileId}`);
    return profile;
  }

  /**
   * Updates profile location information
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param profileLocation Location information to update
   * @returns The updated profile document
   */
  async updateProfileLocation(
    profileId: string,
    userId: string,
    profileLocation: {
      city?: string;
      stateOrProvince?: string;
      country?: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    }
  ): Promise<ProfileDocument> {
    logger.info(`Updating profile location for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Verify user has permission to update
    const user = await User.findById(userId);
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Update location fields
    if (profileLocation && profile.profileLocation) {
      const locationUpdates = {
        city: profileLocation.city,
        stateOrProvince: profileLocation.stateOrProvince,
        country: profileLocation.country,
        coordinates: profileLocation.coordinates
      };

      Object.entries(locationUpdates).forEach(([key, value]) => {
        if (value !== undefined) {
          (profile.profileLocation as any)[key] = value;
        }
      });
    }

    profile.profileInformation.updatedAt = new Date();
    await profile.save();
    logger.info(`Profile location updated for ${profileId}`);
    return profile;
  }

  /**
   * Updates profile format information (images, customization, etc.)
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param ProfileFormat Format information to update
   * @returns The updated profile document
   */
  async updateProfileFormat(
    profileId: string,
    userId: string,
    ProfileFormat: {
      profileImage?: string;
      coverImage?: string;
      profileLogo?: string;
      customization?: {
        theme?: {
          primaryColor?: string;
          secondaryColor?: string;
          accent?: string;
          background?: string;
          text?: string;
          font?: string;
        };
        layout?: {
          sections?: Array<{ id: string; type: string; order: number; visible: boolean }>;
          gridStyle?: 'right-sided' | 'centered' | 'left-sided';
          animation?: 'fade' | 'slide' | 'zoom';
        };
      };
      customCSS?: string;
      updatedAt: Date;
    }
  ): Promise<ProfileDocument> {
    logger.info(`Updating profile format for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Verify user has permission to update
    const user = await User.findById(userId);
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Update format fields
    if (ProfileFormat && profile.ProfileFormat) {
      const formatUpdates = {
        profileImage: ProfileFormat.profileImage,
        coverImage: ProfileFormat.coverImage,
        profileLogo: ProfileFormat.profileLogo,
        customCSS: ProfileFormat.customCSS
      };

      Object.entries(formatUpdates).forEach(([key, value]) => {
        if (value !== undefined) {
          (profile.ProfileFormat as any)[key] = value;
        }
      });

      // Update customization if provided
      if (ProfileFormat.customization && profile.ProfileFormat.customization) {
        if (ProfileFormat.customization.theme && profile.ProfileFormat.customization.theme) {
          profile.ProfileFormat.customization.theme = ProfileFormat.customization.theme;
        }
        if (ProfileFormat.customization.layout) {
          profile.ProfileFormat.customization.layout = ProfileFormat.customization.layout;
        }
      }
    }

    profile.profileInformation.updatedAt = new Date();
    await profile.save();
    logger.info(`Profile format updated for ${profileId}`);
    return profile;
  }

  /**
   * Comprehensive profile update method that handles all types of updates
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param profileInformation Optional basic profile information
   * @param profileLocation Optional location information
   * @param ProfileFormat Optional format information
   * @param updates Optional field updates
   * @returns The updated profile document
   */
  async updateProfileComprehensive(
    profileId: string,
    userId: string,
    profileInformation?: {
      username?: string;
      title?: string;
      accountHolder?: string;
      relationshipToAccountHolder?: string;
    },
    profileLocation?: {
      city?: string;
      stateOrProvince?: string;
      country?: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    },
    ProfileFormat?: {
      profileImage?: string;
      coverImage?: string;
      profileLogo?: string;
      customization?: {
        theme?: {
          primaryColor?: string;
          secondaryColor?: string;
          accent?: string;
          background?: string;
          text?: string;
          font?: string;
        };
        layout?: {
          sections?: Array<{ id: string; type: string; order: number; visible: boolean }>;
          gridStyle?: 'right-sided' | 'centered' | 'left-sided';
          animation?: 'fade' | 'slide' | 'zoom';
        };
      };
      customCSS?: string;
      updatedAt: Date;
    },
    updates?: Array<{
      sectionKey: string;
      fieldKey: string;
      value: any;
    }>
  ): Promise<ProfileDocument> {
    logger.info(`Performing comprehensive update for profile ${profileId}`);

    let updatedProfile: ProfileDocument | null = await Profile.findById(profileId);
    if (!updatedProfile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Update profile information if provided
    if (profileInformation) {
      updatedProfile = await this.updateProfileInformation(profileId, userId, profileInformation) as ProfileDocument;
    }

    // Update profile location if provided
    if (profileLocation) {
      updatedProfile = await this.updateProfileLocation(profileId, userId, profileLocation) as ProfileDocument;
    }

    // Update profile format if provided
    if (ProfileFormat) {
      updatedProfile = await this.updateProfileFormat(profileId, userId, ProfileFormat) as ProfileDocument;
    }

    // Update field content if provided
    if (updates && updates.length > 0) {
      updatedProfile = await this.updateProfileContent(profileId, userId, updates);
    }

    // Ensure we return a ProfileDocument (not null)
    if (!updatedProfile) {
      throw createHttpError(404, 'Profile not found after update');
    }

    return updatedProfile;
  }

  /**
   * Validates field value based on template field type
   * @param fieldKey The field key for error messages
   * @param value The value to validate
   * @param templateField The template field definition
   */
  private validateFieldValue(fieldKey: string, value: any, templateField: any): void {
    switch (templateField.widget) {
      case 'number':
        if (typeof value !== 'number') {
          throw createHttpError(400, `Field ${fieldKey} must be a number`);
        }
        if (templateField.validation?.min !== undefined && value < templateField.validation.min) {
          throw createHttpError(400, `Field ${fieldKey} must be at least ${templateField.validation.min}`);
        }
        if (templateField.validation?.max !== undefined && value > templateField.validation.max) {
          throw createHttpError(400, `Field ${fieldKey} must be at most ${templateField.validation.max}`);
        }
        break;
      case 'email':
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          throw createHttpError(400, `Field ${fieldKey} must be a valid email`);
        }
        break;
      // Add other validation cases as needed
    }
  }

  /**
   * Gets a profile by ID
   * @param profileId The profile ID
   * @param skipAutoSync Optional flag to skip auto-sync with template (default: false)
   * @returns The profile document
   */
  async getProfile(profileId: string, skipAutoSync: boolean = false): Promise<ProfileDocument> {
    logger.info(`Fetching profile ${profileId}${skipAutoSync ? ' (skipping auto-sync)' : ''}`);

    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    const profile = await Profile.findById(profileId)
      .populate('profileInformation.creator', 'fullName firstName lastName email')
      .populate('members', 'profileInformation.username profileInformation.title _id')
      .populate('groups', 'profileInformation.username profileInformation.title _id');

    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // **AUTO-SYNC WITH TEMPLATE**: Ensure profile structure matches current template
    // Skip auto-sync if explicitly requested (e.g., right after field updates)
    if (!skipAutoSync) {
      try {
        await this.syncProfileWithTemplate(profileId);
        logger.info(`Auto-synced profile ${profileId} with template`);
      } catch (syncError) {
        logger.warn(`Failed to auto-sync profile ${profileId} with template:`, syncError);
        // Continue loading profile even if sync fails
      }

      // Re-fetch profile after sync to get updated structure
      const syncedProfile = await Profile.findById(profileId)
        .populate('profileInformation.creator', 'fullName firstName lastName email')
        .populate('members', 'profileInformation.username profileInformation.title _id')
        .populate('groups', 'profileInformation.username profileInformation.title _id');

      if (!syncedProfile) {
        throw createHttpError(404, 'Profile not found after sync');
      }

      // Process sections to ensure all fields are properly enabled and have values
      syncedProfile.sections.forEach(section => {
        section.fields.forEach(field => {
          if (field.value === null && field.default) {
            field.value = field.default;
          }
        });
      });

      // Process members and groups if they exist
      if (syncedProfile.members && syncedProfile.members.length > 0) {
        const membersSection = syncedProfile.sections.find(s => s.key === 'info');
        if (membersSection) {
          const membersField = membersSection.fields.find(f => f.key === 'members');
          if (membersField) {
            membersField.value = syncedProfile.members.map(member => ({
              id: member._id,
              name: (member as any).profileInformation?.title || (member as any).profileInformation?.username
            }));
            membersField.enabled = true;
          }
        }
      }

      if (syncedProfile.groups && syncedProfile.groups.length > 0) {
        const groupsSection = syncedProfile.sections.find(s => s.key === 'info');
        if (groupsSection) {
          const groupsField = groupsSection.fields.find(f => f.key === 'groups');
          if (groupsField) {
            groupsField.value = syncedProfile.groups.map(group => ({
              id: group._id,
              name: (group as any).profileInformation?.title || (group as any).profileInformation?.username
            }));
            groupsField.enabled = true;
          }
        }
      }

      return syncedProfile;
    } else {
      // Skip sync, just process the current profile
      // Process sections to ensure all fields are properly enabled and have values
      profile.sections.forEach(section => {
        section.fields.forEach(field => {
          if (field.value === null && field.default) {
            field.value = field.default;
          }
        });
      });

      // Process members and groups if they exist
      if (profile.members && profile.members.length > 0) {
        const membersSection = profile.sections.find(s => s.key === 'info');
        if (membersSection) {
          const membersField = membersSection.fields.find(f => f.key === 'members');
          if (membersField) {
            membersField.value = profile.members.map(member => ({
              id: member._id,
              name: (member as any).profileInformation?.title || (member as any).profileInformation?.username
            }));
            membersField.enabled = true;
          }
        }
      }

      if (profile.groups && profile.groups.length > 0) {
        const groupsSection = profile.sections.find(s => s.key === 'info');
        if (groupsSection) {
          const groupsField = groupsSection.fields.find(f => f.key === 'groups');
          if (groupsField) {
            groupsField.value = profile.groups.map(group => ({
              id: group._id,
              name: (group as any).profileInformation?.title || (group as any).profileInformation?.username
            }));
            groupsField.enabled = true;
          }
        }
      }

      return profile;
    }
  }

  /**
   * Gets all profiles for a user
   * @param userId The user ID
   * @returns Array of profile documents
   */
  async getUserProfiles(userId: string): Promise<ProfileDocument[]> {
    logger.info(`Fetching all profiles for user ${userId}`);

    if (!isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid user ID');
    }

    return await Profile.find({ 'profileInformation.creator': userId })
      .populate('profileInformation.creator', 'fullName firstName lastName email');
  }

  /**
   * Gets the first profile for a user (typically used for referral processing)
   * @param userId The user ID
   * @returns The profile document or null if not found
   */
  async getProfileByUserId(userId: string): Promise<ProfileDocument | null> {
    logger.info(`Fetching first profile for user ${userId}`);

    if (!isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid user ID');
    }

    return await Profile.findOne({ 'profileInformation.creator': userId })
      .populate('profileInformation.creator', 'fullName firstName lastName email');
  }

  /**
   * Deletes a profile
   * @param profileId The profile ID
   * @param userId The user ID requesting deletion
   * @returns Boolean indicating success
   */
  async deleteProfile(profileId: string, userId: string): Promise<boolean> {
    logger.info(`Deleting profile ${profileId} by user ${userId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get user data to check if they're an admin
    const user = await User.findById(userId);

    // Verify user has permission to delete (either creator or admin)
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to delete this profile');
    }

    // Clean up QR code from Cloudinary if it exists
    try {
      if (profile.ProfileQrCode?.qrCode && profile.ProfileQrCode.qrCode.includes('cloudinary')) {
        const cloudinaryService = new CloudinaryService();
        await cloudinaryService.delete(profile.ProfileQrCode.qrCode);
        logger.info(`QR code deleted from Cloudinary for profile ${profileId}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.warn(`Failed to delete QR code from Cloudinary for profile ${profileId}:`, errorMessage);
      // Continue with profile deletion even if QR code cleanup fails
    }

    const result = await Profile.deleteOne({ _id: profileId });
    return result.deletedCount > 0;
  }

  /**
   * Get all profiles with pagination and filtering (admin only)
   * @param filter Filter criteria
   * @param skip Number of documents to skip
   * @param limit Maximum number of documents to return
   * @returns Array of profile documents
   */
  async getAllProfiles(filter: any = {}, skip = 0, limit = 20): Promise<ProfileDocument[]> {
    logger.info(`Fetching all profiles with filter: ${JSON.stringify(filter)}, skip: ${skip}, limit: ${limit}`);

    let query = Profile.find(filter).sort({ 'profileInformation.createdAt': -1 });
    if (limit > 0) {
      query = query.skip(skip).limit(limit);
          } else {
      // If limit is 0 or negative, do not apply limit (return all)
      if (skip > 0) {
        query = query.skip(skip);
      }
      // No .limit() call, so all profiles are returned
    }
    return await query;
  }

  /**
   * Count profiles matching a filter
   * @param filter Filter criteria
   * @returns Count of matching profiles
   */
  async countProfiles(filter: any = {}): Promise<number> {
    logger.info(`Counting profiles with filter: ${JSON.stringify(filter)}`);

    return await Profile.countDocuments(filter);
  }

  /**
   * Updates a profile's username and description
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param username The new username for the profile
   * @param description Optional description for the profile
   * @returns The updated profile document
   */
  async updateProfileBasicInfo(
    profileId: string,
    userId: string,
    username: string,
    description?: string
  ): Promise<ProfileDocument> {
    logger.info(`Updating basic info for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    if (!username || username.trim() === '') {
      throw createHttpError(400, 'Username is required');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get user data to check if they're an admin and for fullName
    const user = await User.findById(userId);
    if (!user) {
      throw createHttpError(404, 'User not found');
    }

    // Verify user has permission to update (either creator or admin)
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Update the profile username with the provided username
    profile.profileInformation.username = username;
    profile.profileInformation.updatedAt = new Date();

    // If description is provided, update it using the updateProfileContent method
    if (description !== undefined) {
      try {
        // Find the basic section that contains the bio/description field
        const basicSection = profile.sections.find(s =>
          s.key === 'basic' ||
          s.fields.some(f => f.key === 'bio' || f.key === 'description')
        );

        if (basicSection) {
          // Find the bio/description field
          const bioField = basicSection.fields.find(f =>
            f.key === 'bio' || f.key === 'description'
          );

          if (bioField) {
            // Use the existing updateProfileContent method to update the field
            // First, ensure the field is enabled
            await this.setEnabledFields(profileId, userId, [
              {
                sectionKey: basicSection.key,
                fieldKey: bioField.key,
                enabled: true
              }
            ]);

            // Then update the content
            await this.updateProfileContent(profileId, userId, [
              {
                sectionKey: basicSection.key,
                fieldKey: bioField.key,
                value: description
              }
            ]);

            logger.info(`Updated description for profile ${profileId}`);
          } else {
            logger.warn(`Could not find bio/description field in profile ${profileId}`);
          }
        } else {
          // If the section doesn't exist, we can't add the description
          logger.warn(`Could not find basic section in profile ${profileId} to update description`);
        }
      } catch (error) {
        logger.error(`Error updating description for profile ${profileId}:`, error);
        // Don't throw the error to avoid disrupting the username update
      }
    }

    await profile.save();
    
    // Track profile update activity
    try {
      const activityTracker = getActivityTracker();
      await activityTracker.trackProfileUpdate(profileId, {
        fieldUpdated: 'username',
        sectionCompleted: 'basic'
      });
    } catch (error) {
      logger.error(`Error tracking profile update activity for profile ${profileId}:`, error);
    }
    
    logger.info(`Basic info updated for profile ${profileId}`);
    return profile;
  }

  /**
   * Creates a default personal profile for a new user
   * @param userId The user ID
   * @param userObject Optional user object to avoid additional database query
   * @returns The created profile document
   */
  async createDefaultProfile(userId: string, userObject?: any): Promise<ProfileDocument> {
    logger.info(`Creating default personal profile for user ${userId}`);

    // Get user data - use provided user object if available to avoid race conditions
    const user = userObject || await User.findById(userId);
    if (!user) {
      throw createHttpError(404, 'User not found');
    }

    // **CRITICAL FIX: Check if user already has a personal profile to prevent duplicates**
    const existingPersonalProfile = await Profile.findOne({
      'profileInformation.creator': userId,
      profileType: 'personal',
      profileCategory: 'individual'
    });

    if (existingPersonalProfile) {
      logger.info(`User ${userId} already has a personal profile: ${existingPersonalProfile._id}. Returning existing profile.`);

      // Update user's profiles array if needed
      if (!user.profiles || !user.profiles.includes(existingPersonalProfile._id)) {
        if (!user.profiles) user.profiles = [];
        user.profiles.push(existingPersonalProfile._id);
        await user.save();
        logger.info(`Updated user's profiles array with existing personal profile`);
      }

      // Return the existing personal profile
      return existingPersonalProfile;
    }

    // Get the default personal profile template
    let template = await ProfileTemplate.findOne({
      profileType: 'personal',
      profileCategory: 'individual'
    });

    // If template doesn't exist, create it
    if (!template) {
      logger.info('Default personal profile template not found, creating one...');

      // Create a default admin ID (this is required by the schema)
      const adminId = new mongoose.Types.ObjectId();

      // Create the default personal profile template
      template = await ProfileTemplate.create({
        profileCategory: 'individual',
        profileType: 'personal',
        name: 'Personal Profile',
        slug: 'personal-profile',
        createdBy: adminId,
        categories: [
          {
            name: 'basic',
            label: 'Basic Information',
            icon: 'user',
            collapsible: true,
            fields: [
              {
                name: 'fullName',
                label: 'Full Name',
                widget: 'text',
                order: 1,
                enabled: false,
                required: true,
                placeholder: 'Enter your full name',
                icon: 'user'
              },
              {
                name: 'bio',
                label: 'Bio',
                widget: 'textarea',
                order: 2,
                enabled: false,
                required: false,
                placeholder: 'Tell us about yourself',
                icon: 'info'
              },
              {
                name: 'dateOfBirth',
                label: 'Date of Birth',
                widget: 'date',
                order: 3,
                enabled: false,
                required: false,
                icon: 'calendar'
              },
              {
                name: 'gender',
                label: 'Gender',
                widget: 'select',
                order: 4,
                enabled: false,
                required: false,
                icon: 'users',
                options: [
                  { label: 'Male', value: 'male' },
                  { label: 'Female', value: 'female' },
                  { label: 'Non-binary', value: 'non-binary' },
                  { label: 'Prefer not to say', value: 'not-specified' }
                ]
              }
            ]
          },
          {
            name: 'about',
            label: 'About',
            icon: 'info-circle',
            collapsible: true,
            fields: [
              {
                name: 'aboutMyself',
                label: 'About myself',
                widget: 'textarea',
                order: 1,
                enabled: false,
                icon: 'ContentLogoImages.aboutmyself'
              },
              {
                name: 'origin',
                label: 'Origin',
                widget: 'object',
                order: 2,
                enabled: false,
                icon: 'ContentLogoImages.origin'
              },
              {
                name: 'inspirationalQuote',
                label: 'Inspirational quote',
                widget: 'textarea',
                order: 3,
                enabled: false,
                icon: 'ContentLogoImages.quote'
              },
              {
                name: 'interestOrGoals',
                label: 'Interest or Goals',
                widget: 'textarea',
                order: 4,
                enabled: false,
                icon: 'ContentLogoImages.interest'
              },
              {
                name: 'biography',
                label: 'Biography',
                widget: 'textarea',
                order: 5,
                enabled: false,
                icon: 'ContentLogoImages.biography'
              },
              {
                name: 'needsAndWishes',
                label: 'Needs & Wishes',
                widget: 'textarea',
                order: 6,
                enabled: false,
                icon: 'ContentLogoImages.needs'
              },
              {
                name: 'medical',
                label: 'Medical',
                widget: 'object',
                order: 7,
                enabled: false,
                icon: 'ContentLogoImages.medical'
              },
              {
                name: 'hobbies',
                label: 'Hobbies',
                widget: 'list:text',
                order: 8,
                enabled: false,
                icon: 'ContentLogoImages.hobbies'
              },
              {
                name: 'importantLocation',
                label: 'Important Location',
                widget: 'object',
                order: 9,
                enabled: false,
                icon: 'ContentLogoImages.location'
              },
              {
                name: 'religion',
                label: 'Religion',
                widget: 'object',
                order: 10,
                enabled: false,
                icon: 'ContentLogoImages.religion'
              },
              {
                name: 'gallery',
                label: 'Gallery',
                widget: 'image',
                order: 11,
                enabled: false,
                icon: 'ContentLogoImages.gallery'
              }
            ]
          },
          {
            name: 'contact',
            label: 'Contact',
            icon: 'address-book',
            collapsible: true,
            fields: [
              {
                name: 'contactInformation',
                label: 'Contact information',
                widget: 'object',
                order: 1,
                enabled: false,
                icon: 'ContentLogoImages.contactinfo'
              },
              {
                name: 'emergency',
                label: 'Emergency',
                widget: 'object',
                order: 2,
                enabled: false,
                icon: 'ContentLogoImages.emergency'
              },
              {
                name: 'meansToContact',
                label: 'Means to contact',
                widget: 'select',
                order: 3,
                enabled: false,
                icon: 'ContentLogoImages.contactmethod',
                options: [
                  { label: 'Email', value: 'email' },
                  { label: 'Phone', value: 'phone' },
                  { label: 'Text', value: 'text' },
                  { label: 'Social Media', value: 'social' }
                ]
              },
              {
                name: 'messagingPlatforms',
                label: 'Messaging Platforms',
                widget: 'multiselect',
                order: 4,
                enabled: false,
                icon: 'ContentLogoImages.messageapp',
                options: [
                  { label: 'WhatsApp', value: 'whatsapp' },
                  { label: 'Telegram', value: 'telegram' },
                  { label: 'Signal', value: 'signal' },
                  { label: 'Messenger', value: 'messenger' }
                ]
              },
              {
                name: 'myContacts',
                label: 'My Contacts',
                widget: 'list:text',
                order: 5,
                enabled: false,
                icon: 'ContentLogoImages.contactapp'
              },
              {
                name: 'contactsPlatforms',
                label: 'Contacts platforms',
                widget: 'multiselect',
                order: 6,
                enabled: false,
                icon: 'ContentLogoImages.contactapp',
                options: [
                  { label: 'Google Contacts', value: 'google' },
                  { label: 'iCloud', value: 'icloud' },
                  { label: 'Outlook', value: 'outlook' }
                ]
              },
              {
                name: 'availability',
                label: 'Availability',
                widget: 'object',
                order: 7,
                enabled: false,
                icon: 'ContentLogoImages.socailpartner'
              },
              {
                name: 'calendarPlatforms',
                label: 'Calendar platforms',
                widget: 'multiselect',
                order: 8,
                enabled: false,
                icon: 'ContentLogoImages.calender',
                options: [
                  { label: 'Google Calendar', value: 'google' },
                  { label: 'Outlook Calendar', value: 'outlook' },
                  { label: 'Apple Calendar', value: 'apple' }
                ]
              },
              {
                name: 'bookingPlatforms',
                label: 'Booking platforms',
                widget: 'multiselect',
                order: 9,
                enabled: false,
                icon: 'ContentLogoImages.booking',
                options: [
                  { label: 'Calendly', value: 'calendly' },
                  { label: 'Acuity', value: 'acuity' },
                  { label: 'Bookly', value: 'bookly' }
                ]
              }
            ]
          },
          {
            name: 'social',
            label: 'Social',
            icon: 'share-alt',
            collapsible: true,
            fields: [
              {
                name: 'socialMediaPlatforms',
                label: 'Social media platforms',
                widget: 'object',
                order: 1,
                enabled: false,
                icon: 'ContentLogoImages.socialmedia'
              },
              {
                name: 'holidaysObservances',
                label: 'Holidays & Observances',
                widget: 'object',
                order: 2,
                enabled: false,
                icon: 'ContentLogoImages.website'
              },
              {
                name: 'paymentPayoutPlatforms',
                label: 'Payment & Payout Platforms',
                widget: 'object',
                order: 3,
                enabled: false,
                icon: 'ContentLogoImages.payments'
              },
              {
                name: 'insurancePlatform',
                label: 'Insurance platform',
                widget: 'object',
                order: 4,
                enabled: false,
                icon: 'ContentLogoImages.insurance'
              },
              {
                name: 'connections',
                label: 'Connections',
                widget: 'profile_reference',
                order: 5,
                enabled: false,
                icon: 'ContentLogoImages.connection'
              },
              {
                name: 'affiliations',
                label: 'Affiliations',
                widget: 'profile_reference',
                order: 6,
                enabled: false,
                icon: 'ContentLogoImages.affiliations'
              },
              {
                name: 'videoStreamingPlatforms',
                label: 'Video Streaming Platforms',
                widget: 'list:text',
                order: 7,
                enabled: false,
                icon: 'ContentLogoImages.streaming'
              },
              {
                name: 'videoSharingPlatforms',
                label: 'Video sharing platforms',
                widget: 'list:text',
                order: 8,
                enabled: false,
                icon: 'ContentLogoImages.video'
              },
              {
                name: 'gamesPlatforms',
                label: 'Games platforms',
                widget: 'list:text',
                order: 9,
                enabled: false,
                icon: 'ContentLogoImages.game'
              }
            ]
          }
        ]
      });

      logger.info(`Created default personal profile template: ${(template._id as mongoose.Types.ObjectId).toString()}`);
    }

    const templateId = (template._id as mongoose.Types.ObjectId).toString();

    // Create the profile
    const profile = await this.createProfile(userId, templateId);

    // Track profile creation activity
    try {
      const activityTracker = getActivityTracker();
      await activityTracker.trackActivity({
        profileId: profile._id.toString(),
        activityType: 'profile_created',
        metadata: {
          templateId,
          source: 'default_creation',
          timestamp: new Date()
        }
      });
    } catch (error) {
      logger.error(`Error tracking profile creation activity for profile ${profile._id}:`, error);
    }

    // Initialize referral code for the new profile
    try {
      const { ProfileReferralService } = require('./profile-referral.service');
      await ProfileReferralService.initializeReferralCode(profile._id);
      logger.info(`Initialized referral code for profile: ${profile._id}`);
    } catch (error) {
      logger.error(`Error initializing referral code for profile ${profile._id}:`, error);
      // Don't throw the error to avoid disrupting the profile creation process
    }

    // **AUTOMATIC MYPTS REWARD FOR JOINING PLATFORM**
    try {
      logger.info(`🎯 Starting platform join reward for profile ${profile._id}`);
      const activityTrackingService = new ActivityTrackingService();
      const rewardResult = await activityTrackingService.trackActivity(
        profile._id,
        'platform_join',
        {
          userId,
          profileId: profile._id.toString(),
          timestamp: new Date(),
          description: 'Welcome bonus for joining the platform'
        }
      );

      logger.info(`🎯 Platform join reward result:`, rewardResult);

      if (rewardResult.success && rewardResult.pointsEarned > 0) {
        logger.info(`✅ Awarded ${rewardResult.pointsEarned} MyPts to profile ${profile._id} for joining platform`);
      } else {
        logger.warn(`❌ Platform join reward failed or 0 points earned:`, rewardResult);
      }
    } catch (error) {
      logger.error(`❌ Error awarding MyPts for platform join to profile ${profile._id}:`, error);
      // Don't throw the error to avoid disrupting the profile creation process
    }

    // Add profile to user's profiles array
    await User.findByIdAndUpdate(userId, {
      $addToSet: { profiles: profile._id }
    });

    // Create a referral record for the profile
    try {
      const { ProfileReferralService } = require('./profile-referral.service');
      await ProfileReferralService.getProfileReferral(profile._id);
      logger.info(`Created referral record for profile: ${profile._id}`);

      // Check if the user was referred (has a valid referral code)
      // Always check for referral code, whether from normal registration or social auth
      // First check for temporary referral code (from registration process)
      // Important: We prioritize tempReferralCode over referralCode because referralCode might be the user's own code
      const referralCode = user.tempReferralCode;
      logger.info(`Checking referral code for user ${userId}: ${referralCode}`);

      // Log the entire user object for debugging
      logger.info(`User object for debugging: ${JSON.stringify({
        id: user._id,
        email: user.email,
        referralCode: user.referralCode,
        tempReferralCode: user.tempReferralCode
      })}`);

      if (referralCode && typeof referralCode === 'string' && referralCode.trim() !== '') {
        try {
          // Validate the referral code
          const referringProfileId = await ProfileReferralService.validateReferralCode(referralCode);

          if (referringProfileId) {
            // Process the referral and retry if it fails initially
            let referralProcessed = await ProfileReferralService.processReferral(profile._id, referringProfileId);

            // If first attempt fails, wait briefly and retry once
            if (!referralProcessed) {
              await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
              referralProcessed = await ProfileReferralService.processReferral(profile._id, referringProfileId);
            }

            if (referralProcessed) {
              logger.info(`Successfully processed referral for profile ${profile._id} with referral code ${referralCode}`);

              // NOTE: Referral rewards are automatically handled by ProfileReferralService.processReferral
              // No need to duplicate the reward logic here

              // Initialize referral record for the new profile
              await ProfileReferralService.initializeReferralCode(profile._id);
            } else {
              logger.error(`Failed to process referral for profile ${profile._id} with referral code ${referralCode} after retry`);
            }
          } else {
            logger.warn(`Invalid referral code provided: ${referralCode}`);
          }
        } catch (referralError) {
          logger.error(`Error processing referral for profile ${profile._id}:`, referralError);
          // Continue profile creation even if referral processing fails
        }
      } else {
        logger.info(`No referral code found for user ${userId}`);
      }
    } catch (error) {
      logger.error(`Error creating referral record for profile ${profile._id}:`, error);
      // Don't throw the error to avoid disrupting the profile creation process
    }

    return profile;
  }

  /**
   * Set profile availability with cross-profile conflict detection
   */
  async setAvailability(profileId: string, userId: string, availabilityData: any): Promise<any> {
    return await this.availabilityService.setAvailability(profileId, userId, availabilityData);
  }

  /**
   * Update profile availability with conflict resolution
   */
  async updateAvailability(profileId: string, userId: string, updates: any, forceUpdate: boolean = false): Promise<any> {
    return await this.availabilityService.updateAvailability(profileId, userId, updates, forceUpdate);
  }

  /**
   * Get profile availability
   */
  async getAvailability(profileId: string): Promise<any> {
    return await this.availabilityService.getAvailability(profileId);
  }

  /**
   * Get available slots for a specific date with cross-profile conflict filtering
   */
  async getAvailableSlots(profileId: string, userId: string, date: Date, options?: any): Promise<TimeSlot[]> {
    return await this.availabilityService.getAvailableSlots(profileId, userId, date, options);
  }

  /**
   * Get all user profiles with their availability status
   */
  async getUserProfilesAvailability(userId: string): Promise<Array<{
    profileId: string;
    profileName: string;
    profileType: string;
    isPersonalProfile: boolean;
    availability: any;
  }>> {
    return await this.availabilityService.getUserProfilesAvailability(userId);
  }

  /**
   * Sync availability across all user profiles (for personal profile changes)
   */
  async syncAvailabilityAcrossProfiles(userId: string, personalProfileId: string, unavailablePeriods: any[]): Promise<void> {
    return await this.availabilityService.syncAvailabilityAcrossProfiles(userId, personalProfileId, unavailablePeriods);
  }

  /**
   * Find the next available slot for a profile
   */
  async findNextAvailableSlot(
    profileId: string,
    fromDate: Date = new Date(),
    minDuration: number = 30
  ): Promise<TimeSlot | null> {
    // Use SlotGenerationService directly since it has the method
    const { SlotGenerationService } = require('./slot-generation.service');
    return await SlotGenerationService.findNextAvailableSlot(profileId, fromDate, minDuration);
  }

  /**
   * Get unavailable periods for a specific date range
   * Updated to use the new expansion service for better performance
   */
  async getUnavailablePeriodsForDateRange(profileId: string, startDate: Date, endDate: Date): Promise<Array<{start: Date, end: Date, reason?: string, type: string}>> {
    logger.info(`Getting unavailable periods for profile ${profileId} from ${startDate} to ${endDate}`);
    
    // Validate date range (limit to 90 days for performance)
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff > 90) {
      throw createHttpError(400, 'Date range cannot exceed 90 days');
    }

    // Use the new expansion service for better performance
    return await UnavailablePeriodExpansionService.getUnavailablePeriodsForDateRange(profileId, startDate, endDate);
  }

  /**
   * Get all unavailable periods for a profile (without date range filtering)
   */
  async getUnavailablePeriods(profileId: string): Promise<Array<{start: Date, end: Date, reason?: string, type: string}>> {
    logger.info(`Getting all unavailable periods for profile ${profileId}`);
    
    // Get the next 90 days by default
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 90);

    return await UnavailablePeriodExpansionService.getUnavailablePeriodsForDateRange(profileId, startDate, endDate);
  }

  private getDayName(dayIndex: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayIndex];
  }

  /**
   * Fetches community profiles with filters for location and other criteria
   * @param filters Object containing filter criteria (town, city, country, etc.)
   * @param skip Number of documents to skip
   * @param limit Maximum number of documents to return
   * @returns Array of community profile documents with minimal sections
   */
  async getCommunityProfiles(filters: ProfileFilter = {}, skip = 0, limit = 20): Promise<ProfileDocument[]> {
    logger.info(`Fetching community profiles with filters: ${JSON.stringify(filters)}, skip: ${skip}, limit: ${limit}`);

    // Build the filter query
    const query: any = {
      profileCategory: 'group',
      profileType: 'community'
    };

    // Type filter
    if (filters.profileType) {
      query['profileType'] = filters.profileType;
    }
    // Access filter (if present in schema)
    if (filters.accessType) {
      query['accessType'] = filters.accessType;
    }

    // Created by filter
    if (filters.createdBy) {
      query['profileInformation.creator'] = filters.createdBy;
    }

    // Viewed/Not viewed filter (example: analytics.Networking.views or a views array)
    if (filters.viewed === 'viewed') {
      query['analytics.Networking.views'] = { $gt: 0 };
    } else if (filters.viewed === 'not_viewed') {
      query['$or'] = [
        { 'analytics.Networking.views': { $exists: false } },
        { 'analytics.Networking.views': 0 }
      ];
    }

    // Location filters
    if (filters.city) {
      query['profileLocation.city'] = { $regex: new RegExp(filters.city, 'i') };
    }
    if (filters.stateOrProvince) {
      query['profileLocation.stateOrProvince'] = { $regex: new RegExp(filters.stateOrProvince, 'i') };
    }
    if (filters.country) {
      query['profileLocation.country'] = { $regex: new RegExp(filters.country, 'i') };
    }
    if (filters.town) {
      query['$or'] = [
        { 'profileLocation.city': { $regex: new RegExp(filters.town, 'i') } },
        { 'profileLocation.stateOrProvince': { $regex: new RegExp(filters.town, 'i') } }
      ];
    }
    // Geospatial
    if (filters.latitude && filters.longitude && filters.radius) {
      query['profileLocation.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [filters.longitude, filters.latitude]
          },
          $maxDistance: filters.radius * 1000 // Convert km to meters
        }
      };
    }

    // Tag filter (if tags array or in sections.fields)
    if (filters.tag) {
      query['$or'] = [
        { 'tags': filters.tag },
        { 'sections.fields': { $elemMatch: { key: 'tag', value: filters.tag } } }
      ];
    }

    // Verification status
    if (filters.verificationStatus === 'verified') {
      query['verificationStatus.isVerified'] = true;
    } else if (filters.verificationStatus === 'not_verified') {
      query['verificationStatus.isVerified'] = false;
    }

    // Creation date filter (e.g., 'last_24_hours', 'last_7_days', 'last_30_days', 'last_365_days')
    if (filters.creationDate) {
      const now = new Date();
      let fromDate: Date | null = null;
      switch (filters.creationDate) {
        case 'last_24_hours':
          fromDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'last_7_days':
          fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'last_30_days':
          fromDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'last_365_days':
          fromDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          fromDate = null;
      }
      if (fromDate) {
        query['profileInformation.createdAt'] = { $gte: fromDate };
      }
    }

    // Group/member filter
    if (filters.groupId) {
      query['groups'] = filters.groupId;
    }
    if (filters.memberId) {
      query['members'] = filters.memberId;
    }

    // Keyword search
    if (filters.keyword) {
      query['$or'] = [
        { 'profileInformation.username': { $regex: new RegExp(filters.keyword, 'i') } },
        { 'profileInformation.title': { $regex: new RegExp(filters.keyword, 'i') } },
        { 'sections.fields.value': { $regex: new RegExp(filters.keyword, 'i') } },
        { 'profileLocation.city': { $regex: new RegExp(filters.keyword, 'i') } },
        { 'profileLocation.stateOrProvince': { $regex: new RegExp(filters.keyword, 'i') } },
        { 'profileLocation.country': { $regex: new RegExp(filters.keyword, 'i') } }
      ];
    }

    // Sorting
    const sort: any = {};
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'name':
          sort['profileInformation.username'] = filters.sortOrder === 'desc' ? -1 : 1;
          break;
        case 'createdAt':
          sort['profileInformation.createdAt'] = filters.sortOrder === 'desc' ? -1 : 1;
          break;
        case 'members':
          sort['members'] = filters.sortOrder === 'desc' ? -1 : 1;
          break;
        case 'groups':
          sort['groups'] = filters.sortOrder === 'desc' ? -1 : 1;
          break;
        default:
          sort['profileInformation.createdAt'] = -1;
      }
    } else {
      sort['profileInformation.createdAt'] = -1;
    }

    // Fetch profiles
    const profiles = await Profile.find(query)
      .select({
        'profileInformation': 1,
        'sections': 1,
        'profileLocation': 1,
        'members': 1,
        'groups': 1,
        'verificationStatus': 1,
        'analytics': 1,
        'tags': 1
      })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    logger.info(`Found ${profiles.length} community profiles`);
    return profiles as unknown as ProfileDocument[];
  }

  /**
   * Delete duplicate personal profiles for users, keeping only profiles with non-zero MYPTS balance
   * or the most recent one if all have zero balance
   * @returns Summary of deletion results
   */
  async deleteDuplicatePersonalProfiles(): Promise<{
    totalUsersProcessed: number;
    totalProfilesDeleted: number;
    usersWithDuplicates: number;
    deletionDetails: Array<{
      userId: string;
      profilesFound: number;
      profilesDeleted: number;
      keptProfile: {
        id: string;
        name: string;
        balance: number;
        createdAt: Date;
      };
      deletedProfiles: Array<{
        id: string;
        name: string;
        balance: number;
        createdAt: Date;
      }>;
    }>;
  }> {
    logger.info('Starting duplicate personal profile deletion process');
    
    const deletionDetails: Array<{
      userId: string;
      profilesFound: number;
      profilesDeleted: number;
      keptProfile: {
        id: string;
        name: string;
        balance: number;
        createdAt: Date;
      };
      deletedProfiles: Array<{
        id: string;
        name: string;
        balance: number;
        createdAt: Date;
      }>;
    }> = [];

    let totalUsersProcessed = 0;
    let totalProfilesDeleted = 0;
    let usersWithDuplicates = 0;

    // Find all users with personal profiles
    const usersWithProfiles = await Profile.aggregate([
      { $match: { profileType: 'personal' } },
      { $group: { _id: '$profileInformation.creator', count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } }
    ]);

    logger.info(`Found ${usersWithProfiles.length} users with duplicate personal profiles`);

    for (const userGroup of usersWithProfiles) {
      const userId = userGroup._id;
      totalUsersProcessed++;

      // Get all personal profiles for this user
      const userProfiles = await Profile.find({
        'profileInformation.creator': userId,
        profileType: 'personal'
      }).sort({ 'profileInformation.createdAt': 1 });

      if (userProfiles.length > 1) {
        usersWithDuplicates++;
        
        // Keep the profile with the highest MyPts balance, or the oldest one if balances are equal
        const profileToKeep = userProfiles.reduce((best, current) => {
          const bestBalance = (best as any).ProfileMypts?.currentBalance || 0;
          const currentBalance = (current as any).ProfileMypts?.currentBalance || 0;
          
          if (currentBalance > bestBalance) {
            return current;
          } else if (currentBalance === bestBalance) {
            // If balances are equal, keep the oldest profile
            return best.profileInformation.createdAt < current.profileInformation.createdAt ? best : current;
          }
          return best;
        });

        // Delete the duplicate profiles
        const profilesToDelete = userProfiles.filter(p => p._id.toString() !== profileToKeep._id.toString());
        
        const deletedProfiles = [];
        for (const profile of profilesToDelete) {
          deletedProfiles.push({
            id: profile._id.toString(),
            name: profile.profileInformation.username || 'Unknown',
            balance: (profile as any).ProfileMypts?.currentBalance || 0,
            createdAt: profile.profileInformation.createdAt
          });
          
          await Profile.findByIdAndDelete(profile._id);
          totalProfilesDeleted++;
        }

        deletionDetails.push({
          userId: userId.toString(),
          profilesFound: userProfiles.length,
          profilesDeleted: profilesToDelete.length,
          keptProfile: {
            id: profileToKeep._id.toString(),
            name: profileToKeep.profileInformation.username || 'Unknown',
            balance: (profileToKeep as any).ProfileMypts?.currentBalance || 0,
            createdAt: profileToKeep.profileInformation.createdAt
          },
          deletedProfiles
        });

        logger.info(`Processed user ${userId}: kept 1 profile, deleted ${profilesToDelete.length} duplicates`);
      }
    }

    logger.info(`Deletion process completed. Processed ${totalUsersProcessed} users, deleted ${totalProfilesDeleted} profiles`);
    
    return {
      totalUsersProcessed,
      totalProfilesDeleted,
      usersWithDuplicates,
      deletionDetails
    };
  }

  /**
   * Gets the profile structure (sections and fields) for debugging
   * @param profileId The profile ID
   * @returns The profile structure with available sections and fields
   */
  async getProfileStructure(profileId: string): Promise<{
    sections: Array<{
      key: string;
      label: string;
      fields: Array<{
        key: string;
        label: string;
        enabled: boolean;
      }>;
    }>;
  }> {
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const sections = profile.sections?.map(section => ({
      key: section.key,
      label: section.label,
      fields: section.fields?.map(field => ({
        key: field.key,
        label: field.label,
        enabled: field.enabled
      })) || []
    })) || [];

    return { sections };
  }

  /**
   * Syncs a profile's structure with its template to add missing sections/fields
   * @param profileId The profile ID to sync
   * @returns The updated profile document
   */
  async syncProfileWithTemplate(profileId: string): Promise<ProfileDocument> {
    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get the current template
    const template = await ProfileTemplate.findById(profile.templatedId);
    if (!template) {
      logger.warn(`Template not found for profile ${profileId}, skipping sync`);
      return profile;
    }

    let hasChanges = false;
    const syncLog = {
      profileId,
      templateId: (template._id as mongoose.Types.ObjectId).toString(),
      templateName: template.name,
      sectionsAdded: [] as string[],
      fieldsAdded: [] as string[],
      fieldsUpdated: [] as string[],
      linksAdded: [] as string[]
    };

    // Create a map of existing sections for quick lookup
    const existingSections = new Map(profile.sections.map(section => [section.key, section]));

    // Process template categories to update profile sections
    const updatedSections = template.categories.map(category => {
      const existingSection = existingSections.get(category.name);
      
      if (existingSection) {
        // Update existing section with new template metadata
        const updatedSection = {
          ...existingSection,
          label: category.label || existingSection.label,
          icon: category.icon || existingSection.icon,
          collapsible: category.collapsible !== undefined ? category.collapsible : existingSection.collapsible
        };

        // Create a map of existing fields for quick lookup
        const existingFields = new Map(existingSection.fields.map(field => [field.key, field]));

        // Process template fields to update section fields
        updatedSection.fields = category.fields.map(templateField => {
          const existingField = existingFields.get(templateField.name);
          
          if (existingField) {
            // Update existing field with new template metadata, preserve user data
            const updatedField = {
              ...existingField,
              label: templateField.label || existingField.label,
              widget: templateField.widget || existingField.widget,
              required: templateField.required !== undefined ? templateField.required : existingField.required,
              placeholder: templateField.placeholder || existingField.placeholder,
              options: templateField.options || existingField.options,
              validation: templateField.validation || existingField.validation,
              icon: templateField.icon || (existingField as any).icon || '',
              order: templateField.order !== undefined ? templateField.order : (existingField as any).order || 0,
              isFavourite: templateField.isFavourite !== undefined ? templateField.isFavourite : (existingField as any).isFavourite || false
            };

            // Check if field metadata changed
            if (JSON.stringify(updatedField) !== JSON.stringify(existingField)) {
              hasChanges = true;
              syncLog.fieldsUpdated.push(`${category.name}.${templateField.name}`);
            }

            return updatedField;
          } else {
            // Add new field from template
            hasChanges = true;
            syncLog.fieldsAdded.push(`${category.name}.${templateField.name}`);
            return {
              key: templateField.name,
              label: templateField.label || templateField.name,
              widget: templateField.widget || 'text',
              required: templateField.required || false,
              placeholder: templateField.placeholder || '',
              enabled: templateField.enabled !== undefined ? templateField.enabled : false,
              value: templateField.default || null,
              options: templateField.options || [],
              validation: templateField.validation || {},
              icon: templateField.icon || '',
              order: templateField.order || 0,
              isFavourite: templateField.isFavourite || false
            };
          }
        });

        // Check if section metadata changed
        if (JSON.stringify(updatedSection) !== JSON.stringify(existingSection)) {
          hasChanges = true;
        }

        return updatedSection;
      } else {
        // Add new section from template
        hasChanges = true;
        syncLog.sectionsAdded.push(category.name);
        return {
          key: category.name,
          label: category.label || category.name,
          icon: category.icon || '',
          collapsible: category.collapsible !== false,
          fields: category.fields.map(field => ({
            key: field.name,
            label: field.label || field.name,
            widget: field.widget || 'text',
            required: field.required || false,
            placeholder: field.placeholder || '',
            enabled: field.enabled !== undefined ? field.enabled : false,
            value: field.default || null,
            options: field.options || [],
            validation: field.validation || {},
            icon: field.icon || '',
            order: field.order || 0,
            isFavourite: field.isFavourite || false
          }))
        };
      }
    });

    // Initialize links array if it doesn't exist
    if (!profile.links) {
      profile.links = [];
    }

      // Sync links from template
      if (template.links && Array.isArray(template.links)) {
        template.links.forEach(linkGroup => {
          if (linkGroup && linkGroup.links && Array.isArray(linkGroup.links)) {
            linkGroup.links.forEach(link => {
              // Check if this link already exists in the profile
              const existingLink = profile.links?.find(profileLink => profileLink.name === link.name);
              
              if (!existingLink) {
                // Add missing link from template
                profile.links?.push({
                  name: link.name,
                  label: link.label,
                  category: linkGroup.category,
                  icon: link.icon,
                  baseUrl: link.baseUrl,
                  urlPattern: link.urlPattern,
                  placeholder: link.placeholder,
                  value: "", // Empty value initially
                  enabled: link.enabled !== false,
                  active: false, // Not active by default until user sets a value
                  order: link.order || 0,
                  description: link.description,
                  isFavourite: link.isFavourite || false
                } as any);
                hasChanges = true;
                syncLog.linksAdded.push(`${linkGroup.category}.${link.name}`);
              }
            });
          }
        });
      }
    // Only update if there are changes
    if (hasChanges) {
      // Sync icons with latest URLs from ContentLogoImage database
      await this.syncIconsWithDatabase(updatedSections as any, profile.links || []);
      
      profile.sections = updatedSections as any;
      if (syncLog.linksAdded.length > 0) {
        profile.markModified('links');
      }
      profile.profileInformation.updatedAt = new Date();
      await profile.save();
      
      logger.info(`Synced profile ${profileId} with template ${(template._id as mongoose.Types.ObjectId).toString()}`, {
        syncLog,
        changes: {
          sectionsAdded: syncLog.sectionsAdded.length,
          fieldsAdded: syncLog.fieldsAdded.length,
          fieldsUpdated: syncLog.fieldsUpdated.length,
          linksAdded: syncLog.linksAdded.length
        }
      });
    } else {
      logger.debug(`Profile ${profileId} already in sync with template ${(template._id as mongoose.Types.ObjectId).toString()}`);
    }

    return profile;
  }

  /**
   * Syncs all personal profiles with their templates to update structure
   * @returns Summary of sync results
   */
  async syncAllPersonalProfiles(): Promise<{
    totalProfiles: number;
    syncedProfiles: number;
    errors: Array<{ profileId: string; error: string }>;
  }> {
    const personalProfiles = await Profile.find({ profileType: 'personal' });
    const errors: Array<{ profileId: string; error: string }> = [];
    let syncedProfiles = 0;

    for (const profile of personalProfiles) {
      try {
        await this.syncProfileWithTemplate(profile._id.toString());
        syncedProfiles++;
      } catch (error) {
        errors.push({
          profileId: profile._id.toString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return {
      totalProfiles: personalProfiles.length,
      syncedProfiles,
      errors
    };
  }

  /**
   * Syncs all profiles that use a specific template
   * @param templateId The template ID to sync profiles for
   * @returns Summary of sync results
   */
  async syncProfilesByTemplate(templateId: string): Promise<{
    totalProfiles: number;
    syncedProfiles: number;
    errors: Array<{ profileId: string; error: string }>;
  }> {
    logger.info(`Syncing all profiles using template ${templateId}`);

    if (!isValidObjectId(templateId)) {
      throw createHttpError(400, 'Invalid template ID');
    }

    const profiles = await Profile.find({ templatedId: templateId });
    const errors: Array<{ profileId: string; error: string }> = [];
    let syncedProfiles = 0;

    logger.info(`Found ${profiles.length} profiles to sync for template ${templateId}`);

    // Process profiles in batches for better performance
    const batchSize = 10;
    for (let i = 0; i < profiles.length; i += batchSize) {
      const batch = profiles.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchPromises = batch.map(async (profile) => {
        try {
          await this.syncProfileWithTemplate(profile._id.toString());
          syncedProfiles++;
          logger.debug(`Synced profile ${profile._id} (${profile.profileInformation?.username})`);
        } catch (error) {
          errors.push({
            profileId: profile._id.toString(),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          logger.error(`Failed to sync profile ${profile._id}:`, error);
        }
      });

      await Promise.all(batchPromises);
      
      // Small delay between batches to be gentle on the system
      if (i + batchSize < profiles.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    logger.info(`Template sync completed for ${templateId}: ${syncedProfiles}/${profiles.length} profiles synced successfully, ${errors.length} errors`);

    return {
      totalProfiles: profiles.length,
      syncedProfiles,
      errors
    };
  }

  /**
   * Populates basic information fields with existing data from other sections and profile info
   * @param profileId The profile ID to update
   * @returns The updated profile document
   */
  async populateBasicInformation(profileId: string): Promise<ProfileDocument> {
    logger.info(`Populating basic information for profile ${profileId}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    
    // Auto-populate basic information if not already present
    if (!profileDoc.profileInformation.username) {
      profileDoc.profileInformation.username = `user_${profileDoc._id.toString().slice(-8)}`;
    }
    
    if (!profileDoc.profileInformation.title) {
      profileDoc.profileInformation.title = 'Profile Title';
    }

    await profileDoc.save();
    logger.info(`Successfully populated basic information for profile ${profileId}`);
    
    return profileDoc;
  }

  /**
   * Remove a specific unavailable period by ID
   */
  async removeUnavailablePeriod(profileId: string, periodId: string): Promise<any> {
    logger.info(`Removing unavailable period ${periodId} for profile ${profileId}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    if (!profileDoc.availability?.unavailablePeriods) {
      throw createHttpError(404, 'No unavailable periods found');
    }

    // Find and remove the specific period
    const periods = (profileDoc.availability as any).unavailablePeriods;
    const initialLength = periods.length;
    
    // Filter out the period with matching ID
    (profileDoc.availability as any).unavailablePeriods = periods.filter(
      (period: any) => period._id?.toString() !== periodId
    );

    const finalLength = (profileDoc.availability as any).unavailablePeriods.length;
    
    if (initialLength === finalLength) {
      throw createHttpError(404, 'Unavailable period not found');
    }

    await profileDoc.save();

    // Refresh expansion instances when availability changes
    try {
      await UnavailablePeriodExpansionService.refreshInstancesForProfile(profileId);
      logger.info(`Refreshed expansion instances after removing period ${periodId}`);
    } catch (error) {
      logger.error(`Failed to refresh expansion instances for profile ${profileId}:`, error);
    }

    logger.info(`Successfully removed unavailable period ${periodId} for profile ${profileId}`);
    return profileDoc.availability;
  }

  /**
   * Remove unavailable periods by date range
   */
  async removeUnavailablePeriodsInRange(
    profileId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<any> {
    logger.info(`Removing unavailable periods for profile ${profileId} between ${startDate.toISOString()} and ${endDate.toISOString()}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    if (!profileDoc.availability?.unavailablePeriods) {
      return profileDoc.availability;
    }

    const periods = (profileDoc.availability as any).unavailablePeriods;
    const initialLength = periods.length;
    
    // Filter out periods that fall within the date range
    (profileDoc.availability as any).unavailablePeriods = periods.filter((period: any) => {
      // Handle specific date periods
      if (period.type === 'specific' && period.date) {
        const periodDate = new Date(period.date);
        return !(periodDate >= startDate && periodDate <= endDate);
      }
      
      // For recurring periods, we keep them but the expansion service will handle the instances
      return true;
    });

    const finalLength = (profileDoc.availability as any).unavailablePeriods.length;
    const removedCount = initialLength - finalLength;

    await profileDoc.save();

    // Refresh expansion instances when availability changes
    try {
      await UnavailablePeriodExpansionService.refreshInstancesForProfile(profileId);
      logger.info(`Refreshed expansion instances after removing ${removedCount} periods`);
    } catch (error) {
      logger.error(`Failed to refresh expansion instances for profile ${profileId}:`, error);
    }

    logger.info(`Successfully removed ${removedCount} unavailable periods for profile ${profileId}`);
    return profileDoc.availability;
  }

  /**
   * Remove specific unavailable period instances (for fine-grained control)
   */
  async removeUnavailablePeriodInstances(
    profileId: string, 
    startTime: Date, 
    endTime: Date
  ): Promise<{ removedCount: number }> {
    logger.info(`Removing unavailable period instances for profile ${profileId} from ${startTime.toISOString()} to ${endTime.toISOString()}`);
    
    try {
      // Remove specific instances from the expansion collection
      const result = await UnavailablePeriodExpansionService.removeInstancesInRange(
        profileId, 
        startTime, 
        endTime
      );
      
      logger.info(`Removed ${result.removedCount} unavailable period instances for profile ${profileId}`);
      return result;
    } catch (error) {
      logger.error(`Error removing unavailable period instances for profile ${profileId}:`, error);
      throw createHttpError(500, 'Failed to remove unavailable period instances');
    }
  }

  /**
   * Cancel/remove a recurring unavailable period
   */
  async cancelRecurringUnavailablePeriod(
    profileId: string, 
    periodId: string, 
    fromDate?: Date
  ): Promise<any> {
    logger.info(`Canceling recurring unavailable period ${periodId} for profile ${profileId}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    if (!profileDoc.availability?.unavailablePeriods) {
      throw createHttpError(404, 'No unavailable periods found');
    }

    const periods = (profileDoc.availability as any).unavailablePeriods;
    const period = periods.find((p: any) => p._id?.toString() === periodId);
    
    if (!period) {
      throw createHttpError(404, 'Unavailable period not found');
    }

    if (period.type !== 'recurring') {
      throw createHttpError(400, 'Period is not recurring');
    }

    if (fromDate) {
      // Set an end date for the recurring period instead of removing it entirely
      period.endDate = fromDate;
      logger.info(`Set end date for recurring period ${periodId} to ${fromDate.toISOString()}`);
    } else {
      // Remove the recurring period entirely
      (profileDoc.availability as any).unavailablePeriods = periods.filter(
        (p: any) => p._id?.toString() !== periodId
      );
      logger.info(`Removed recurring period ${periodId} entirely`);
    }

    await profileDoc.save();

    // Refresh expansion instances when availability changes
    try {
      await UnavailablePeriodExpansionService.refreshInstancesForProfile(profileId);
      logger.info(`Refreshed expansion instances after canceling recurring period ${periodId}`);
    } catch (error) {
      logger.error(`Failed to refresh expansion instances for profile ${profileId}:`, error);
    }

    return profileDoc.availability;
  }

  /**
   * Replace all unavailable periods (use with caution)
   */
  async replaceUnavailablePeriods(profileId: string, unavailablePeriods: any[]): Promise<any> {
    logger.info(`Replacing all unavailable periods for profile ${profileId}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    if (!profileDoc.availability) {
      profileDoc.availability = {
        isAvailable: true,
        defaultDuration: 60,
        bufferTime: 15,
        unavailablePeriods: [],
        bookingWindow: {
          minNotice: 60,
          maxAdvance: 30
        }
      } as any;
    }

    // Generate unique IDs for periods that don't have them
    const mongoose = require('mongoose');
    const periodsWithIds = unavailablePeriods.map((period: any) => ({
      ...period,
      _id: period._id || new mongoose.Types.ObjectId()
    }));

    // Replace all periods
    (profileDoc.availability as any).unavailablePeriods = periodsWithIds;
    
    await profileDoc.save();

    // Refresh expansion instances when availability changes
    try {
      await UnavailablePeriodExpansionService.refreshInstancesForProfile(profileId);
      logger.info(`Refreshed expansion instances after replacing unavailable periods for profile ${profileId}`);
    } catch (error) {
      logger.error(`Failed to refresh expansion instances for profile ${profileId}:`, error);
    }

    logger.info(`Replaced all unavailable periods for profile ${profileId} with ${periodsWithIds.length} new periods`);
    return profileDoc.availability;
  }

  /**
   * Add new unavailable periods to existing ones
   */
  async addUnavailablePeriods(profileId: string, unavailablePeriods: any[]): Promise<any> {
    logger.info(`Adding ${unavailablePeriods.length} new unavailable periods for profile ${profileId}`);
    
    const profile = await this.getProfile(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    const profileDoc = profile as ProfileDocument;
    if (!profileDoc.availability) {
      profileDoc.availability = {
        isAvailable: true,
        defaultDuration: 60,
        bufferTime: 15,
        unavailablePeriods: [],
        bookingWindow: {
          minNotice: 60,
          maxAdvance: 30
        }
      } as any;
    }

    const existingPeriods = (profileDoc.availability as any).unavailablePeriods || [];
    
    // Generate unique IDs for new periods that don't have them
    const mongoose = require('mongoose');
    const periodsWithIds = unavailablePeriods.map((period: any) => ({
      ...period,
      _id: period._id || new mongoose.Types.ObjectId()
    }));

    // Smart merge: only add periods that don't already exist
    const mergedPeriods = [...existingPeriods];
    let addedCount = 0;
    
    for (const newPeriod of periodsWithIds) {
      // Check if this period already exists
      const isDuplicate = existingPeriods.some((existingPeriod: any) => 
        this.arePeriodsEqual(existingPeriod, newPeriod)
      );
      
      if (!isDuplicate) {
        mergedPeriods.push(newPeriod);
        addedCount++;
      }
    }
    
    (profileDoc.availability as any).unavailablePeriods = mergedPeriods;
    
    await profileDoc.save();

    // Refresh expansion instances when availability changes
    try {
      await UnavailablePeriodExpansionService.refreshInstancesForProfile(profileId);
      logger.info(`Refreshed expansion instances after adding unavailable periods for profile ${profileId}`);
    } catch (error) {
      logger.error(`Failed to refresh expansion instances for profile ${profileId}:`, error);
    }

    logger.info(`Added ${addedCount} new unavailable periods to profile ${profileId} (${unavailablePeriods.length - addedCount} duplicates skipped)`);
    return profileDoc.availability;
  }

  /**
   * Helper method to check if two periods are equal
   */
  private arePeriodsEqual(period1: any, period2: any): boolean {
    // Compare key properties to determine if periods are duplicates
    if (period1.type !== period2.type) return false;
    
    if (period1.type === 'specific') {
      return period1.date?.getTime() === period2.date?.getTime();
    }
    
    if (period1.type === 'recurring') {
      return (
        JSON.stringify(period1.days?.sort()) === JSON.stringify(period2.days?.sort()) &&
        period1.startTime === period2.startTime &&
        period1.endTime === period2.endTime &&
        period1.isAllDay === period2.isAllDay
      );
    }
    
    return false;
  }

  /**
   * Updates profile links with new values and/or active status
   * @param profileId The profile ID to update
   * @param userId The user ID making the update
   * @param linkUpdates Array of link updates
   * @returns The updated profile document
   */
  async updateProfileLinks(
    profileId: string,
    userId: string,
    linkUpdates: Array<{
      name: string;
      value?: string;
      active?: boolean;
    }>
  ): Promise<ProfileDocument> {
    logger.info(`Updating links for profile ${profileId}`);

    if (!isValidObjectId(profileId) || !isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid profile ID or user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get user data to check if they're an admin
    const user = await User.findById(userId);

    // Verify user has permission to update (either creator or admin)
    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to update this profile');
    }

    // Initialize links array if it doesn't exist
    if (!profile.links) {
      profile.links = [];
    }

    // Update links
    linkUpdates.forEach(({ name, value, active }) => {
      const linkIndex = profile.links!.findIndex(link => link.name === name);
      
      if (linkIndex !== -1) {
        // Update existing link
        if (value !== undefined) {
          (profile.links![linkIndex] as any).value = value;
        }
        if (active !== undefined) {
          (profile.links![linkIndex] as any).active = active;
        }
      } else {
        // If link doesn't exist, we might need to create it from template
        logger.warn(`Link ${name} not found in profile ${profileId}, skipping update`);
      }
    });

    // Mark the links array as modified so Mongoose saves the changes
    profile.markModified('links');
    profile.profileInformation.updatedAt = new Date();
    await profile.save();

    logger.info(`Profile links updated for ${profileId}`);
    return profile;
  }

  /**
   * Toggles the active status of a profile link
   * @param profileId The profile ID
   * @param userId The user ID making the update
   * @param linkName The name of the link to toggle
   * @param active The new active status
   * @returns The updated profile document
   */
  async toggleProfileLink(
    profileId: string,
    userId: string,
    linkName: string,
    active: boolean
  ): Promise<ProfileDocument> {
    logger.info(`Toggling link ${linkName} to ${active} for profile ${profileId}`);

    return this.updateProfileLinks(profileId, userId, [
      { name: linkName, active }
    ]);
  }

  /**
   * Syncs profile links with template links if they don't exist
   * @param profileId The profile ID
   * @returns The updated profile document
   */
  async syncProfileLinks(profileId: string): Promise<ProfileDocument> {
    logger.info(`Syncing links for profile ${profileId}`);

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Get the template
    const template = await ProfileTemplate.findById(profile.templatedId);
    if (!template) {
      throw createHttpError(404, 'Template not found');
    }

    // Initialize links array if it doesn't exist
    if (!profile.links) {
      profile.links = [];
    }

    // Add missing links from template
    let hasChanges = false;
    template.links.forEach(linkGroup => {
      linkGroup.links.forEach(templateLink => {
        const existingLink = profile.links!.find(link => link.name === templateLink.name);
        
        if (!existingLink) {
          // Add new link from template
          profile.links!.push({
            name: templateLink.name,
            label: templateLink.label,
            category: linkGroup.category,
            icon: templateLink.icon,
            baseUrl: templateLink.baseUrl,
            urlPattern: templateLink.urlPattern,
            placeholder: templateLink.placeholder,
            value: '', // Start with empty value
            enabled: templateLink.enabled || true,
            active: false, // Start inactive
            order: templateLink.order || 0,
            description: templateLink.description
          } as any);
          hasChanges = true;
        }
      });
    });

    // Save if there are changes
    if (hasChanges) {
      // Sync icons with latest URLs from ContentLogoImage database for new links
      await this.syncIconsWithDatabase(profile.sections as any, profile.links);
      
      profile.markModified('links');
      profile.profileInformation.updatedAt = new Date();
      await profile.save();
      logger.info(`Synced ${profile.links.length} links for profile ${profileId}`);
    }

    return profile;
  }

  /**
   * Syncs profile sections and links with latest icon URLs from ContentLogoImage database
   * @param sections The profile sections to sync
   * @param links The profile links to sync
   */
  private async syncIconsWithDatabase(
    sections: Array<{
      key: string;
      label: string;
      icon: string;
      collapsible: boolean;
      fields: Array<{
        key: string;
        label: string;
        widget: string;
        required: boolean;
        placeholder: string;
        enabled: boolean;
        value: any;
        options: any[];
        validation: any;
        icon: string;
        order: number;
        isFavourite: boolean;
      }>;
    }>,
    links: Array<{
      name: string;
      label: string;
      category: string;
      icon?: string;
      baseUrl?: string;
      urlPattern?: string;
      placeholder?: string;
      value?: string;
      enabled: boolean;
      active: boolean;
      order: number;
      description?: string;
      isFavourite?: boolean;
    }>
  ): Promise<void> {
    logger.info('Syncing profile icons with ContentLogoImage database');

    // Sync category icons
    for (const section of sections) {
      if (section.icon && section.icon.startsWith('ContentLogoImages.')) {
        const iconKey = section.icon.replace('ContentLogoImages.', '');
        const latestIconUrl = await getContentLogoImageUrl(iconKey);
        if (latestIconUrl) {
          section.icon = latestIconUrl;
          logger.info(`Updated category icon for ${section.key}: ${iconKey} -> ${latestIconUrl}`);
        }
      }

      // Sync field icons
      for (const field of section.fields) {
        if (field.icon && field.icon.startsWith('ContentLogoImages.')) {
          const iconKey = field.icon.replace('ContentLogoImages.', '');
          const latestIconUrl = await getContentLogoImageUrl(iconKey);
          if (latestIconUrl) {
            field.icon = latestIconUrl;
            logger.info(`Updated field icon for ${section.key}.${field.key}: ${iconKey} -> ${latestIconUrl}`);
          }
        }
      }
    }

    // Sync link icons
    for (const link of links) {
      if (link.icon && link.icon.startsWith('ContentLogoImages.')) {
        const iconKey = link.icon.replace('ContentLogoImages.', '');
        const latestIconUrl = await getContentLogoImageUrl(iconKey);
        if (latestIconUrl) {
          link.icon = latestIconUrl;
          logger.info(`Updated link icon for ${link.name}: ${iconKey} -> ${latestIconUrl}`);
        }
      }
    }

    logger.info('Completed icon sync with ContentLogoImage database');
  }

  /**
   * Get profile by username
   * @param username - The username to search for
   * @param skipAutoSync - Whether to skip auto-sync with template
   * @returns Profile document
   */
  async getProfileByUsername(username: string, skipAutoSync: boolean = false): Promise<ProfileDocument> {
    logger.info(`Getting profile by username: ${username}`);

    if (!username) {
      throw createHttpError(400, 'Username is required');
    }

    // Find profile by username
    const profile = await Profile.findOne({
      'profileInformation.username': username
    }).populate('profileInformation.creator', 'firstName lastName email');

    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Auto-sync with template if not skipped
    if (!skipAutoSync) {
      try {
        await this.syncProfileWithTemplate(profile._id.toString());
        // Refresh the profile after sync
        const refreshedProfile = await Profile.findById(profile._id)
          .populate('profileInformation.creator', 'firstName lastName email');
        if (refreshedProfile) {
          return refreshedProfile;
        }
      } catch (syncError) {
        logger.warn(`Failed to auto-sync profile ${profile._id} with template:`, syncError);
        // Continue with original profile if sync fails
      }
    }

    return profile;
  }

  /**
   * Get profile by secondary ID (sharecode)
   * @param secondaryId The secondary ID (sharecode) of the profile
   * @param skipAutoSync Whether to skip auto-sync with template
   * @returns The profile document
   */
  async getProfileBySecondaryId(secondaryId: string, skipAutoSync: boolean = false): Promise<ProfileDocument> {
    logger.info(`Getting profile by secondary ID: ${secondaryId}`);

    if (!secondaryId) {
      throw createHttpError(400, 'Secondary ID is required');
    }

    // Validate secondary ID format
    if (!/^[a-zA-Z][a-zA-Z0-9]{7}$/.test(secondaryId)) {
      throw createHttpError(400, 'Invalid secondary ID format');
    }

    // Find profile by secondary ID
    const profile = await Profile.findOne({
      secondaryId: secondaryId
    }).populate('profileInformation.creator', 'firstName lastName email');

    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // Auto-sync with template if not skipped
    if (!skipAutoSync) {
      try {
        await this.syncProfileWithTemplate(profile._id.toString());
        // Refresh the profile after sync
        const refreshedProfile = await Profile.findById(profile._id)
          .populate('profileInformation.creator', 'firstName lastName email');
        if (refreshedProfile) {
          return refreshedProfile;
        }
      } catch (syncError) {
        logger.warn(`Failed to auto-sync profile ${profile._id} with template:`, syncError);
        // Continue with original profile if sync fails
      }
    }

    return profile;
  }

  /**
   * Publish a profile
   * @param profileId The profile ID to publish
   * @param userId The user ID performing the action
   * @returns The updated profile document
   */
  async publishProfile(profileId: string, userId: string): Promise<ProfileDocument> {
    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    if (!isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // RBAC: Check permissions using the proper RBAC system
    const isOwner = profile.profileInformation.creator.toString() === userId.toString();
    
    let canPublish = isOwner;
    
    // If not owner, check if user has admin permissions to publish profiles
    if (!isOwner) {
      const { RBACService } = await import('./rbac.service');
      canPublish = await RBACService.hasPermission(userId, 'profile.publish.any', {
        resourceId: profileId
      });
    }

    if (!canPublish) {
      throw createHttpError(403, 'You do not have permission to publish this profile');
    }

    if (profile.published) {
      throw createHttpError(400, 'Profile is already published');
    }

    // Update profile
    profile.published = true;
    profile.publishedAt = new Date();
    profile.publishedBy = new mongoose.Types.ObjectId(userId);

    await profile.save();

    logger.info(`Profile ${profileId} published by user ${userId} (isOwner: ${isOwner})`);
    return profile;
  }

  /**
   * Unpublish a profile
   * @param profileId The profile ID to unpublish
   * @param userId The user ID performing the action
   * @returns The updated profile document
   */
  async unpublishProfile(profileId: string, userId: string): Promise<ProfileDocument> {
    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    if (!isValidObjectId(userId)) {
      throw createHttpError(400, 'Invalid user ID');
    }

    const profile = await Profile.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // RBAC: Check permissions using the proper RBAC system
    const isOwner = profile.profileInformation.creator.toString() === userId.toString();
    
    let canUnpublish = isOwner;
    
    // If not owner, check if user has admin permissions to unpublish profiles
    if (!isOwner) {
      const { RBACService } = await import('./rbac.service');
      canUnpublish = await RBACService.hasPermission(userId, 'profile.unpublish.any', {
        resourceId: profileId
      });
    }

    if (!canUnpublish) {
      throw createHttpError(403, 'You do not have permission to unpublish this profile');
    }

    if (!profile.published) {
      throw createHttpError(400, 'Profile is already unpublished');
    }

    // Update profile
    profile.published = false;
    profile.publishedAt = undefined;
    profile.publishedBy = undefined;

    await profile.save();

    logger.info(`Profile ${profileId} unpublished by user ${userId} (isOwner: ${isOwner})`);
    return profile;
  }

  /**
   * Get published profiles with optional filters
   * @param filters Optional filters for published profiles
   * @returns Array of published profile documents
   */
  async getPublishedProfiles(filters: {
    profileType?: string;
    profileCategory?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<ProfileDocument[]> {
    const query: any = { published: true };

    if (filters.profileType) {
      query.profileType = filters.profileType;
    }

    if (filters.profileCategory) {
      query.profileCategory = filters.profileCategory;
    }

    const profiles = await Profile.find(query)
      .populate('profileInformation.creator', 'name email')
      .populate('publishedBy', 'name email')
      .sort({ publishedAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.offset || 0);

    return profiles;
  }

  /**
   * Get publication statistics
   * @returns Publication statistics
   */
  async getPublicationStats(): Promise<{
    totalProfiles: number;
    publishedProfiles: number;
    unpublishedProfiles: number;
    publishedPercentage: number;
    recentlyPublished: number;
  }> {
    const totalProfiles = await Profile.countDocuments();
    const publishedProfiles = await Profile.countDocuments({ published: true });
    const unpublishedProfiles = totalProfiles - publishedProfiles;
    const publishedPercentage = totalProfiles > 0 ? (publishedProfiles / totalProfiles) * 100 : 0;

    // Count profiles published in the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentlyPublished = await Profile.countDocuments({
      published: true,
      publishedAt: { $gte: sevenDaysAgo }
    });

    return {
      totalProfiles,
      publishedProfiles,
      unpublishedProfiles,
      publishedPercentage: Math.round(publishedPercentage * 100) / 100,
      recentlyPublished
    };
  }

  /**
   * Transfer profile to another user
   */
  async transferProfile(
    profileId: string,
    fromUserId: string,
    toUserId: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const profile = await this.getProfile(profileId);
      const fromUser = await User.findById(fromUserId);
      const toUser = await User.findById(toUserId);

      if (!profile || !fromUser || !toUser) {
        throw createHttpError(404, 'Profile or user not found');
      }

      // Check if the profile belongs to the fromUser
      if (profile.profileInformation.creator.toString() !== fromUserId) {
        throw createHttpError(403, 'You can only transfer your own profiles');
      }

      // Check if the receiving user has the right subscription to receive profiles
      const toUserPlan = await subscriptionService.getUserActivePlan(toUserId);
      if (!toUserPlan) {
        throw createHttpError(400, 'Receiving user does not have an active subscription');
      }

      // Check profile limits based on profile type
      const profileType = profile.profileType || 'personal';
      let canReceive = false;

      switch (profileType) {
        case 'personal':
          canReceive = toUserPlan.usageStats.individualProfiles < toUserPlan.limits.individualProfiles;
          break;
        case 'business':
          canReceive = toUserPlan.usageStats.individualProfiles < toUserPlan.limits.individualProfiles;
          break;
        case 'academic':
          canReceive = toUserPlan.usageStats.individualProfiles < toUserPlan.limits.individualProfiles;
          break;
        case 'group':
          canReceive = toUserPlan.usageStats.groupProfiles < toUserPlan.limits.groupProfiles;
          break;
        default:
          canReceive = toUserPlan.usageStats.individualProfiles < toUserPlan.limits.individualProfiles;
      }

      if (!canReceive) {
        throw createHttpError(400, 'Receiving user has reached their profile limit for this type');
      }

      // Update profile ownership
      profile.profileInformation.creator = new mongoose.Types.ObjectId(toUserId);
      profile.profileInformation.accountHolder = toUser.fullName;
      await profile.save();

      // Log the transfer using account service
      await AccountService.logTransfer(
        'profile',
        'transferred',
        fromUserId,
        toUserId,
        24, // myPts for profile transfer
        {
          profileId,
          reason,
          profileType: profile.profileType || 'personal'
        }
      );

      return {
        success: true,
        message: 'Profile transferred successfully'
      };
    } catch (error) {
      logger.error('Error transferring profile:', error);
      throw error;
    }
  }

  /**
   * Claim a shared profile
   */
  async claimProfile(
    profileId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const profile = await this.getProfile(profileId);
      const user = await User.findById(userId);

      if (!profile || !user) {
        throw createHttpError(404, 'Profile or user not found');
      }

      // Check if profile is available for claiming (shared or unclaimed)
      if (profile.profileInformation.creator) {
        throw createHttpError(400, 'Profile is already claimed by another user');
      }

      // Check if the user has the right subscription to claim profiles
      const userPlan = await subscriptionService.getUserActivePlan(userId);
      if (!userPlan) {
        throw createHttpError(400, 'User does not have an active subscription');
      }

      // Check profile limits
      const profileType = profile.profileType || 'personal';
      let canClaim = false;

      switch (profileType) {
        case 'personal':
          canClaim = userPlan.usageStats.individualProfiles < userPlan.limits.individualProfiles;
          break;
        case 'business':
          canClaim = userPlan.usageStats.individualProfiles < userPlan.limits.individualProfiles;
          break;
        case 'academic':
          canClaim = userPlan.usageStats.individualProfiles < userPlan.limits.individualProfiles;
          break;
        case 'group':
          canClaim = userPlan.usageStats.groupProfiles < userPlan.limits.groupProfiles;
          break;
        default:
          canClaim = userPlan.usageStats.individualProfiles < userPlan.limits.individualProfiles;
      }

      if (!canClaim) {
        throw createHttpError(400, 'User has reached their profile limit for this type');
      }

      // Claim the profile
      profile.profileInformation.creator = new mongoose.Types.ObjectId(userId);
      profile.profileInformation.accountHolder = user.fullName;
      await profile.save();

      // Log the claim
      await AccountService.logTransfer(
        'profile',
        'claimed',
        '', // No fromUserId for claims
        userId,
        12, // myPts for profile claim
        {
          profileId,
          profileType: profile.profileType || 'personal'
        }
      );

      return {
        success: true,
        message: 'Profile claimed successfully'
      };
    } catch (error) {
      logger.error('Error claiming profile:', error);
      throw error;
    }
  }
}


