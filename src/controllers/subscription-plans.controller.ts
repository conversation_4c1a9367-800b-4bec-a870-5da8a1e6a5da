import { Request, Response } from 'express';
import createHttpError from 'http-errors';
import asyncHandler from 'express-async-handler';
import Plan from '../models/Plan';
import { logger } from '../utils/logger';

// @desc    Get all subscription plans
// @route   GET /api/subscription-plans
// @access  Public
export const getAllSubscriptionPlans = asyncHandler(async (req: Request, res: Response) => {
    try {
        const plans = await Plan.find({ isActive: true }).sort({ price: 1 });
        
        logger.info(`📊 Retrieved ${plans.length} active subscription plans`);
        
        res.json({
            success: true,
            data: plans,
            message: 'Subscription plans fetched successfully',
            count: plans.length
        });
    } catch (error) {
        logger.error('❌ Error fetching subscription plans:', error);
        throw createHttpError(500, 'Failed to fetch subscription plans');
    }
});

// @desc    Get subscription plan by ID
// @route   GET /api/subscription-plans/:id
// @access  Public
export const getSubscriptionPlanById = asyncHandler(async (req: Request, res: Response) => {
    try {
        const plan = await Plan.findById(req.params.id);
        
        if (!plan) {
            throw createHttpError(404, 'Subscription plan not found');
        }
        
        logger.info(`📋 Retrieved subscription plan: ${plan.name}`);
        
        res.json({
            success: true,
            data: plan,
            message: 'Subscription plan fetched successfully'
        });
    } catch (error) {
        if (error instanceof createHttpError.HttpError) {
            throw error;
        }
        logger.error('❌ Error fetching subscription plan by ID:', error);
        throw createHttpError(500, 'Failed to fetch subscription plan');
    }
});

// @desc    Get subscription plan by name
// @route   GET /api/subscription-plans/name/:name
// @access  Public
export const getSubscriptionPlanByName = asyncHandler(async (req: Request, res: Response) => {
    try {
        const plan = await Plan.findOne({ 
            name: req.params.name,
            isActive: true 
        });
        
        if (!plan) {
            throw createHttpError(404, `Subscription plan '${req.params.name}' not found`);
        }
        
        logger.info(`📋 Retrieved subscription plan by name: ${plan.name}`);
        
        res.json({
            success: true,
            data: plan,
            message: 'Subscription plan fetched successfully'
        });
    } catch (error) {
        if (error instanceof createHttpError.HttpError) {
            throw error;
        }
        logger.error('❌ Error fetching subscription plan by name:', error);
        throw createHttpError(500, 'Failed to fetch subscription plan');
    }
});

// @desc    Get subscription plan comparison
// @route   GET /api/subscription-plans/compare
// @access  Public
export const getSubscriptionPlanComparison = asyncHandler(async (req: Request, res: Response) => {
    try {
        const plans = await Plan.find({ isActive: true }).sort({ price: 1 });
        
        // Create comparison data
        const comparison = {
            plans: plans.map(plan => ({
                id: plan._id,
                name: plan.name,
                planType: plan.planType,
                price: plan.price,
                billingCycle: plan.billingCycle,
                description: plan.description,
                features: plan.features,
                benefits: plan.benefits,
                limits: {
                    individualProfiles: plan.individualProfiles,
                    secondaryAccounts: plan.secondaryAccounts,
                    accessoryProfiles: plan.accessoryProfiles,
                    groupProfiles: plan.groupProfiles,
                    storageGB: plan.storageGB,
                    nfcProductLinking: plan.nfcProductLinking
                },
                featureAccess: {
                    insightsAccess: plan.insightsAccess,
                    qrCodeAccess: plan.qrCodeAccess,
                    scannerAccess: plan.scannerAccess,
                    communityAccess: plan.communityAccess,
                    myPtsRate: plan.myPtsRate,
                    payoutPriority: plan.payoutPriority,
                    payoutDays: plan.payoutDays,
                    rewardSystemAccess: plan.rewardSystemAccess,
                    bonusTriggers: plan.bonusTriggers,
                    gamifiedActivities: plan.gamifiedActivities,
                    circleCreation: plan.circleCreation,
                    leadsReferrals: plan.leadsReferrals,
                    teamProxyRoles: plan.teamProxyRoles,
                    affiliateProgram: plan.affiliateProgram,
                    addonMarketplace: plan.addonMarketplace,
                    supportType: plan.supportType
                }
            })),
            summary: {
                totalPlans: plans.length,
                priceRange: {
                    min: Math.min(...plans.map(p => p.price)),
                    max: Math.max(...plans.map(p => p.price))
                },
                features: {
                    hasFreePlan: plans.some(p => p.price === 0),
                    hasPremiumFeatures: plans.some(p => p.planType === 'premium'),
                    hasTeamFeatures: plans.some(p => p.teamProxyRoles)
                }
            }
        };
        
        logger.info(`📊 Created comparison for ${plans.length} subscription plans`);
        
        res.json({
            success: true,
            data: comparison,
            message: 'Subscription plan comparison generated successfully'
        });
    } catch (error) {
        logger.error('❌ Error creating subscription plan comparison:', error);
        throw createHttpError(500, 'Failed to create subscription plan comparison');
    }
}); 