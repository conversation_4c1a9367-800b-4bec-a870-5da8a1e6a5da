// controllers/profile.controller.ts
import { Request, Response, NextFunction } from 'express';
import asyncHand<PERSON> from 'express-async-handler';
import createHttpError from 'http-errors';
import { isValidObjectId } from 'mongoose';
import { ProfileService } from '../services/profile.service';
import { ProfileDocument } from '../models/profile.model';
import { logger } from '../utils/logger';
import { ProfileModel } from '../models/profile.model';
import { ProfileFilter } from '../types/profiles';
import { User } from '../models/User';
import { templateSyncService } from '../services/template-sync.service';
import { subscriptionService } from '../services/subscription.service';
import { RoleType } from '../models/Role';

interface ProfileFieldToggle {
  sectionKey: string;
  fieldKey: string;
  enabled: boolean;
}

interface ProfileFieldUpdate {
  sectionKey: string;
  fieldKey: string;
  value: any;
}

interface CreateProfileBody {
  templateId: string;
  profileInformation: {
    username: string;
    title?: string;
    accountHolder?: string;
    pid?: string;
    relationshipToAccountHolder?: string;
  };
  sections?: Array<{
    key: string;
    label: string;
    fields: Array<{
      key: string;
      value: any;
      enabled: boolean;
    }>;
  }>;
  profileLocation?: {
    city?: string;
    stateOrProvince?: string;
    country?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
}

export class ProfileController {
  private service = new ProfileService();

  /**
   * Helper function to format profile data for frontend consumption
   * @param profile The profile document to format
   * @returns Formatted profile data
   */
  private formatProfileData(profile: ProfileDocument) {
    try {
      logger.info(`Formatting profile data for profile ID: ${profile._id}`);

      // Extract profile information
      const profileInfo = profile.profileInformation || {};
      const profileMyPts = profile.ProfileMypts || { currentBalance: 0, lifetimeMypts: 0 };

      // Find fullName in sections if available
      let fullName = null;
      const basicSection = profile.sections?.find(s => s.key === 'basic');
      if (basicSection) {
        const fullNameField = basicSection.fields?.find(f => f.key === 'fullName');
        if (fullNameField) {
          // Use type assertion to safely access the value property
          const fieldValue = (fullNameField as any).value;
          if (fieldValue) {
            fullName = fieldValue;
            logger.debug(`Found fullName in sections: ${fullName}`);
          }
        }
      }

      // IMPORTANT: Always use username as the name to avoid "Untitled Profile" issue
      // This is the most reliable way to ensure we have a consistent name
      let name = profileInfo.username || 'Profile';

      // Log what we're using as the name
      logger.info(`Using username "${profileInfo.username}" as profile name`);

      // Final check to ensure we never return "Untitled Profile"
      if (name === 'Untitled Profile') {
        name = profileInfo.username || 'Profile';
        logger.debug(`Replaced "Untitled Profile" with: ${name}`);
      }

      // Default value information
      const valueInfo = {
        valuePerPts: 0.024, // Default base value
        currency: 'USD',
        symbol: '$',
        totalValue: profileMyPts.currentBalance * 0.024,
        formattedValue: `$${(profileMyPts.currentBalance * 0.024).toFixed(2)}`
      };

      // Calculate formatted balance string
      const balance = profileMyPts.currentBalance || 0;
      const formattedBalance = `${balance.toLocaleString()} MyPts`;

      logger.info(`Formatted profile name: "${name}", balance: ${balance}`);

      // Format the profile data
      const formattedData = {
        _id: profile._id,
        id: profile._id, // Include both formats for compatibility
        secondaryId: profile.secondaryId || null, // Include the secondary ID
        name: name,
        username: profileInfo.username,
        type: {
          category: profile.profileCategory || "individual",
          subtype: profile.profileType || "personal",
        },
        profileType: profile.profileType || "personal",
        profileCategory: profile.profileCategory || "individual",
        description: "", // No direct equivalent in new model
        accessToken: profileInfo.accessToken || "",
        // Include balance information in multiple formats for compatibility
        balance: balance,
        formattedBalance: formattedBalance,
        balanceInfo: {
          balance: balance,
          lifetimeEarned: profileMyPts.lifetimeMypts || 0,
          lifetimeSpent: 0, // Not available in new model
          lastTransaction: null, // Not available in new model
          value: valueInfo
        },
        // Always include the raw profile data to ensure consistent access to ProfileFormat
        _rawProfile: profile
      };

      // Final check to ensure name is never "Untitled Profile"
      if (formattedData.name === 'Untitled Profile') {
        formattedData.name = profileInfo.username || 'Profile';
        logger.warn(`Fixed "Untitled Profile" in final output to: ${formattedData.name}`);
      }


      logger.info(`Final formatted profile name: "${formattedData.name}"`);
      return formattedData;
    } catch (error) {
      logger.error('Error formatting profile data:', error);

      // Log the profile data that caused the error
      try {
        logger.error(`Profile data that caused error: ${JSON.stringify({
          id: profile._id,
          profileInfo: profile.profileInformation,
          sections: profile.sections?.map(s => ({ key: s.key, fields: s.fields?.map(f => ({ key: f.key })) }))
        })}`);
      } catch (logError) {
        logger.error('Error logging profile data:', logError);
      }

      // Try to get username from profile information
      let fallbackName = 'Profile';
      try {
        if (profile.profileInformation?.username) {
          fallbackName = profile.profileInformation.username;
          logger.info(`Using username "${fallbackName}" for fallback profile name`);
        }
      } catch (nameError) {
        logger.error('Error getting username for fallback:', nameError);
      }

      // Return basic profile data if formatting fails
      const fallbackData = {
        _id: profile._id,
        id: profile._id,
        secondaryId: profile.secondaryId || null, // Include the secondary ID
        name: fallbackName, // Use username or 'Profile' instead of 'Untitled Profile'
        username: profile.profileInformation?.username || '',
        profileType: 'personal',
        profileCategory: 'individual',
        type: {
          category: 'individual',
          subtype: 'personal',
        },
        description: '',
        accessToken: '',
        balance: 0,
        formattedBalance: '0 MyPts',
        balanceInfo: {
          balance: 0,
          lifetimeEarned: 0,
          lifetimeSpent: 0,
          lastTransaction: null,
          value: {
            valuePerPts: 0.024,
            currency: 'USD',
            symbol: '$',
            totalValue: 0,
            formattedValue: '$0.00'
          }
        }
      };

      logger.info('Returning fallback profile data');
      return fallbackData;
    }
  }

  /** POST /p */
  createProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    // Check profile creation limit before proceeding
    const profileLimit = await subscriptionService.checkProfileLimit(userId);
    if (!profileLimit.allowed) {
      const upgradeOptions = await subscriptionService.getUpgradeRecommendations(userId);
      const user = await User.findById(userId);

      res.status(429).json({
        success: false,
        message: 'Profile creation limit reached',
        code: 'LIMIT_REACHED',
        usage: {
          current: profileLimit.current,
          max: profileLimit.max,
          percentage: profileLimit.percentage
        },
        upgradePrompt: {
          title: 'Profile Limit Reached',
          message: this.getProfileLimitMessage(user?.subscription.plan || 'free', profileLimit.max),
          upgradeOptions
        }
      });
      return;
    }

    const { templateId, profileInformation, sections, profileLocation } = req.body as CreateProfileBody;
    if (!templateId) throw createHttpError(400, 'templateId is required');
    if (!profileInformation?.username) throw createHttpError(400, 'username is required');

    // Automatically remove spaces from username
    profileInformation.username = profileInformation.username.replace(/\s+/g, '');

    const ip = req.headers['x-forwarded-for'] as string;
    const profile = await this.service.createProfileWithContent(
      userId,
      templateId,
      profileInformation,
      sections,
      undefined,
      profileLocation,
      ip
    );

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(profile);

    // Check if user is approaching profile limit and include upgrade prompt
    const updatedProfileLimit = await subscriptionService.checkProfileLimit(userId);
    let upgradePrompt = null;

    if (updatedProfileLimit.nearingLimit || updatedProfileLimit.percentage >= 80) {
      const upgradeOptions = await subscriptionService.getUpgradeRecommendations(userId);
      const user = await User.findById(userId);

      upgradePrompt = {
        title: 'Profile Limit Warning',
        message: this.getProfileWarningMessage(user?.subscription.plan || 'free', updatedProfileLimit.current, updatedProfileLimit.max),
        upgradeOptions,
        showAt: 'bottom' // Suggest showing as non-blocking notification
      };
    }

    const responseData: any = {
      success: true,
      profile: formattedProfile,
      usage: {
        profiles: updatedProfileLimit
      }
    };

    if (upgradePrompt) {
      responseData.upgradePrompt = upgradePrompt;
    }

    res.status(201).json(responseData);
  });

  /** POST /p/:profileId/fields */
  setEnabledFields = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    const toggles = req.body as ProfileFieldToggle[];
    if (!Array.isArray(toggles)) throw createHttpError(400, 'Expected array of field toggles');

    const updated = await this.service.setEnabledFields(profileId, userId, toggles);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** PUT /p/:profileId/content */
  updateProfileContent = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    const { profileInformation, updates, profileLocation, ProfileFormat } = req.body as { profileInformation: { username?: string; title?: string; accountHolder?: string; relationshipToAccountHolder?: string; }, updates: ProfileFieldUpdate[], profileLocation: { city?: string; stateOrProvince?: string; country?: string; coordinates?: { latitude: number; longitude: number; }; }, ProfileFormat: { profileImage?: string; coverImage?: string; profileLogo?: string; customization?: { theme?: { primaryColor?: string; secondaryColor?: string; accent?: string; background?: string; text?: string; font?: string; }; layout?: { sections?: Array<{ id: string; type: string; order: number; visible: boolean }>; gridStyle?: 'right-sided' | 'centered' | 'left-sided'; animation?: 'fade' | 'slide' | 'zoom'; }; }; customCSS?: string; updatedAt: Date; } };

    if (!Array.isArray(updates)) throw createHttpError(400, 'Expected array of field updates');

    // Automatically remove spaces from username if provided
    if (profileInformation?.username) {
      profileInformation.username = profileInformation.username.replace(/\s+/g, '');
    }

    const updated = await this.service.updateProfileComprehensive(profileId, userId, profileInformation, profileLocation, ProfileFormat, updates);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** GET /p/:profileId */
  getProfile = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    // Check if skipAutoSync is requested via query parameter
    const skipAutoSync = req.query.skipAutoSync === 'true';

    const profile = await this.service.getProfile(profileId, skipAutoSync);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(profile);

    res.json({ success: true, profile: formattedProfile });
  });

  /** GET /view/profile/:username */
  getProfileByUsername = asyncHandler(async (req: Request, res: Response) => {
    const { username } = req.params;
    if (!username) throw createHttpError(400, 'Username is required');

    // Check if skipAutoSync is requested via query parameter
    const skipAutoSync = req.query.skipAutoSync === 'true';

    const profile = await this.service.getProfileByUsername(username, skipAutoSync);

    // **AUTO-GENERATE SHARE TOKEN FOR ACCESS CONTROL AND TRACKING**
    // This ensures /view/profile/username URLs have proper permissions and analytics
    let shareToken = null;
    let shareUrl = null;
    let accessLevel = 'basic';
    let allowedSections = ['basic', 'social', 'contact'];
    let customPermissions = null;
    let filteredProfile = profile;

    try {
      // Import ProfileShareLinkService dynamically
      const { ProfileShareLinkService } = await import('../services/profile-share-link.service');
      const shareService = new ProfileShareLinkService();
      
      // Check if a basic share token already exists for this profile
      const existingShareLinks = await shareService.getProfileShareLinks(
        profile._id.toString(), 
        profile.profileInformation.creator.toString()
      );
      
      // Look for existing basic access share link (permanent, no expiry, no view limit)
      const existingBasicLink = existingShareLinks.find(link => 
        link.accessLevel === 'basic' && 
        link.isActive && 
        !link.expiresAt && 
        !link.maxViews
      );
      
      if (existingBasicLink) {
        // Use existing share token and apply its access control
        shareToken = existingBasicLink.shareToken;
        shareUrl = `${process.env.FRONTEND_URL}/profile/shared/${shareToken}`;
        
        // Apply the same access control as share tokens
        const shareResult = await shareService.getProfileByShareToken(shareToken, {
          accessedBy: undefined, // Anonymous access
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          location: undefined // Could be enhanced with geolocation
        });
        
        filteredProfile = shareResult.profile;
        accessLevel = shareResult.accessLevel;
        allowedSections = shareResult.allowedSections;
        customPermissions = shareResult.customPermissions;
        
        logger.info(`Applied existing share token access control for profile ${profile._id}: ${shareToken}`);
      } else {
        // Create a new basic share token for access control
        const shareLink = await shareService.createShareLink(
          profile._id.toString(),
          profile.profileInformation.creator.toString(),
          {
            accessLevel: 'basic',
            allowedSections: ['basic', 'social', 'contact'],
            customPermissions: {
              canViewContactInfo: true,
              canViewSocialLinks: true,
              canViewAnalytics: false,
              canViewVault: false,
              canDownload: false,
              canShare: true
            }
          }
        );
        
        shareToken = shareLink.shareToken;
        shareUrl = `${process.env.FRONTEND_URL}/profile/shared/${shareToken}`;
        
        // Apply the same access control as share tokens
        const shareResult = await shareService.getProfileByShareToken(shareToken, {
          accessedBy: undefined, // Anonymous access
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          location: undefined // Could be enhanced with geolocation
        });
        
        filteredProfile = shareResult.profile;
        accessLevel = shareResult.accessLevel;
        allowedSections = shareResult.allowedSections;
        customPermissions = shareResult.customPermissions;
        
        logger.info(`Created new share token and applied access control for profile ${profile._id}: ${shareToken}`);
      }
    } catch (shareError) {
      logger.warn(`Failed to generate/apply share token for profile ${profile._id}:`, shareError);
      // Continue with full profile access as fallback (original behavior)
      filteredProfile = profile;
    }

    // Format the profile data for frontend consumption (using filtered profile)
    const formattedProfile = this.formatProfileData(filteredProfile as any);

    // Add share token and access control information to response
    const response: any = { 
      success: true, 
      profile: formattedProfile 
    };
    
    if (shareToken) {
      response.shareToken = shareToken;
      response.shareUrl = shareUrl;
      response.accessLevel = accessLevel;
      response.allowedSections = allowedSections;
      if (customPermissions) {
        response.customPermissions = customPermissions;
      }
    }

    res.json(response);
  });

  /** GET /view/:sharecode */
  getProfileBySharecode = asyncHandler(async (req: Request, res: Response) => {
    const { sharecode } = req.params;
    if (!sharecode) throw createHttpError(400, 'Sharecode is required');

    // Check if skipAutoSync is requested via query parameter
    const skipAutoSync = req.query.skipAutoSync === 'true';

    // First get the profile by secondaryId to get the shareToken
    const profile = await this.service.getProfileBySecondaryId(sharecode, skipAutoSync);
    
    if (!profile.profileInformation?.shareToken) {
      throw createHttpError(404, 'Profile share token not found');
    }

    // Use the ProfileShareLinkService to get the profile with proper access control
    try {
      const { ProfileShareLinkService } = await import('../services/profile-share-link.service');
      const shareService = new ProfileShareLinkService();
      
      // Get profile by share token with access control and permissions
      const shareResult = await shareService.getProfileByShareToken(
        profile.profileInformation.shareToken,
        {
          accessedBy: req.user?._id?.toString(), // Optional - user might be logged in
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          location: undefined // Could be enhanced with geolocation
        }
      );

      // Format the filtered profile data for frontend consumption
      const formattedProfile = this.formatProfileData(shareResult.profile);

      // Return response with access control information
      const response: any = { 
        success: true, 
        profile: formattedProfile,
        accessLevel: shareResult.accessLevel,
        allowedSections: shareResult.allowedSections
      };

      if (shareResult.customPermissions) {
        response.customPermissions = shareResult.customPermissions;
      }

      res.json(response);

    } catch (shareError) {
      logger.warn(`Failed to get profile by share token for sharecode ${sharecode}:`, shareError);
      
      // Fallback: return basic profile info without sensitive data
      const basicProfile = {
        _id: profile._id,
        id: profile._id,
        secondaryId: profile.secondaryId,
        name: profile.profileInformation?.username || 'Profile',
        username: profile.profileInformation?.username,
        profileType: profile.profileType,
        profileCategory: profile.profileCategory,
        type: {
          category: profile.profileCategory,
          subtype: profile.profileType,
        },
        description: '',
        accessToken: '',
        balance: 0,
        formattedBalance: '0 MyPts',
        balanceInfo: {
          balance: 0,
          lifetimeEarned: 0,
          lifetimeSpent: 0,
          lastTransaction: null,
          value: {
            valuePerPts: 0.024,
            currency: 'USD',
            symbol: '$',
            totalValue: 0,
            formattedValue: '$0.00'
          }
        }
      };

      res.json({ 
        success: true, 
        profile: basicProfile,
        accessLevel: 'basic',
        allowedSections: ['basic'],
        message: 'Limited profile access due to share token issues'
      });
    }
  });

  /** GET /p */
  getUserProfiles = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const profiles = await this.service.getUserProfiles(userId);

    // Format each profile for frontend consumption
    const formattedProfiles = profiles.map(profile => this.formatProfileData(profile));

    res.json({ success: true, profiles: formattedProfiles });
  });

  /** DELETE /p/:profileId */
  deleteProfile = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    // Check if we should also delete the user account
    const deleteUserAccount = req.query.deleteUserAccount === 'true';

    // Log the query parameters for debugging
    logger.info(`Query parameters: ${JSON.stringify(req.query)}`);
    logger.info(`deleteUserAccount parameter: ${deleteUserAccount}`);

    // Log request details for debugging
    logger.info(`Deleting profile ${profileId} by user ${userId}${deleteUserAccount ? ' with user account deletion' : ''}`);
    logger.info(`Request headers: ${JSON.stringify(req.headers)}`);
    logger.info(`User object: ${JSON.stringify({
      id: (req.user as any)?._id,
      role: (req.user as any)?.role,
      _doc: (req.user as any)?._doc ? { role: (req.user as any)?._doc?.role } : 'no _doc'
    })}`);

    // Get the profile to find the creator/owner
    const profile = await this.service.getProfile(profileId);
    if (!profile) throw createHttpError(404, 'Profile not found');

    // Delete the profile
    const deleted = await this.service.deleteProfile(profileId, userId);

    // If deleteUserAccount is true and the user is an admin, also delete the user account
    if (deleteUserAccount && deleted) {
      const user = req.user as any;

      // Get the role from various sources
      const userRole = user.role ||
                      (user._doc ? user._doc.role : null) ||
                      req.header('X-User-Role') ||
                      req.cookies['X-User-Role'];

      const isAdminHeader = req.header('X-Is-Admin') === 'true';
      const isAdminCookie = req.cookies['X-Is-Admin'] === 'true';

      // Log all role-related information for debugging
      logger.info(`Role check for deleteUserAccount: userRole=${userRole}, isAdminHeader=${isAdminHeader}, isAdminCookie=${isAdminCookie}`);
      logger.info(`User object: ${JSON.stringify({
        id: user._id,
        role: user.role,
        _doc: user._doc ? { role: user._doc.role } : 'no _doc'
      })}`);

      // Consider the user an admin if any of the admin indicators are present
      const isAdmin = [RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(userRole as RoleType) || isAdminHeader || isAdminCookie;

      // For testing purposes, always allow user deletion
      // In production, uncomment the admin check
      const forceAllowDeletion = true; // Set to false in production

      if (!isAdmin && !forceAllowDeletion) {
        logger.warn(`User ${user._id} attempted to delete user account but has role: ${userRole}`);
        throw createHttpError(403, 'Only admins can delete user accounts');
      } else if (!isAdmin && forceAllowDeletion) {
        logger.warn(`User ${user._id} is not an admin but deletion is being forced for testing`);
      }

      try {
        // Get the user ID associated with this profile (the creator)
        let creatorId = null;

        // Try different ways to get the creator ID
        if (profile.profileInformation?.creator) {
          if (typeof profile.profileInformation.creator === 'string') {
            creatorId = profile.profileInformation.creator;
          } else if (typeof profile.profileInformation.creator === 'object') {
            creatorId = profile.profileInformation.creator.toString();
          }
        }

        // If we still don't have a creator ID, try other fields
        // Use type assertion to access potential properties not in the type definition
        const profileAny = profile as any;

        if (!creatorId && profileAny.user) {
          if (typeof profileAny.user === 'string') {
            creatorId = profileAny.user;
          } else if (typeof profileAny.user === 'object') {
            creatorId = profileAny.user.toString();
          }
        }

        // If we still don't have a creator ID, try the owner field
        if (!creatorId && profileAny.owner) {
          if (typeof profileAny.owner === 'string') {
            creatorId = profileAny.owner;
          } else if (typeof profileAny.owner === 'object') {
            creatorId = profileAny.owner.toString();
          }
        }

        if (creatorId) {
          logger.info(`Admin ${user._id} is deleting user account ${creatorId} along with profile ${profileId}`);

          // Import the AuthService to delete the user
          const { AuthService } = require('../services/auth.service');

          // Log the creator ID and profile information for debugging
          logger.info(`Creator ID: ${creatorId}`);
          logger.info(`Profile information: ${JSON.stringify({
            id: profile._id,
            creator: profile.profileInformation?.creator,
            user: profileAny.user,
            owner: profileAny.owner
          })}`);

          // Delete the user account
          await AuthService.deleteUser(creatorId);
          logger.info(`User account ${creatorId} deleted along with profile ${profileId}`);
        } else {
          logger.warn(`Could not find creator ID for profile ${profileId}`);
          logger.warn(`Profile information: ${JSON.stringify({
            id: profile._id,
            creator: profile.profileInformation?.creator,
            user: profileAny.user,
            owner: profileAny.owner
          })}`);
        }
      } catch (error) {
        logger.error(`Failed to delete user account for profile ${profileId}:`, error);
        logger.error(`Error details: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
        logger.error(`Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
        // Log the error but don't fail the request - we'll still return success for the profile deletion
      }
    }

    res.json({ success: deleted });
  });

  /** PUT /p/:profileId/basic-info */
  updateProfileBasicInfo = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    const { username, description } = req.body;
    if (!username) throw createHttpError(400, 'Username is required');

    // Automatically remove spaces from username
    const cleanUsername = username.replace(/\s+/g, '');

    const updated = await this.service.updateProfileBasicInfo(
      profileId,
      userId,
      cleanUsername,
      description
    );

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** PUT /p/:profileId/links */
  updateProfileLinks = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    const { links } = req.body as { links: Array<{ name: string; value?: string; active?: boolean; }> };
    if (!Array.isArray(links)) throw createHttpError(400, 'Expected array of link updates');

    const updated = await this.service.updateProfileLinks(profileId, userId, links);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** PUT /p/:profileId/links/:linkName/toggle */
  toggleProfileLink = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId, linkName } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');
    if (!linkName) throw createHttpError(400, 'Link name is required');

    const { active } = req.body as { active: boolean };
    if (typeof active !== 'boolean') throw createHttpError(400, 'Active status must be a boolean');

    const updated = await this.service.toggleProfileLink(profileId, userId, linkName, active);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** POST /p/:profileId/links/sync */
  syncProfileLinks = asyncHandler(async (req: Request, res: Response) => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    // Verify user has permission to sync (either creator or admin)
    const profile = await this.service.getProfile(profileId);
    const user = await User.findById(userId);

    if (profile.profileInformation.creator.toString() !== userId.toString() &&
        (!user || !user.role || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role))) {
      throw createHttpError(403, 'You do not have permission to sync this profile');
    }

    const updated = await this.service.syncProfileLinks(profileId);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /** POST /default */
  createDefaultProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const userId = (req.user as any)?._id;
    if (!userId) throw createHttpError(401, 'Unauthorized');

    // **CRITICAL FIX: Check if user already has a personal profile to prevent duplicates**
    const existingPersonalProfile = await ProfileModel.findOne({
      'profileInformation.creator': userId,
      profileType: 'personal',
      profileCategory: 'individual'
    });

    if (existingPersonalProfile) {
      logger.info(`User ${userId} already has a personal profile: ${existingPersonalProfile._id}. Returning existing profile.`);

      // Format the existing profile data for frontend consumption
      const formattedProfile = this.formatProfileData(existingPersonalProfile);

      res.status(200).json({
        success: true,
        profile: formattedProfile,
        message: 'Personal profile already exists for this user'
      });
      return;
    }

    const profile = await this.service.createDefaultProfile(userId);

    // Format the profile data for frontend consumption
    const formattedProfile = this.formatProfileData(profile);

    res.status(201).json({ success: true, profile: formattedProfile });
  });

  /** GET /all - Get all profiles (admin only) */
  getAllProfiles = asyncHandler(async (req: Request, res: Response) => {
    const user = req.user as any;

    // Check if user is authenticated and has admin role
    if (!user?._id) throw createHttpError(401, 'Unauthorized');
    if (!user.role || ![RoleType.REGULAR_USER, RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role)) {
      throw createHttpError(403, 'Admin access required');
    }

    logger.info(`Admin user ${user._id} (${user.email}) requesting all profiles`);

    // Parse query parameters for pagination and filtering
    let limit = parseInt(req.query.limit as string);
    if (isNaN(limit) || limit < 0) limit = 0; // treat negative or invalid as no limit

    let page = parseInt(req.query.page as string) || 1;
    let skip = (page - 1) * (limit > 0 ? limit : 0);

    // Get filter parameters
    const nameFilter = req.query.name as string;
    const categoryFilter = req.query.category as string;
    const typeFilter = req.query.type as string;

    // Build filter object
    const filter: any = {};

    if (nameFilter) {
      // Case-insensitive search on profile name or username
      filter['$or'] = [
        { 'profileInformation.username': { $regex: nameFilter, $options: 'i' } },
        { 'profileInformation.title': { $regex: nameFilter, $options: 'i' } }
      ];
    }

    if (categoryFilter && categoryFilter !== 'all') {
      filter.profileCategory = categoryFilter;
    }

    if (typeFilter && typeFilter !== 'all') {
      filter.profileType = typeFilter;
    }

    // Get total count for pagination
    const totalCount = await this.service.countProfiles(filter);

    // Get profiles with pagination
    const profiles = await this.service.getAllProfiles(filter, skip, limit);

    // Format each profile for frontend consumption
    const formattedProfiles = profiles.map(profile => this.formatProfileData(profile));

    // Calculate pagination info
    let totalPages = 1;
    if (limit > 0) {
      totalPages = Math.ceil(totalCount / limit);
    } else {
      page = 1;
      skip = 0;
    }

    // Return response with pagination info
    res.json({
      success: true,
      profiles: formattedProfiles,
      pagination: {
        total: totalCount,
        page,
        pages: totalPages,
        limit
      }
    });
  });

  /**
   * Get community profiles with filters
   * @param req Express request object
   * @param res Express response object
   * @param next Express next function
   */
  async getCommunityProfiles(req: Request, res: Response, next: NextFunction) {
    try {
      const { town, city, country, ...otherFilters } = req.query;
      const skip = parseInt(req.query.skip as string) || 0;
      const limit = parseInt(req.query.limit as string) || 20;

      const filters: ProfileFilter = {
        ...(town && { town: town as string }),
        ...(city && { city: city as string }),
        ...(country && { country: country as string }),
        ...otherFilters
      };

      const profiles = await this.service.getCommunityProfiles(filters, skip, limit);

      res.json({
        success: true,
        data: profiles,
        pagination: {
          skip,
          limit,
          total: profiles.length
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete duplicate personal profiles (Admin only)
   * Keeps profiles with non-zero MYPTS balance, or the most recent one if all have zero balance
   */
  deleteDuplicatePersonalProfiles = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // Check if user is admin
    const user = req.user as any;
    if (!user || ![RoleType.ADMIN_USER, RoleType.SUPER_ADMIN].includes(user.role)) {
      throw createHttpError(403, 'Admin access required');
    }

    logger.info(`Admin ${user._id} initiated duplicate personal profile deletion`);

    try {
      const results = await this.service.deleteDuplicatePersonalProfiles();

      logger.info('Duplicate profile deletion completed successfully', {
        totalUsersProcessed: results.totalUsersProcessed,
        totalProfilesDeleted: results.totalProfilesDeleted,
        usersWithDuplicates: results.usersWithDuplicates
      });

      res.status(200).json({
        success: true,
        message: 'Duplicate personal profiles deleted successfully',
        data: results
      });
    } catch (error) {
      logger.error('Error deleting duplicate personal profiles:', error);
      throw createHttpError(500, 'Failed to delete duplicate personal profiles');
    }
  });

  /**
   * Get profile structure (sections and fields) for debugging
   */
  getProfileStructure = asyncHandler(async (req: Request, res: Response) => {
    const profileId = req.params.profileId;

    const structure = await this.service.getProfileStructure(profileId);

    res.status(200).json({
      success: true,
      data: structure,
      message: 'Profile structure retrieved successfully'
    });
  });

  /**
   * Sync profile structure with its template
   */
  syncProfileWithTemplate = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;
    if (!isValidObjectId(profileId)) throw createHttpError(400, 'Invalid profileId');

    const updated = await this.service.syncProfileWithTemplate(profileId);
    const formattedProfile = this.formatProfileData(updated);

    res.json({ success: true, profile: formattedProfile });
  });

  /**
   * Sync all personal profiles with their templates
   */
  async syncAllPersonalProfiles(req: Request, res: Response): Promise<void> {
    const { profileCategory } = req.query;

    const syncJobResult = await templateSyncService.syncAllProfilesByType(
      'personal',
      profileCategory as string
    );

    res.status(200).json({
      success: true,
      message: 'Background sync job started for all personal profiles',
      data: syncJobResult
    });
  }

  /**
   * Sync profiles by template ID (now returns background job)
   */
  async syncProfilesByTemplate(req: Request, res: Response): Promise<void> {
    const { templateId } = req.params;

    const syncJobResult = await templateSyncService.scheduleTemplateSync(templateId);

    res.status(200).json({
      success: true,
      message: 'Background sync job started',
      data: syncJobResult
    });
  }

  /** POST /p/:profileId/populate-basic - Populate basic information with existing data */
  populateBasicInformation = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;

    if (!profileId) {
      throw createHttpError(400, 'Profile ID is required');
    }

    const result = await this.service.populateBasicInformation(profileId);

    res.json({
      success: true,
      data: result,
      message: 'Basic information populated successfully'
    });
  });

  /**
   * Generate profile limit reached message based on current tier
   */
  private getProfileLimitMessage(currentTier: string, maxProfiles: number): string {
    const messages: Record<string, string> = {
      free: `You've reached your profile limit (${maxProfiles} profile). Upgrade to Basic to create 2 additional profiles and remove ads.`,
      basic: `You've reached your profile limit (${maxProfiles} profiles). Upgrade to Plus for 5 total profiles and custom QR codes.`,
      plus: `You've reached your profile limit (${maxProfiles} profiles). Upgrade to Premium for unlimited profiles and advanced business features.`
    };

    return messages[currentTier] || `You've reached your profile limit (${maxProfiles} profiles). Upgrade to access more profiles.`;
  }

  /**
   * Generate profile limit warning message when approaching limit
   */
  private getProfileWarningMessage(currentTier: string, current: number, max: number): string {
    const remaining = max - current;
    const nextTier = this.getNextTier(currentTier);

    if (remaining === 1) {
      return `You can create ${remaining} more profile. Consider upgrading to ${nextTier} for more profiles.`;
    } else {
      return `You can create ${remaining} more profiles. Consider upgrading to ${nextTier} for additional profiles.`;
    }
  }

  /**
   * Get the next tier name for upgrade suggestions
   */
  private getNextTier(currentTier: string): string {
    const upgrades: Record<string, string> = {
      free: 'Basic',
      basic: 'Plus',
      plus: 'Premium'
    };
    return upgrades[currentTier] || 'Premium';
  }

  /**
   * Publish a profile
   * @route PATCH /api/profiles/:profileId/publish
   * @access Private
   */
  public publishProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { profileId } = req.params;
    const userId = (req.user as any)?._id;

    if (!userId) {
      throw createHttpError(401, 'Unauthorized');
    }

    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    // Find the profile and verify ownership
    const profile = await ProfileModel.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // RBAC: Check permissions using the proper RBAC system
    const isOwner = profile.profileInformation.creator.toString() === userId.toString();
    
    // Import RBACService dynamically to avoid circular dependencies
    const { RBACService } = await import('../services/rbac.service');
    
    let canPublish = isOwner;
    
    // If not owner, check if user has admin permissions to publish profiles
    if (!isOwner) {
      canPublish = await RBACService.hasPermission(userId, 'profile.publish.any', {
        resourceId: profileId,
        ipAddress: req.ip
      });
    }

    if (!canPublish) {
      throw createHttpError(403, 'You do not have permission to publish this profile');
    }

    // Check if profile is already published
    if (profile.published) {
      throw createHttpError(400, 'Profile is already published');
    }

    // Admin override logging
    if (!isOwner) {
      logger.warn(`User ${userId} is publishing profile ${profileId} owned by ${profile.profileInformation.creator} via admin permissions`);
    }

    // Update profile to published status
    profile.published = true;
    profile.publishedAt = new Date();
    profile.publishedBy = userId;
    
    await profile.save();

    logger.info(`Profile ${profileId} published by user ${userId} (isOwner: ${isOwner})`);

    res.status(200).json({
      success: true,
      message: 'Profile published successfully',
      data: {
        profileId: profile._id,
        published: profile.published,
        publishedAt: profile.publishedAt,
        publishedBy: profile.publishedBy,
        isAdminAction: !isOwner
      }
    });
  });

  /**
   * Unpublish a profile
   * @route PATCH /api/profiles/:profileId/unpublish
   * @access Private
   */
  public unpublishProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { profileId } = req.params;
    const userId = (req.user as any)?._id;

    if (!userId) {
      throw createHttpError(401, 'Unauthorized');
    }

    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    // Find the profile and verify ownership
    const profile = await ProfileModel.findById(profileId);
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // RBAC: Check permissions using the proper RBAC system
    const isOwner = profile.profileInformation.creator.toString() === userId.toString();
    
    // Import RBACService dynamically to avoid circular dependencies
    const { RBACService } = await import('../services/rbac.service');
    
    let canUnpublish = isOwner;
    
    // If not owner, check if user has admin permissions to unpublish profiles
    if (!isOwner) {
      canUnpublish = await RBACService.hasPermission(userId, 'profile.unpublish.any', {
        resourceId: profileId,
        ipAddress: req.ip
      });
    }

    if (!canUnpublish) {
      throw createHttpError(403, 'You do not have permission to unpublish this profile');
    }

    // Check if profile is already unpublished
    if (!profile.published) {
      throw createHttpError(400, 'Profile is already unpublished');
    }

    // Admin override logging
    if (!isOwner) {
      logger.warn(`User ${userId} is unpublishing profile ${profileId} owned by ${profile.profileInformation.creator} via admin permissions`);
    }

    // Update profile to unpublished status
    profile.published = false;
    profile.publishedAt = undefined;
    profile.publishedBy = undefined;
    
    await profile.save();

    logger.info(`Profile ${profileId} unpublished by user ${userId} (isOwner: ${isOwner})`);

    res.status(200).json({
      success: true,
      message: 'Profile unpublished successfully',
      data: {
        profileId: profile._id,
        published: profile.published,
        isAdminAction: !isOwner
      }
    });
  });

  /**
   * Get profile publication status
   * @route GET /api/profiles/:profileId/publication-status
   * @access Private
   */
  public getProfilePublicationStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { profileId } = req.params;
    const userId = (req.user as any)?._id;

    if (!userId) {
      throw createHttpError(401, 'Unauthorized');
    }

    if (!isValidObjectId(profileId)) {
      throw createHttpError(400, 'Invalid profile ID');
    }

    // Find the profile and verify ownership
    const profile = await ProfileModel.findById(profileId).populate('publishedBy', 'name email');
    if (!profile) {
      throw createHttpError(404, 'Profile not found');
    }

    // RBAC: Check permissions using the proper RBAC system
    const isOwner = profile.profileInformation.creator.toString() === userId.toString();
    
    // Import RBACService dynamically to avoid circular dependencies
    const { RBACService } = await import('../services/rbac.service');
    
    let canView = isOwner;
    
    // If not owner, check if user has admin permissions to view publication status
    if (!isOwner) {
      canView = await RBACService.hasPermission(userId, 'profile.publication.view.any', {
        resourceId: profileId,
        ipAddress: req.ip
      });
    }

    if (!canView) {
      throw createHttpError(403, 'You do not have permission to view this profile publication status');
    }

    res.status(200).json({
      success: true,
      data: {
        profileId: profile._id,
        published: profile.published,
        publishedAt: profile.publishedAt,
        publishedBy: profile.publishedBy,
        isAdminView: !isOwner
      }
    });
  });

  /**
   * Get published profiles (public endpoint)
   * @route GET /api/profiles/published
   * @access Public
   */
  public getPublishedProfiles = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { profileType, profileCategory, limit, offset } = req.query;

    const filters = {
      profileType: profileType as string,
      profileCategory: profileCategory as string,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    };

    const profileService = new ProfileService();
    const profiles = await profileService.getPublishedProfiles(filters);

    res.status(200).json({
      success: true,
      data: profiles,
      pagination: {
        limit: filters.limit || 50,
        offset: filters.offset || 0,
        total: profiles.length
      }
    });
  });

  /**
   * Get publication statistics (admin only)
   * @route GET /api/profiles/publication-stats
   * @access Admin
   */
  public getPublicationStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await this.service.getPublicationStats();

      res.json({
        status: 'success',
        data: stats
      });
    } catch (error) {
      logger.error('Error getting publication stats:', error);
      throw createHttpError(500, 'Failed to get publication stats');
    }
  });

  /**
   * Transfer profile to another user
   * @route POST /api/profiles/:profileId/transfer
   */
  public transferProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const { profileId } = req.params;
      const { toUserId, reason } = req.body;
      const currentUserId = req.user?._id?.toString();

      if (!currentUserId) {
        throw createHttpError(400, 'User authentication required');
      }

      if (!toUserId) {
        throw createHttpError(400, 'Recipient user ID is required');
      }

      const result = await this.service.transferProfile(
        profileId,
        currentUserId,
        toUserId,
        reason
      );

      logger.info('Profile transferred successfully', {
        profileId,
        fromUserId: currentUserId,
        toUserId,
        reason
      });

      res.json({
        status: 'success',
        data: result
      });
    } catch (error) {
      logger.error('Error transferring profile:', error);
      throw error;
    }
  });

  /**
   * Claim a shared profile
   * @route POST /api/profiles/:profileId/claim
   */
  public claimProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const { profileId } = req.params;
      const currentUserId = req.user?._id?.toString();

      if (!currentUserId) {
        throw createHttpError(400, 'User authentication required');
      }

      const result = await this.service.claimProfile(
        profileId,
        currentUserId
      );

      logger.info('Profile claimed successfully', {
        profileId,
        userId: currentUserId
      });

      res.json({
        status: 'success',
        data: result
      });
    } catch (error) {
      logger.error('Error claiming profile:', error);
      throw error;
    }
  });
}
