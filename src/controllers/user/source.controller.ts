import { Request, Response } from 'express';
import { Source, SourceType, SourceCategory } from '../../models/Source';
import { logger } from '../../utils/logger';

export class UserSourceController {
  /**
   * Get all available sources for users
   */
  static async getAvailableSources(req: Request, res: Response) {
    try {
      const {
        category,
        type,
        search
      } = req.query;

      // Build filter query - only show active and enabled sources
      const filter: any = {
        isActive: true,
        isEnabled: true
      };
      
      if (search) {
        filter.$or = [
          { displayName: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      if (category) filter.category = category;
      if (type) filter.type = type;

      const sources = await Source.find(filter)
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata') // Exclude sensitive and admin data
        .sort({ displayName: 1 })
        .lean();

      // Group sources by category for better organization
      const groupedSources = sources.reduce((acc, source) => {
        if (!acc[source.category]) {
          acc[source.category] = [];
        }
        acc[source.category].push(source);
        return acc;
      }, {} as Record<string, any[]>);

      res.json({
        success: true,
        data: {
          sources,
          groupedSources,
          total: sources.length
        }
      });
    } catch (error) {
      logger.error('Error fetching available sources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch available sources'
      });
    }
  }

  /**
   * Get source details by ID
   */
  static async getSourceDetails(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const source = await Source.findOne({
        _id: id,
        isActive: true,
        isEnabled: true
      })
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata') // Exclude sensitive and admin data
        .lean();

      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found or not available'
        });
      }

      res.json({
        success: true,
        data: source
      });
    } catch (error) {
      logger.error('Error fetching source details:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch source details'
      });
    }
  }

  /**
   * Get source setup instructions
   */
  static async getSourceSetupInstructions(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const source = await Source.findOne({
        _id: id,
        isActive: true,
        isEnabled: true
      })
        .select('name displayName type category requiresSetup setupInstructions dataFields permissions')
        .lean();

      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found or not available'
        });
      }

      if (!source.requiresSetup) {
        return res.json({
          success: true,
          data: {
            ...source,
            setupInstructions: 'This source does not require additional setup.'
          }
        });
      }

      res.json({
        success: true,
        data: source
      });
    } catch (error) {
      logger.error('Error fetching source setup instructions:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch setup instructions'
      });
    }
  }

  /**
   * Get sources by category
   */
  static async getSourcesByCategory(req: Request, res: Response) {
    try {
      const { category } = req.params;

      // Validate category
      if (!Object.values(SourceCategory).includes(category as SourceCategory)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid category'
        });
      }

      const sources = await Source.find({
        category,
        isActive: true,
        isEnabled: true
      })
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata')
        .sort({ displayName: 1 })
        .lean();

      res.json({
        success: true,
        data: {
          category,
          sources,
          total: sources.length
        }
      });
    } catch (error) {
      logger.error('Error fetching sources by category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch sources by category'
      });
    }
  }

  /**
   * Get sources by type
   */
  static async getSourcesByType(req: Request, res: Response) {
    try {
      const { type } = req.params;

      // Validate type
      if (!Object.values(SourceType).includes(type as SourceType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid source type'
        });
      }

      const sources = await Source.find({
        type,
        isActive: true,
        isEnabled: true
      })
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata')
        .sort({ displayName: 1 })
        .lean();

      res.json({
        success: true,
        data: {
          type,
          sources,
          total: sources.length
        }
      });
    } catch (error) {
      logger.error('Error fetching sources by type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch sources by type'
      });
    }
  }

  /**
   * Get available categories
   */
  static async getAvailableCategories(req: Request, res: Response) {
    try {
      const categories = await Source.distinct('category', {
        isActive: true,
        isEnabled: true
      });

      const categoryStats = await Source.aggregate([
        {
          $match: {
            isActive: true,
            isEnabled: true
          }
        },
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            category: '$_id',
            count: 1,
            _id: 0
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          categories,
          stats: categoryStats
        }
      });
    } catch (error) {
      logger.error('Error fetching available categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch available categories'
      });
    }
  }

  /**
   * Get available types
   */
  static async getAvailableTypes(req: Request, res: Response) {
    try {
      const types = await Source.distinct('type', {
        isActive: true,
        isEnabled: true
      });

      const typeStats = await Source.aggregate([
        {
          $match: {
            isActive: true,
            isEnabled: true
          }
        },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            type: '$_id',
            count: 1,
            _id: 0
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          types,
          stats: typeStats
        }
      });
    } catch (error) {
      logger.error('Error fetching available types:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch available types'
      });
    }
  }

  /**
   * Search sources
   */
  static async searchSources(req: Request, res: Response) {
    try {
      const { q, category, type, limit = 10 } = req.query;

      if (!q || typeof q !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const filter: any = {
        isActive: true,
        isEnabled: true,
        $or: [
          { displayName: { $regex: q, $options: 'i' } },
          { description: { $regex: q, $options: 'i' } },
          { name: { $regex: q, $options: 'i' } }
        ]
      };

      if (category) filter.category = category;
      if (type) filter.type = type;

      const sources = await Source.find(filter)
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata')
        .sort({ displayName: 1 })
        .limit(parseInt(limit as string))
        .lean();

      res.json({
        success: true,
        data: {
          query: q,
          sources,
          total: sources.length
        }
      });
    } catch (error) {
      logger.error('Error searching sources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search sources'
      });
    }
  }

  /**
   * Get featured/popular sources
   */
  static async getFeaturedSources(req: Request, res: Response) {
    try {
      const { limit = 6 } = req.query;

      // Get sources that are most commonly used or featured
      // For now, we'll get the first few enabled sources
      // In the future, this could be based on usage statistics
      const sources = await Source.find({
        isActive: true,
        isEnabled: true
      })
        .select('-apiKey -apiSecret -createdBy -updatedBy -metadata')
        .sort({ displayName: 1 })
        .limit(parseInt(limit as string))
        .lean();

      res.json({
        success: true,
        data: {
          sources,
          total: sources.length
        }
      });
    } catch (error) {
      logger.error('Error fetching featured sources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch featured sources'
      });
    }
  }
} 