import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '../utils/asyncHandler';
import { subscriptionService } from '../services/subscription.service';
import createHttpError from 'http-errors';
import { logger } from '../utils/logger';

// @desc    Get available subscription plans
// @route   GET /api/subscriptions/plans
// @access  Public
export const getAvailablePlans = asyncHandler(async (req: Request, res: Response) => {
  const plans = await subscriptionService.getAvailablePlans();
  
  res.json({
    success: true,
    message: 'Available plans retrieved successfully',
    data: plans
  });
});

// @desc    Get user's current subscription
// @route   GET /api/subscriptions/my-subscription
// @access  Private
export const getMySubscription = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  
  const userPlan = await subscriptionService.getUserActivePlan(userId);
  
  if (!userPlan) {
    throw createHttpError(404, 'No active subscription found');
  }
  
  // Transform the response to swap planId and planData
  const userPlanData = userPlan.toObject();
  if (userPlanData.planId && userPlanData.planId._id) {
    // Store the full plan object in planData
    userPlanData.planData = userPlanData.planId;
    // Replace planId with just the ID string
    userPlanData.planId = userPlanData.planId._id;
  }
  
  res.json({
    success: true,
    message: 'Subscription details retrieved successfully',
    data: userPlanData
  });
});

// @desc    Subscribe to a plan
// @route   POST /api/subscriptions/subscribe
// @access  Private
export const subscribeToPlan = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  const { planId, billingCycle, paymentProvider } = req.body;
  
  if (!planId) {
    throw createHttpError(400, 'Plan ID is required');
  }
  
  const userPlan = await subscriptionService.assignPlanToUser(
    userId,
    planId,
    billingCycle || 'monthly',
    paymentProvider || 'test'
  );
  
  // Get the populated user plan data
  const populatedUserPlan = await subscriptionService.getUserActivePlan(userId);
  
  // Transform the response to swap planId and planData
  const userPlanData = populatedUserPlan!.toObject();
  if (userPlanData.planId && userPlanData.planId._id) {
    // Store the full plan object in planData
    userPlanData.planData = userPlanData.planId;
    // Replace planId with just the ID string
    userPlanData.planId = userPlanData.planId._id;
  }
  
  res.status(201).json({
    success: true,
    message: 'Subscription created successfully',
    data: userPlanData
  });
});

// @desc    Upgrade subscription
// @route   PUT /api/subscriptions/upgrade
// @access  Private
export const upgradeSubscription = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  const { planId, billingCycle, paymentProvider } = req.body;
  
  if (!planId) {
    throw createHttpError(400, 'Plan ID is required');
  }
  
  const userPlan = await subscriptionService.upgradeUserPlan(
    userId,
    planId,
    billingCycle || 'monthly',
    paymentProvider || 'test'
  );
  
  // Get the populated user plan data
  const populatedUserPlan = await subscriptionService.getUserActivePlan(userId);
  
  // Transform the response to swap planId and planData
  const userPlanData = populatedUserPlan!.toObject();
  if (userPlanData.planId && userPlanData.planId._id) {
    // Store the full plan object in planData
    userPlanData.planData = userPlanData.planId;
    // Replace planId with just the ID string
    userPlanData.planId = userPlanData.planId._id;
  }
  
  res.json({
    success: true,
    message: 'Subscription upgraded successfully',
    data: userPlanData
  });
});

// @desc    Cancel subscription
// @route   DELETE /api/subscriptions/cancel
// @access  Private
export const cancelSubscription = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  const { reason } = req.body;
  
  const success = await subscriptionService.cancelUserSubscription(userId, reason);
  
  res.json({
    success: true,
    message: 'Subscription canceled successfully',
    data: { canceled: success }
  });
});

// @desc    Get usage statistics
// @route   GET /api/subscriptions/usage
// @access  Private
export const getUsageStats = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  
  const usageStats = await subscriptionService.getUsageStats(userId);
  
  res.json({
    success: true,
    message: 'Usage statistics retrieved successfully',
    data: usageStats
  });
});

// @desc    Get upgrade recommendations
// @route   GET /api/subscriptions/upgrade-recommendations
// @access  Private
export const getUpgradeRecommendations = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  
  const recommendations = await subscriptionService.getUpgradeRecommendations(userId);
  
  res.json({
    success: true,
    message: 'Upgrade recommendations retrieved successfully',
    data: recommendations
  });
});

// @desc    Check if action is allowed
// @route   POST /api/subscriptions/check-action
// @access  Private
export const checkActionAllowed = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  const { action } = req.body;
  
  if (!action) {
    throw createHttpError(400, 'Action is required');
  }
  
  const result = await subscriptionService.checkActionAllowed(userId, action);
  
  res.json({
    success: true,
    message: 'Action check completed',
    data: result
  });
});

// @desc    Update usage statistics
// @route   POST /api/subscriptions/update-usage
// @access  Private
export const updateUsageStats = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  const { action, increment } = req.body;
  
  if (!action) {
    throw createHttpError(400, 'Action is required');
  }
  
  await subscriptionService.updateUsageStats(userId, action, increment || 1);
  
  res.json({
    success: true,
    message: 'Usage statistics updated successfully'
  });
});

// @desc    Process webhook from payment provider
// @route   POST /api/subscriptions/webhook/:provider
// @access  Public
export const processWebhook = asyncHandler(async (req: Request, res: Response) => {
  const { provider } = req.params;
  const payload = req.body;
  const signature = req.headers['stripe-signature'] || req.headers['flutterwave-signature'] || '';
  
  if (!['stripe', 'flutterwave', 'test'].includes(provider)) {
    throw createHttpError(400, 'Invalid payment provider');
  }
  
  const success = await subscriptionService.processWebhook(
    provider as 'stripe' | 'flutterwave' | 'test',
    payload,
    signature as string
  );
  
  if (success) {
    res.status(200).json({ received: true });
  } else {
    throw createHttpError(400, 'Webhook processing failed');
  }
});

// @desc    Test webhook endpoint for development
// @route   POST /api/subscriptions/test-webhook
// @access  Public
export const testWebhook = asyncHandler(async (req: Request, res: Response) => {
  const { subscriptionId, status, userId } = req.body;
  
  // Simulate webhook payload
  const testPayload = {
    subscriptionId,
    status,
    userId,
    timestamp: new Date().toISOString()
  };
  
  const success = await subscriptionService.processWebhook('test', testPayload, 'test-signature');
  
  res.json({
    success: true,
    message: 'Test webhook processed successfully',
    data: { processed: success, payload: testPayload }
  });
});

// @desc    Initialize default plans (Admin only)
// @route   POST /api/subscriptions/initialize-plans
// @access  Admin
export const initializeDefaultPlans = asyncHandler(async (req: Request, res: Response) => {
  await subscriptionService.initializeDefaultPlans();
  
  res.json({
    success: true,
    message: 'Default plans initialized successfully'
  });
});

// @desc    Get subscription history
// @route   GET /api/subscriptions/history
// @access  Private
export const getSubscriptionHistory = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  
  // This would need to be implemented in the service
  // For now, return a placeholder
  res.json({
    success: true,
    message: 'Subscription history retrieved successfully',
    data: {
      message: 'Subscription history feature coming soon'
    }
  });
});

// @desc    Get billing information
// @route   GET /api/subscriptions/billing
// @access  Private
export const getBillingInfo = asyncHandler(async (req: Request, res: Response) => {
  const user: any = req.user!;
  const userId = user._id.toString();
  
  const userPlan = await subscriptionService.getUserActivePlan(userId);
  
  if (!userPlan) {
    throw createHttpError(404, 'No active subscription found');
  }
  
  const billingInfo = {
    planName: userPlan.planName,
    planType: userPlan.planType,
    billingCycle: userPlan.billingCycle,
    paymentStatus: userPlan.paymentStatus,
    paymentProvider: userPlan.paymentProvider,
    lastPaymentDate: userPlan.lastPaymentDate,
    nextBillingDate: userPlan.nextBillingDate,
    autoRenew: userPlan.autoRenew,
    assignedAt: userPlan.assignedAt,
    expiresAt: userPlan.expiresAt
  };
  
  res.json({
    success: true,
    message: 'Billing information retrieved successfully',
    data: billingInfo
  });
});

// @desc    Update billing preferences
// @route   PUT /api/subscriptions/billing-preferences
// @access  Private
export const updateBillingPreferences = asyncHandler(async (req: Request, res: Response) => {
  const { autoRenew } = req.body;
  const userId = (req as any).user.id;

  const result = await subscriptionService.updateBillingPreferences(userId, { autoRenew });

  res.status(200).json({
    success: true,
    message: 'Billing preferences updated successfully',
    data: result
  });
});

// Admin Plan Management Controllers
export const createPlan = asyncHandler(async (req: Request, res: Response) => {
  const planData = req.body;

  const plan = await subscriptionService.createPlan(planData);

  res.status(201).json({
    success: true,
    message: 'Plan created successfully',
    data: plan
  });
});

export const updatePlan = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;
  const updateData = req.body;

  const plan = await subscriptionService.updatePlan(planId, updateData);

  res.status(200).json({
    success: true,
    message: 'Plan updated successfully',
    data: plan
  });
});

export const deletePlan = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;

  await subscriptionService.deletePlan(planId);

  res.status(200).json({
    success: true,
    message: 'Plan deleted successfully'
  });
});

export const getAllPlans = asyncHandler(async (req: Request, res: Response) => {
  const plans = await subscriptionService.getAllPlans();

  res.status(200).json({
    success: true,
    message: 'All plans retrieved successfully',
    data: plans
  });
});

export const getPlanById = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;

  const plan = await subscriptionService.getPlanById(planId);

  res.status(200).json({
    success: true,
    message: 'Plan retrieved successfully',
    data: plan
  });
});

export const togglePlanStatus = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;

  const plan = await subscriptionService.togglePlanStatus(planId);

  res.status(200).json({
    success: true,
    message: `Plan ${plan.isActive ? 'activated' : 'deactivated'} successfully`,
    data: plan
  });
});

export const getPlanUsageStats = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;

  const stats = await subscriptionService.getPlanUsageStats(planId);

  res.status(200).json({
    success: true,
    message: 'Plan usage statistics retrieved successfully',
    data: stats
  });
});

export const duplicatePlan = asyncHandler(async (req: Request, res: Response) => {
  const { planId } = req.params;
  const { newName, newPrice } = req.body;

  const newPlan = await subscriptionService.duplicatePlan(planId, { newName, newPrice });

  res.status(201).json({
    success: true,
    message: 'Plan duplicated successfully',
    data: newPlan
  });
}); 