import { Request, Response } from 'express';
import { RateReviewService, IReviewData, IReviewFilters } from '../services/rate-review.service';
import { logger } from '../utils/logger';
import { asyncHandler } from '../utils/asyncHandler';

export class RateReviewController {
  /**
   * Create a new review
   */
  static createReview = asyncHandler(async (req: Request, res: Response) => {
    const {
      rating,
      subject,
      reason,
      feedback,
      improvementCategories
    } = req.body;

    if (!rating || !feedback) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields: rating, feedback' 
      });
    }

    if (rating < 1 || rating > 5) {
      return res.status(400).json({ 
        success: false, 
        message: 'Rating must be between 1 and 5' 
      });
    }

    const reviewData: IReviewData = {
      rating,
      subject,
      reason,
      feedback,
      improvementCategories
    };

    const currentUserId = req.user?._id?.toString();
    const review = await RateReviewService.createReview(reviewData, currentUserId!);

    logger.info('Review created successfully', { 
      reviewId: review._id, 
      userId: currentUserId 
    });

    res.status(201).json({ 
      status: 'success', 
      data: review 
    });
  });

  /**
   * Get reviews with filtering and pagination
   */
  static getReviews = asyncHandler(async (req: Request, res: Response) => {
    const { 
      page = 1, 
      limit = 10, 
      rating, 
      startDate, 
      endDate, 
      sortBy, 
      sortOrder 
    } = req.query;

    const filters: IReviewFilters = {};
    
    if (rating) filters.rating = Number(rating);
    if (startDate && endDate) {
      filters.dateRange = {
        start: new Date(startDate as string),
        end: new Date(endDate as string)
      };
    }
    if (sortBy) filters.sortBy = sortBy as any;
    if (sortOrder) filters.sortOrder = sortOrder as any;

    const result = await RateReviewService.getReviews(
      filters,
      { page: Number(page), limit: Number(limit) }
    );

    res.status(200).json({
      status: 'success',
      data: result
    });
  });

  /**
   * Get review by ID
   */
  static getReviewById = asyncHandler(async (req: Request, res: Response) => {
    const { reviewId } = req.params;

    if (!reviewId) {
      return res.status(400).json({
        success: false,
        message: 'Review ID is required'
      });
    }

    const review = await RateReviewService.getReviewById(reviewId);

    res.status(200).json({
      status: 'success',
      data: review
    });
  });

  /**
   * Update a review
   */
  static updateReview = asyncHandler(async (req: Request, res: Response) => {
    const { reviewId } = req.params;
    const {
      rating,
      subject,
      reason,
      feedback,
      improvementCategories
    } = req.body;

    if (!reviewId) {
      return res.status(400).json({
        success: false,
        message: 'Review ID is required'
      });
    }

    const updates: Partial<IReviewData> = {};
    if (rating !== undefined) updates.rating = rating;
    if (subject !== undefined) updates.subject = subject;
    if (reason !== undefined) updates.reason = reason;
    if (feedback !== undefined) updates.feedback = feedback;
    if (improvementCategories !== undefined) updates.improvementCategories = improvementCategories;

    // Remove undefined values
    Object.keys(updates).forEach(key => {
      if (updates[key as keyof IReviewData] === undefined) {
        delete updates[key as keyof IReviewData];
      }
    });

    const currentUserId = req.user?._id?.toString();
    const updatedReview = await RateReviewService.updateReview(reviewId, currentUserId!, updates);

    res.status(200).json({
      status: 'success',
      data: updatedReview
    });
  });

  /**
   * Delete a review
   */
  static deleteReview = asyncHandler(async (req: Request, res: Response) => {
    const { reviewId } = req.params;

    if (!reviewId) {
      return res.status(400).json({
        success: false,
        message: 'Review ID is required'
      });
    }

    const currentUserId = req.user?._id?.toString();
    const result = await RateReviewService.deleteReview(reviewId, currentUserId!);

    res.status(200).json({
      status: 'success',
      data: result
    });
  });

  /**
   * Get review statistics
   */
  static getReviewStats = asyncHandler(async (req: Request, res: Response) => {
    const stats = await RateReviewService.getReviewStats();

    res.status(200).json({
      status: 'success',
      data: stats
    });
  });

  /**
   * Get user's reviews
   */
  static getUserReviews = asyncHandler(async (req: Request, res: Response) => {
    const { page = 1, limit = 10 } = req.query;
    const currentUserId = req.user?._id?.toString();

    const result = await RateReviewService.getUserReviews(currentUserId!, {
      page: Number(page),
      limit: Number(limit)
    });

    res.status(200).json({
      status: 'success',
      data: result
    });
  });

  /**
   * Get all reviews for admin (all users)
   */
  static getAllReviewsForAdmin = asyncHandler(async (req: Request, res: Response) => {
    const { 
      page = 1, 
      limit = 20, 
      rating, 
      startDate, 
      endDate, 
      sortBy, 
      sortOrder 
    } = req.query;

    const filters: IReviewFilters = {};
    
    if (rating) filters.rating = Number(rating);
    if (startDate && endDate) {
      filters.dateRange = {
        start: new Date(startDate as string),
        end: new Date(endDate as string)
      };
    }
    if (sortBy) filters.sortBy = sortBy as any;
    if (sortOrder) filters.sortOrder = sortOrder as any;

    const result = await RateReviewService.getAllReviewsForAdmin(
      filters,
      { page: Number(page), limit: Number(limit) }
    );

    res.status(200).json({
      status: 'success',
      data: result
    });
  });
} 