import { Request, Response } from 'express';
import { Source, SourceType, SourceCategory } from '../../models/Source';
import { logger } from '../../utils/logger';
import cloudinaryService from '../../utils/fileUploads';

// Helper function to validate MongoDB ObjectId
const validateObjectId = (id: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};

export class AdminSourceController {
  /**
   * Get all sources with pagination and filtering
   */
  static async getAllSources(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        type,
        category,
        isActive,
        isEnabled,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Build filter query
      const filter: any = {};
      
      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { displayName: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      if (type) filter.type = type;
      if (category) filter.category = category;
      if (isActive !== undefined) filter.isActive = isActive === 'true';
      if (isEnabled !== undefined) filter.isEnabled = isEnabled === 'true';

      // Build sort query
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const [sources, total] = await Promise.all([
        Source.find(filter)
          .select('-apiKey -apiSecret') // Exclude sensitive data
          .sort(sort)
          .skip(skip)
          .limit(limitNum)
          .populate('createdBy', 'fullName email')
          .populate('updatedBy', 'fullName email')
          .lean(),
        Source.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      res.json({
        success: true,
        data: sources,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      });
    } catch (error) {
      logger.error('Error fetching sources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch sources'
      });
    }
  }

  /**
   * Get a single source by ID
   */
  static async getSourceById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!validateObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid source ID'
        });
      }

      const source = await Source.findById(id)
        .populate('createdBy', 'fullName email')
        .populate('updatedBy', 'fullName email')
        .lean();

      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found'
        });
      }

      res.json({
        success: true,
        data: source
      });
    } catch (error) {
      logger.error('Error fetching source:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch source'
      });
    }
  }

  /**
   * Create a new source
   */
  static async createSource(req: Request, res: Response) {
    try {
      const {
        name,
        displayName,
        type,
        category,
        description,
        isActive = true,
        isEnabled = false,
        requiresSetup = true,
        setupInstructions,
        apiEndpoint,
        apiKey,
        apiSecret,
        webhookUrl,
        permissions = [],
        dataFields = [],
        syncSettings = {},
        rateLimits = {},
        metadata = {}
      } = req.body;

      // Validation
      const errors: string[] = [];

      // Required fields validation
      if (!name || typeof name !== 'string') {
        errors.push('Name is required and must be a string');
      } else if (name.length < 2 || name.length > 50) {
        errors.push('Name must be between 2 and 50 characters');
      } else if (!/^[a-z0-9_-]+$/.test(name)) {
        errors.push('Name can only contain lowercase letters, numbers, hyphens, and underscores');
      }

      if (!displayName || typeof displayName !== 'string') {
        errors.push('Display name is required and must be a string');
      } else if (displayName.length < 2 || displayName.length > 100) {
        errors.push('Display name must be between 2 and 100 characters');
      }

      if (!type || typeof type !== 'string') {
        errors.push('Type is required and must be a string');
      } else if (!Object.values(SourceType).includes(type as SourceType)) {
        errors.push('Invalid source type');
      }

      if (!category || typeof category !== 'string') {
        errors.push('Category is required and must be a string');
      } else if (!Object.values(SourceCategory).includes(category as SourceCategory)) {
        errors.push('Invalid source category');
      }

      // Optional fields validation
      if (description && (typeof description !== 'string' || description.length > 500)) {
        errors.push('Description must be a string and less than 500 characters');
      }

      if (setupInstructions && (typeof setupInstructions !== 'string' || setupInstructions.length > 2000)) {
        errors.push('Setup instructions must be a string and less than 2000 characters');
      }

      if (apiEndpoint && typeof apiEndpoint !== 'string') {
        errors.push('API endpoint must be a string');
      }

      if (apiKey && (typeof apiKey !== 'string' || apiKey.length > 500)) {
        errors.push('API key must be a string and less than 500 characters');
      }

      if (apiSecret && (typeof apiSecret !== 'string' || apiSecret.length > 500)) {
        errors.push('API secret must be a string and less than 500 characters');
      }

      if (webhookUrl && typeof webhookUrl !== 'string') {
        errors.push('Webhook URL must be a string');
      }

      if (permissions && !Array.isArray(permissions)) {
        errors.push('Permissions must be an array');
      }

      if (dataFields && !Array.isArray(dataFields)) {
        errors.push('Data fields must be an array');
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors
        });
      }

      // Check if source with same name already exists
      const existingSource = await Source.findOne({ name: name.toLowerCase() });
      if (existingSource) {
        return res.status(400).json({
          success: false,
          message: 'Source with this name already exists'
        });
      }

      // Process icon base64 upload
      let iconData = undefined;
      if (req.body.icon) {
        const iconBase64 = req.body.icon;
        if (typeof iconBase64 === 'string' && iconBase64.trim()) {
          try {
            // Upload base64 image to Cloudinary
            const uploadUrl = await cloudinaryService.uploadBase64Image(iconBase64.trim(), {
              folder: 'source-icons',
              resourceType: 'image',
              transformation: [
                { width: 64, height: 64, crop: 'fit' } // Optimize for icon size
              ]
            });

            iconData = {
              url: uploadUrl,
              filename: `icon_${name}_${Date.now()}`,
              originalName: 'icon.svg',
              mimeType: 'image/svg+xml',
              size: 0,
              uploadedAt: new Date()
            };

            logger.info(`Icon uploaded for source ${name}: ${uploadUrl}`);
          } catch (uploadError) {
            logger.error('Error uploading icon:', uploadError);
            return res.status(500).json({
              success: false,
              message: 'Failed to upload icon. Please ensure it\'s a valid base64 image.'
            });
          }
        }
      }

      const source = new Source({
        name: name.toLowerCase(),
        displayName,
        type,
        category,
        icon: iconData,
        description,
        isActive,
        isEnabled,
        requiresSetup,
        setupInstructions,
        apiEndpoint,
        apiKey,
        apiSecret,
        webhookUrl,
        permissions,
        dataFields,
        syncSettings,
        rateLimits,
        metadata,
        createdBy: req.user?._id,
        updatedBy: req.user?._id
      });

      await source.save();

      // Populate user references
      await source.populate('createdBy', 'fullName email');
      await source.populate('updatedBy', 'fullName email');

      logger.info(`Source created: ${source.name} by user ${req.user?._id}`);

      res.status(201).json({
        success: true,
        data: source,
        message: 'Source created successfully'
      });
    } catch (error) {
      logger.error('Error creating source:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create source'
      });
    }
  }

  /**
   * Update a source
   */
  static async updateSource(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Validation
      const errors: string[] = [];

      // Validate ObjectId
      if (!validateObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid source ID'
        });
      }

      // Validate update fields if provided
      if (updateData.name !== undefined) {
        if (typeof updateData.name !== 'string') {
          errors.push('Name must be a string');
        } else if (updateData.name.length < 2 || updateData.name.length > 50) {
          errors.push('Name must be between 2 and 50 characters');
        } else if (!/^[a-z0-9_-]+$/.test(updateData.name)) {
          errors.push('Name can only contain lowercase letters, numbers, hyphens, and underscores');
        }
      }

      if (updateData.displayName !== undefined) {
        if (typeof updateData.displayName !== 'string') {
          errors.push('Display name must be a string');
        } else if (updateData.displayName.length < 2 || updateData.displayName.length > 100) {
          errors.push('Display name must be between 2 and 100 characters');
        }
      }

      if (updateData.type !== undefined) {
        if (typeof updateData.type !== 'string') {
          errors.push('Type must be a string');
        } else if (!Object.values(SourceType).includes(updateData.type)) {
          errors.push('Invalid source type');
        }
      }

      if (updateData.category !== undefined) {
        if (typeof updateData.category !== 'string') {
          errors.push('Category must be a string');
        } else if (!Object.values(SourceCategory).includes(updateData.category)) {
          errors.push('Invalid source category');
        }
      }

      if (updateData.description !== undefined) {
        if (typeof updateData.description !== 'string' || updateData.description.length > 500) {
          errors.push('Description must be a string and less than 500 characters');
        }
      }

      if (updateData.setupInstructions !== undefined) {
        if (typeof updateData.setupInstructions !== 'string' || updateData.setupInstructions.length > 2000) {
          errors.push('Setup instructions must be a string and less than 2000 characters');
        }
      }

      if (updateData.apiEndpoint !== undefined) {
        if (typeof updateData.apiEndpoint !== 'string') {
          errors.push('API endpoint must be a string');
        }
      }

      if (updateData.apiKey !== undefined) {
        if (typeof updateData.apiKey !== 'string' || updateData.apiKey.length > 500) {
          errors.push('API key must be a string and less than 500 characters');
        }
      }

      if (updateData.apiSecret !== undefined) {
        if (typeof updateData.apiSecret !== 'string' || updateData.apiSecret.length > 500) {
          errors.push('API secret must be a string and less than 500 characters');
        }
      }

      if (updateData.webhookUrl !== undefined) {
        if (typeof updateData.webhookUrl !== 'string') {
          errors.push('Webhook URL must be a string');
        }
      }

      if (updateData.permissions !== undefined) {
        if (!Array.isArray(updateData.permissions)) {
          errors.push('Permissions must be an array');
        }
      }

      if (updateData.dataFields !== undefined) {
        if (!Array.isArray(updateData.dataFields)) {
          errors.push('Data fields must be an array');
        }
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors
        });
      }

      const source = await Source.findById(id);
      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found'
        });
      }

      // Ensure name is lowercase if provided
      if (updateData.name) {
        updateData.name = updateData.name.toLowerCase();
        
        // Check for name conflicts
        const existingSource = await Source.findOne({ 
          name: updateData.name, 
          _id: { $ne: id } 
        });
        if (existingSource) {
          return res.status(400).json({
            success: false,
            message: 'Source with this name already exists'
          });
        }
      }

      // Process icon base64 upload if provided
      if (updateData.icon && typeof updateData.icon === 'string' && updateData.icon.trim()) {
        try {
          // Upload base64 image to Cloudinary
          const uploadUrl = await cloudinaryService.uploadBase64Image(updateData.icon.trim(), {
            folder: 'source-icons',
            resourceType: 'image',
            transformation: [
              { width: 64, height: 64, crop: 'fit' } // Optimize for icon size
            ]
          });

          updateData.icon = {
            url: uploadUrl,
            filename: `icon_${source.name}_${Date.now()}`,
            originalName: 'icon.svg',
            mimeType: 'image/svg+xml',
            size: 0,
            uploadedAt: new Date()
          };

          logger.info(`Icon updated for source ${source.name}: ${uploadUrl}`);
        } catch (uploadError) {
          logger.error('Error uploading icon:', uploadError);
          return res.status(500).json({
            success: false,
            message: 'Failed to upload icon. Please ensure it\'s a valid base64 image.'
          });
        }
      }

      // Update the source
      Object.assign(source, updateData);
      source.updatedBy = req.user?._id;
      await source.save();

      // Populate user references
      await source.populate('createdBy', 'fullName email');
      await source.populate('updatedBy', 'fullName email');

      logger.info(`Source updated: ${source.name} by user ${req.user?._id}`);

      res.json({
        success: true,
        data: source,
        message: 'Source updated successfully'
      });
    } catch (error) {
      logger.error('Error updating source:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update source'
      });
    }
  }

  /**
   * Delete a source
   */
  static async deleteSource(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!validateObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid source ID'
        });
      }

      const source = await Source.findById(id);
      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found'
        });
      }

      // Check if source is currently enabled
      if (source.isEnabled) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete an enabled source. Disable it first.'
        });
      }

      await Source.findByIdAndDelete(id);

      logger.info(`Source deleted: ${source.name} by user ${req.user?._id}`);

      res.json({
        success: true,
        message: 'Source deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting source:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete source'
      });
    }
  }

  /**
   * Toggle source status (active/inactive)
   */
  static async toggleSourceStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { isActive, isEnabled } = req.body;

      if (!validateObjectId(id)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid source ID'
        });
      }

      const source = await Source.findById(id);
      if (!source) {
        return res.status(404).json({
          success: false,
          message: 'Source not found'
        });
      }

      const updateData: any = { updatedBy: req.user?._id };
      
      if (isActive !== undefined) {
        updateData.isActive = isActive;
      }
      
      if (isEnabled !== undefined) {
        updateData.isEnabled = isEnabled;
      }

      Object.assign(source, updateData);
      await source.save();

      logger.info(`Source status updated: ${source.name} (active: ${source.isActive}, enabled: ${source.isEnabled}) by user ${req.user?._id}`);

      res.json({
        success: true,
        data: source,
        message: 'Source status updated successfully'
      });
    } catch (error) {
      logger.error('Error updating source status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update source status'
      });
    }
  }

  /**
   * Get source statistics
   */
  static async getSourceStats(req: Request, res: Response) {
    try {
      const stats = await Source.aggregate([
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: ['$isActive', 1, 0] } },
            enabled: { $sum: { $cond: ['$isEnabled', 1, 0] } },
            byCategory: { $push: '$category' },
            byType: { $push: '$type' }
          }
        },
        {
          $project: {
            _id: 0,
            total: 1,
            active: 1,
            enabled: 1,
            inactive: { $subtract: ['$total', '$active'] },
            disabled: { $subtract: ['$total', '$enabled'] },
            categoryBreakdown: {
              $reduce: {
                input: '$byCategory',
                initialValue: {},
                in: {
                  $mergeObjects: [
                    '$$value',
                    { $literal: { '$$this': { $add: [{ $ifNull: ['$$value.$$this', 0] }, 1] } } }
                  ]
                }
              }
            },
            typeBreakdown: {
              $reduce: {
                input: '$byType',
                initialValue: {},
                in: {
                  $mergeObjects: [
                    '$$value',
                    { $literal: { '$$this': { $add: [{ $ifNull: ['$$value.$$this', 0] }, 1] } } }
                  ]
                }
              }
            }
          }
        }
      ]);

      res.json({
        success: true,
        data: stats[0] || {
          total: 0,
          active: 0,
          enabled: 0,
          inactive: 0,
          disabled: 0,
          categoryBreakdown: {},
          typeBreakdown: {}
        }
      });
    } catch (error) {
      logger.error('Error fetching source stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch source statistics'
      });
    }
  }

  /**
   * Get available source types and categories
   */
  static async getSourceOptions(req: Request, res: Response) {
    try {
      res.json({
        success: true,
        data: {
          types: Object.values(SourceType),
          categories: Object.values(SourceCategory)
        }
      });
    } catch (error) {
      logger.error('Error fetching source options:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch source options'
      });
    }
  }
} 