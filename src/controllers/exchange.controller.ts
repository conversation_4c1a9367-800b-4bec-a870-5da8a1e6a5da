import { Request, Response } from 'express';
import { Exchange, IExchange } from '../models/Exchange';
import { Profile } from '../models/profile.model';
import { Notification } from '../models/Notification';
import { User } from '../models/User';
import { logger } from '../utils/logger';
import { CustomError } from '../utils/errors';
import { getClientInfo } from '../utils/controllerUtils';
import { MyPtsModel } from '../models/my-pts.model';

/**
 * Exchange Controller
 * 
 * Handles profile exchanges between users with support for:
 * - Profile ID and Secondary ID lookup
 * - Multiple exchange channels (in_app, nfc, qr, manual)
 * - Notifications and MyPts rewards
 * - Analytics and tracking
 * - Security and validation
 */
export class ExchangeController {
  
  /**
   * Initiate an exchange with another profile
   * @route POST /api/exchange/initiate
   */
  static async initiateExchange(req: Request, res: Response) {
    try {
      const {
        senderProfileId,
        receiverProfileId,
        profileName,
        exchangeReason,
        channel = 'in_app',
        timestamp
      } = req.body;

      // Validate required fields
      if (!senderProfileId || !receiverProfileId) {
        return res.status(400).json({
          success: false,
          message: 'Sender and receiver profile IDs are required'
        });
      }

      // Validate channel
      const validChannels = ['in_app', 'nfc', 'qr', 'manual'];
      if (!validChannels.includes(channel)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid channel. Must be one of: in_app, nfc, qr, manual'
        });
      }

      // Get client info for tracking
      const clientInfo = await getClientInfo(req);

      // Find sender profile (by ID or secondary ID)
      const senderProfile = await Profile.findOne({
        $or: [
          { _id: senderProfileId },
          { secondaryId: senderProfileId }
        ]
      });

      if (!senderProfile) {
        return res.status(404).json({
          success: false,
          message: 'Sender profile not found'
        });
      }

      // Find receiver profile (by ID or secondary ID)
      const receiverProfile = await Profile.findOne({
        $or: [
          { _id: receiverProfileId },
          { secondaryId: receiverProfileId }
        ]
      });

      if (!receiverProfile) {
        return res.status(404).json({
          success: false,
          message: 'Receiver profile not found'
        });
      }

      // Prevent self-exchange
      if (senderProfile._id.toString() === receiverProfile._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot exchange with yourself'
        });
      }

      // Check if exchange already exists
      const existingExchange = await Exchange.findOne({
        $or: [
          {
            senderProfileId: senderProfile._id,
            receiverProfileId: receiverProfile._id,
            status: { $in: ['pending', 'completed'] }
          },
          {
            senderProfileId: receiverProfile._id,
            receiverProfileId: senderProfile._id,
            status: { $in: ['pending', 'completed'] }
          }
        ]
      });

      if (existingExchange) {
        return res.status(409).json({
          success: false,
          message: 'Exchange already exists between these profiles',
          exchangeId: existingExchange.exchangeId
        });
      }

      // Create exchange
      const exchange = new Exchange({
        senderProfileId: senderProfile._id,
        receiverProfileId: receiverProfile._id,
        senderSecondaryId: senderProfile.secondaryId,
        receiverSecondaryId: receiverProfile.secondaryId,
        profileName,
        exchangeReason,
        channel,
        exchangeData: {
          senderProfile: {
            id: senderProfile._id,
            secondaryId: senderProfile.secondaryId,
            name: senderProfile.profileInformation?.name,
            username: senderProfile.profileInformation?.username,
            title: senderProfile.profileInformation?.title
          },
          receiverProfile: {
            id: receiverProfile._id,
            secondaryId: receiverProfile.secondaryId,
            name: receiverProfile.profileInformation?.name,
            username: receiverProfile.profileInformation?.username,
            title: receiverProfile.profileInformation?.title
          }
        },
        metadata: {
          deviceInfo: {
            userAgent: clientInfo.userAgent,
            ipAddress: clientInfo.ip,
            deviceType: clientInfo.device
          }
        }
      });

      await exchange.save();

      // Award MyPts to sender
      const myPtsAwarded = 5; // Base points for initiating exchange
      await ExchangeController.awardMyPts(senderProfile._id, myPtsAwarded, 'exchange_initiated');

      // Send notification to receiver
      await ExchangeController.sendExchangeNotification(exchange, 'receiver');

      // Log the exchange
      logger.info('Exchange initiated successfully', {
        exchangeId: exchange.exchangeId,
        senderProfileId: senderProfile._id,
        receiverProfileId: receiverProfile._id,
        channel,
        myPtsAwarded
      });

      res.status(201).json({
        success: true,
        message: 'Exchange initiated successfully',
        exchangeId: exchange.exchangeId,
        myPtsAwarded
      });

    } catch (error) {
      logger.error('Exchange initiation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to initiate exchange'
      });
    }
  }

  /**
   * Get exchange history for a profile
   * @route GET /api/exchange/history
   */
  static async getExchangeHistory(req: Request, res: Response) {
    try {
      const { profileId, status, limit = 20, offset = 0 } = req.query;

      if (!profileId) {
        return res.status(400).json({
          success: false,
          message: 'Profile ID is required'
        });
      }

      // Find profile (by ID or secondary ID)
      const profile = await Profile.findOne({
        $or: [
          { _id: profileId },
          { secondaryId: profileId }
        ]
      });

      if (!profile) {
        return res.status(404).json({
          success: false,
          message: 'Profile not found'
        });
      }

      // Build query
      const query: any = {
        $or: [
          { senderProfileId: profile._id },
          { receiverProfileId: profile._id }
        ]
      };

      if (status) {
        query.status = status;
      }

      // Get exchanges with pagination
      const exchanges = await Exchange.find(query)
        .populate('senderProfileId', 'profileInformation.name profileInformation.username secondaryId')
        .populate('receiverProfileId', 'profileInformation.name profileInformation.username secondaryId')
        .sort({ createdAt: -1 })
        .limit(Number(limit))
        .skip(Number(offset));

      // Transform data for response
      const exchangeHistory = exchanges.map(exchange => {
        const isSender = exchange.senderProfileId.toString() === profile._id.toString();
        const otherProfile = isSender ? exchange.receiverProfileId : exchange.senderProfileId;
        
        return {
          exchangeId: exchange.exchangeId,
          senderProfileId: exchange.senderProfileId,
          receiverProfileId: exchange.receiverProfileId,
          profileName: exchange.profileName,
          exchangeReason: exchange.exchangeReason,
          channel: exchange.channel,
          timestamp: exchange.createdAt,
          status: exchange.status,
          isSender,
          otherProfile: {
            id: otherProfile._id,
            secondaryId: otherProfile.secondaryId,
            name: otherProfile.profileInformation?.name,
            username: otherProfile.profileInformation?.username
          },
          myPtsAwarded: exchange.analytics?.myPtsAwarded || 0
        };
      });

      // Get total count
      const totalCount = await Exchange.countDocuments(query);

      res.json({
        success: true,
        exchanges: exchangeHistory,
        pagination: {
          total: totalCount,
          limit: Number(limit),
          offset: Number(offset),
          hasMore: Number(offset) + exchanges.length < totalCount
        }
      });

    } catch (error) {
      logger.error('Get exchange history error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get exchange history'
      });
    }
  }

  /**
   * Get detailed info for a specific exchange
   * @route GET /api/exchange/:exchangeId
   */
  static async getExchangeDetails(req: Request, res: Response) {
    try {
      const { exchangeId } = req.params;

      if (!exchangeId) {
        return res.status(400).json({
          success: false,
          message: 'Exchange ID is required'
        });
      }

      const exchange = await Exchange.findOne({ exchangeId })
        .populate('senderProfileId', 'profileInformation.name profileInformation.username secondaryId profileInformation.title')
        .populate('receiverProfileId', 'profileInformation.name profileInformation.username secondaryId profileInformation.title');

      if (!exchange) {
        return res.status(404).json({
          success: false,
          message: 'Exchange not found'
        });
      }

      // Mark as viewed if not already
      if (!exchange.analytics?.viewed) {
        exchange.analytics = exchange.analytics || {};
        exchange.analytics.viewed = true;
        exchange.analytics.viewedAt = new Date();
        await exchange.save();
      }

      res.json({
        success: true,
        exchange: {
          exchangeId: exchange.exchangeId,
          senderProfileId: exchange.senderProfileId,
          receiverProfileId: exchange.receiverProfileId,
          profileName: exchange.profileName,
          exchangeReason: exchange.exchangeReason,
          channel: exchange.channel,
          timestamp: exchange.createdAt,
          status: exchange.status,
          exchangeData: exchange.exchangeData,
          analytics: exchange.analytics,
          metadata: exchange.metadata,
          expiresAt: exchange.expiresAt,
          completedAt: exchange.completedAt
        }
      });

    } catch (error) {
      logger.error('Get exchange details error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get exchange details'
      });
    }
  }

  /**
   * Complete an exchange (accept)
   * @route POST /api/exchange/:exchangeId/complete
   */
  static async completeExchange(req: Request, res: Response) {
    try {
      const { exchangeId } = req.params;
      const { profileId } = req.body; // ID of the profile completing the exchange

      if (!exchangeId || !profileId) {
        return res.status(400).json({
          success: false,
          message: 'Exchange ID and profile ID are required'
        });
      }

      const exchange = await Exchange.findOne({ exchangeId });

      if (!exchange) {
        return res.status(404).json({
          success: false,
          message: 'Exchange not found'
        });
      }

      // Verify the profile can complete this exchange
      if (exchange.receiverProfileId.toString() !== profileId && 
          exchange.senderProfileId.toString() !== profileId) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to complete this exchange'
        });
      }

      if (!exchange.canBeCompleted()) {
        return res.status(400).json({
          success: false,
          message: 'Exchange cannot be completed'
        });
      }

      // Update exchange status
      exchange.status = 'completed';
      exchange.completedAt = new Date();
      exchange.analytics = exchange.analytics || {};
      exchange.analytics.responded = true;
      exchange.analytics.respondedAt = new Date();

      await exchange.save();

      // Award MyPts to both parties
      const completionPts = 10;
      await ExchangeController.awardMyPts(exchange.senderProfileId, completionPts, 'exchange_completed');
      await ExchangeController.awardMyPts(exchange.receiverProfileId, completionPts, 'exchange_completed');

      // Send notifications
      await ExchangeController.sendExchangeNotification(exchange, 'sender', 'completed');
      await ExchangeController.sendExchangeNotification(exchange, 'receiver', 'completed');

      logger.info('Exchange completed successfully', {
        exchangeId: exchange.exchangeId,
        completedBy: profileId
      });

      res.json({
        success: true,
        message: 'Exchange completed successfully',
        myPtsAwarded: completionPts
      });

    } catch (error) {
      logger.error('Complete exchange error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to complete exchange'
      });
    }
  }

  /**
   * Decline an exchange
   * @route POST /api/exchange/:exchangeId/decline
   */
  static async declineExchange(req: Request, res: Response) {
    try {
      const { exchangeId } = req.params;
      const { profileId, reason } = req.body;

      if (!exchangeId || !profileId) {
        return res.status(400).json({
          success: false,
          message: 'Exchange ID and profile ID are required'
        });
      }

      const exchange = await Exchange.findOne({ exchangeId });

      if (!exchange) {
        return res.status(404).json({
          success: false,
          message: 'Exchange not found'
        });
      }

      // Verify the profile can decline this exchange
      if (exchange.receiverProfileId.toString() !== profileId) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to decline this exchange'
        });
      }

      if (!exchange.canBeDeclined()) {
        return res.status(400).json({
          success: false,
          message: 'Exchange cannot be declined'
        });
      }

      // Update exchange status
      exchange.status = 'declined';
      exchange.declinedAt = new Date();
      if (reason) {
        exchange.exchangeData = exchange.exchangeData || {};
        exchange.exchangeData.customMessage = reason;
      }

      await exchange.save();

      // Send notification to sender
      await ExchangeController.sendExchangeNotification(exchange, 'sender', 'declined');

      logger.info('Exchange declined', {
        exchangeId: exchange.exchangeId,
        declinedBy: profileId,
        reason
      });

      res.json({
        success: true,
        message: 'Exchange declined successfully'
      });

    } catch (error) {
      logger.error('Decline exchange error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to decline exchange'
      });
    }
  }

  /**
   * Cancel an exchange
   * @route DELETE /api/exchange/:exchangeId
   */
  static async cancelExchange(req: Request, res: Response) {
    try {
      const { exchangeId } = req.params;
      const { profileId } = req.body;

      if (!exchangeId || !profileId) {
        return res.status(400).json({
          success: false,
          message: 'Exchange ID and profile ID are required'
        });
      }

      const exchange = await Exchange.findOne({ exchangeId });

      if (!exchange) {
        return res.status(404).json({
          success: false,
          message: 'Exchange not found'
        });
      }

      // Verify the profile can cancel this exchange
      if (exchange.senderProfileId.toString() !== profileId) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to cancel this exchange'
        });
      }

      if (!exchange.canBeCancelled()) {
        return res.status(400).json({
          success: false,
          message: 'Exchange cannot be cancelled'
        });
      }

      // Update exchange status
      exchange.status = 'cancelled';
      exchange.cancelledAt = new Date();

      await exchange.save();

      // Send notification to receiver
      await ExchangeController.sendExchangeNotification(exchange, 'receiver', 'cancelled');

      logger.info('Exchange cancelled', {
        exchangeId: exchange.exchangeId,
        cancelledBy: profileId
      });

      res.json({
        success: true,
        message: 'Exchange cancelled successfully'
      });

    } catch (error) {
      logger.error('Cancel exchange error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel exchange'
      });
    }
  }

  /**
   * Award MyPts to a profile
   */
  private static async awardMyPts(profileId: any, points: number, reason: string) {
    try {
      const myPts = await MyPtsModel.findOne({ profileId });
      
      if (myPts) {
        myPts.balance += points;
        myPts.lifetimeMypts += points;
        await myPts.save();
      } else {
        // Create new MyPts record if it doesn't exist
        await MyPtsModel.create({
          profileId,
          balance: points,
          lifetimeMypts: points
        });
      }

      logger.info('MyPts awarded', {
        profileId,
        points,
        reason
      });
    } catch (error) {
      logger.error('Failed to award MyPts:', error);
    }
  }

  /**
   * Send exchange notification
   */
  private static async sendExchangeNotification(
    exchange: IExchange, 
    recipient: 'sender' | 'receiver', 
    action: 'initiated' | 'completed' | 'declined' | 'cancelled' = 'initiated'
  ) {
    try {
      const profileId = recipient === 'sender' ? exchange.senderProfileId : exchange.receiverProfileId;
      
      // Get user associated with the profile
      const profile = await Profile.findById(profileId);
      if (!profile) return;

      const user = await User.findById(profile.profileInformation.creator);
      if (!user) return;

      // Create notification
      const notification = new Notification({
        recipient: user._id,
        type: `exchange_${action}`,
        title: `Profile Exchange ${action.charAt(0).toUpperCase() + action.slice(1)}`,
        message: ExchangeController.getNotificationMessage(exchange, action, recipient),
        category: 'social',
        channels: ['inApp', 'email'],
        priority: 'medium',
        relatedTo: {
          model: 'Exchange',
          id: exchange._id
        },
        action: {
          text: 'View Exchange',
          url: `/exchange/${exchange.exchangeId}`
        }
      });

      await notification.save();

      // Update exchange notification tracking
      if (recipient === 'sender') {
        exchange.notifications.senderNotified = true;
        exchange.notifications.senderNotificationId = notification._id;
      } else {
        exchange.notifications.receiverNotified = true;
        exchange.notifications.receiverNotificationId = notification._id;
      }

      await exchange.save();

      logger.info('Exchange notification sent', {
        exchangeId: exchange.exchangeId,
        recipient,
        action,
        notificationId: notification._id
      });
    } catch (error) {
      logger.error('Failed to send exchange notification:', error);
    }
  }

  /**
   * Get notification message based on action
   */
  private static getNotificationMessage(
    exchange: IExchange, 
    action: string, 
    recipient: 'sender' | 'receiver'
  ): string {
    const otherProfile = recipient === 'sender' ? 
      exchange.exchangeData?.receiverProfile?.name || 'Someone' :
      exchange.exchangeData?.senderProfile?.name || 'Someone';

    switch (action) {
      case 'initiated':
        return recipient === 'receiver' 
          ? `${otherProfile} wants to exchange profiles with you`
          : `Profile exchange initiated with ${otherProfile}`;
      case 'completed':
        return `Profile exchange with ${otherProfile} completed successfully`;
      case 'declined':
        return recipient === 'sender'
          ? `${otherProfile} declined your profile exchange`
          : `You declined the profile exchange`;
      case 'cancelled':
        return recipient === 'receiver'
          ? `${otherProfile} cancelled the profile exchange`
          : `You cancelled the profile exchange`;
      default:
        return `Profile exchange ${action} with ${otherProfile}`;
    }
  }
} 