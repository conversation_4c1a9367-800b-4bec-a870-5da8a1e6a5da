import { Request, Response } from 'express';
import { AccountService, ICreateAccountData, IUpdateAccountData } from '../services/account.service';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';
import createHttpError from 'http-errors';
import { asyncHandler } from '../utils/asyncHandler';

export class AccountController {
  /**
   * Create account
   * @route POST /api/accounts
   * @access Requires 'account.create' permission
   */
  static createAccount = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    // Check permission using RBAC
    const hasPermission = await RBACService.hasPermission(user._id, 'account.create', {
      ipAddress: req.ip
    });
    
    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to create accounts');
    }

    const accountData: ICreateAccountData = req.body;
    const account = await AccountService.createAccount(accountData, user._id);

    logger.info('Account created successfully', {
      accountId: account.account.userId,
      createdBy: user._id
    });

    res.status(201).json({
      status: 'success',
      message: 'Account created successfully',
      data: account
    });
  });

  /**
   * Get accounts
   * @route GET /api/accounts
   * @access Requires 'account.read.own' or 'account.read.all' permission
   */
  static getAccounts = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    // Check if user can read all accounts or just their own
    const canReadAll = await RBACService.hasPermission(user._id, 'account.read.all', {
      ipAddress: req.ip
    });
    

    console.log(user.role, 'role', user._id, 'id')
    const canReadOwn = await RBACService.hasPermission(user._id, 'account.read.own', {
      ipAddress: req.ip
    });

    if (!canReadAll && !canReadOwn) {
      throw createHttpError(403, 'Insufficient permissions to read accounts');
    }

    const { limit, page, filter } = req.query;
    const options = {
      limit: parseInt(limit as string) || 10,
      page: parseInt(page as string) || 1,
      filter: filter as string
    };

    // If user can only read their own accounts, ensure we filter by their userId
    const userId = canReadAll ? (req.query.userId as string) || user._id : user._id;
    
    const accounts = await AccountService.getAccounts(userId, options);

    logger.info('Accounts retrieved successfully', {
      requestedBy: user._id,
      canReadAll,
      totalAccounts: accounts.total
    });

    res.status(200).json({
      status: 'success',
      data: accounts
    });
  });

  /**
   * Get account by ID
   * @route GET /api/accounts/:id
   * @access Requires 'account.read.own' or 'account.read.all' permission
   */
  static getAccountById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;

    // Get the account first to check ownership
    const account = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = account.account.userId === user.secondaryId || 
                    account.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (account.account.accountType === 'secondary' && 
                     account.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.read.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.read.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to read this account');
    }

    logger.info('Account details retrieved successfully', {
      accountId,
      requestedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      data: account
    });
  });

  /**
   * Update account
   * @route PUT /api/accounts/:id
   * @access Requires 'account.update.own' or 'account.update.all' permission
   */
  static updateAccount = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const updateData: IUpdateAccountData = req.body;

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);

    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.update.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.update.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to update this account');
    }

    const updatedAccount = await AccountService.updateAccount(accountId, updateData);

    logger.info('Account updated successfully', {
      accountId,
      updatedFields: Object.keys(updateData),
      updatedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      message: 'Account updated successfully',
      data: updatedAccount
    });
  });

  /**
   * Deactivate account
   * @route PATCH /api/accounts/:id/deactivate
   * @access Requires 'account.deactivate.own' or 'account.deactivate.all' permission
   */
  static deactivateAccount = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const { reason } = req.body;

    if (!reason) {
      throw createHttpError(400, 'Deactivation reason is required');
    }

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.deactivate.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.deactivate.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to deactivate this account');
    }

    const result = await AccountService.deactivateAccount(accountId, reason);

    logger.info('Account deactivated successfully', {
      accountId,
      reason,
      deactivatedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      message: 'Account deactivated successfully',
      data: result
    });
  });

  /**
   * Permanently close an account
   * @route DELETE /api/accounts/:id
   * @access Requires 'account.delete.own' or 'account.delete.all' permission
   */
  static closeAccount = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const { confirmation, reason } = req.body;

    if (!confirmation) {
      throw createHttpError(400, 'Confirmation is required to permanently close account');
    }

    if (!reason) {
      throw createHttpError(400, 'Closure reason is required');
    }

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.delete.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.delete.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to close this account');
    }

    const result = await AccountService.closeAccount(accountId, reason);

    logger.info('Account permanently closed', {
      accountId,
      reason,
      closedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      message: 'Account permanently closed',
      data: result
    });
  });

  /**
   * Upgrade subscription
   * @route POST /api/accounts/:id/upgrade-subscription
   * @access Requires 'account.subscription.manage.own' or 'account.subscription.manage.all' permission
   */
  static upgradeSubscription = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const { plan, paymentMethod, amount } = req.body;

    if (!plan || !paymentMethod || amount === undefined) {
      throw createHttpError(400, 'Plan, payment method, and amount are required');
    }

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.subscription.manage.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.subscription.manage.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to manage subscription for this account');
    }

    const result = await AccountService.upgradeSubscription(accountId, plan, paymentMethod, amount);

    logger.info('Subscription upgraded successfully', {
      accountId,
      newPlan: plan,
      amount,
      paymentMethod,
      upgradedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      message: 'Subscription upgraded successfully',
      data: result
    });
  });

  /**
   * Fetch account activities
   * @route GET /api/accounts/:id/activities
   * @access Requires 'account.activities.read.own' or 'account.activities.read.all' permission
   */
  static getAccountActivities = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const { filter } = req.query;

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.activities.read.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.activities.read.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to read account activities');
    }

    const activities = await AccountService.getAccountActivities(
      accountId,
      (filter as 'recent' | 'old' | 'all') || 'all'
    );

    logger.info('Account activities retrieved successfully', {
      accountId,
      activityCount: activities.length,
      filter,
      requestedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      data: activities
    });
  });

  /**
   * Log a new account activity
   * @route POST /api/accounts/:id/activities
   * @access Requires 'account.activities.create.own' or 'account.activities.create.all' permission
   */
  static logAccountActivity = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;
    const { activity, timestamp } = req.body;

    if (!activity) {
      throw createHttpError(400, 'Activity description is required');
    }

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    
    // Fix ownership check: compare with the actual user document IDs
    // The account.userId might be secondaryId or a truncated _id, so we need to check both
    // Also check if this is a secondary account that the user created
    const isOwner = existingAccount.account.userId === user.secondaryId || 
                    existingAccount.account.userId === user._id.toString().slice(-8).toUpperCase() ||
                    user._id.toString() === accountId ||
                    user.secondaryId === accountId ||
                    // Check if this is a secondary account created by the current user
                    (existingAccount.account.accountType === 'secondary' && 
                     existingAccount.subscription.uplineAccount === user.username.replace('#', ''));

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.activities.create.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.activities.create.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to log account activities');
    }

    const result = await AccountService.logAccountActivity(accountId, activity, timestamp);

    logger.info('Account activity logged successfully', {
      accountId,
      activity,
      timestamp,
      loggedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      message: 'Activity logged successfully',
      data: result
    });
  });

  /**
   * Get management options for current user
   * @route GET /api/accounts/manage
   * @access Requires 'account.manage' permission
   */
  static getManagementOptions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    // Check permissions using RBAC
    const hasPermission = await RBACService.hasPermission(user._id, 'account.manage', {
      ipAddress: req.ip
    });

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to access account management');
    }

    const options = await AccountService.getManagementOptions();

    logger.info('Management options retrieved successfully', {
      requestedBy: user._id
    });

    res.status(200).json({
      status: 'success',
      data: options
    });
  });

  /**
   * Get all available account-specific actions
   * @route GET /api/accounts/:id/options
   * @access Requires 'account.read.own' or 'account.read.all' permission
   */
  static getAccountOptions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }

    const { accountId } = req.params;

    // Get the account first to check ownership
    const existingAccount = await AccountService.getAccountById(accountId);
    const isOwner = existingAccount.account.userId === user._id || 
                    existingAccount.account.userId === user.secondaryId;

    // Check permissions using RBAC
    let hasPermission = false;
    
    if (isOwner) {
      hasPermission = await RBACService.hasPermission(user._id, 'account.read.own', {
        ipAddress: req.ip
      });
    } else {
      hasPermission = await RBACService.hasPermission(user._id, 'account.read.all', {
        ipAddress: req.ip
      });
    }

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to read account options');
    }

    const options = await AccountService.getAccountOptions(accountId);

    logger.info('Account options retrieved successfully', {
      accountId,
      optionsCount: options.length,
      requestedBy: user._id,
      isOwner
    });

    res.status(200).json({
      status: 'success',
      data: options
    });
  });

  /**
   * Export accounts to file or email
   * @route POST /api/accounts/export
   * @access Requires 'account.export' permission
   */
  static exportAccounts = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const user = req.user as any;
    if (!user) {
      throw createHttpError(401, 'Authentication required');
    }


    // Check permissions using RBAC
    const hasPermission = await RBACService.hasPermission(user._id, 'account.export', {
      ipAddress: req.ip
    });

    if (!hasPermission) {
      throw createHttpError(403, 'Insufficient permissions to export accounts');
    }

    const { format, emailToSend } = req.body;

    if (!format || !['csv', 'json'].includes(format)) {
      throw createHttpError(400, 'Format must be either "csv" or "json"');
    }

    const result = await AccountService.exportAccounts(user._id, format, emailToSend);

    logger.info('Accounts exported successfully', {
      userId: user._id,
      format,
      emailToSend: !!emailToSend
    });

    res.json({
      status: 'success',
      data: result
    });
  });

  /**
   * Get transfer logs with icons
   * @route GET /api/accounts/transfer/logs
   */
  static getTransferLogs = asyncHandler(async (req: Request, res: Response) => {
    const { type, page, limit, userId } = req.query;
    const currentUserId = req.user?._id?.toString();

    const options = {
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 20,
      userId: userId as string || currentUserId
    };

    const result = await AccountService.getTransferLogs(
      type as 'account' | 'profile',
      options
    );

    logger.info('Transfer logs retrieved successfully', {
      type,
      page: options.page,
      limit: options.limit,
      total: result.total
    });

    res.json({
      status: 'success',
      data: result
    });
  });

  /**
   * Transfer account ownership (secondary accounts only)
   * @route POST /api/accounts/:accountId/transfer
   */
  static transferAccountOwnership = asyncHandler(async (req: Request, res: Response) => {
    const { accountId } = req.params;
    const { toUserId, reason } = req.body;
    const currentUserId = req.user?._id?.toString();

    if (!currentUserId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    if (!toUserId) {
      return res.status(400).json({
        success: false,
        message: 'Recipient user ID is required'
      });
    }

    const result = await AccountService.transferAccountOwnership(
      accountId,
      currentUserId,
      toUserId,
      reason
    );

    logger.info('Account ownership transferred successfully', {
      accountId,
      fromUserId: currentUserId,
      toUserId,
      reason
    });

    res.json({
      status: 'success',
      message: 'Accounts exported successfully',
      data: result
    });
  });

  /**
   * Get transfer statistics
   * @route GET /api/accounts/transfer/stats
   */
  static getTransferStats = asyncHandler(async (req: Request, res: Response) => {
    const { userId } = req.query;
    const currentUserId = req.user?._id?.toString();

    const result = await AccountService.getTransferStats(
      userId as string || currentUserId
    );

    logger.info('Transfer statistics retrieved successfully', {
      userId: userId as string || currentUserId
    });

    res.json({
      status: 'success',
      data: result.data
    });
  });
} 