import mongoose from 'mongoose';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { User } from '../models/User';
import UserPlan from '../models/UserPlan';
import Plan from '../models/Plan';

async function verifyUserPlans() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Get all users
    const users = await User.find({});
    logger.info(`Found ${users.length} users in database`);

    // Use the Free plan ID provided by the user
    const freePlanId = '688b300120fdcb1fabb5e585';
    
    // Get Free plan details
    const freePlan = await Plan.findById(freePlanId) as any;
    if (!freePlan) {
      logger.error('Free plan not found in database');
      return;
    }

    logger.info(`Free plan: ${freePlan._id} - ${freePlan.name}`);

    let usersWithActivePlans = 0;
    let usersWithFreePlan = 0;
    let usersWithoutPlans = 0;
    let usersWithOtherPlans = 0;

    const userPlanDetails: any[] = [];

    for (const user of users) {
      // Check if user has an active plan
      const activePlan = await UserPlan.findOne({
        userId: user._id,
        isActive: true
      });

      if (activePlan) {
        usersWithActivePlans++;
        
        if (activePlan.planId.toString() === freePlanId) {
          usersWithFreePlan++;
          userPlanDetails.push({
            userId: user._id,
            email: user.email,
            planName: activePlan.planName,
            planId: activePlan.planId,
            assignedAt: activePlan.assignedAt,
            expiresAt: activePlan.expiresAt,
            status: 'Free Plan'
          });
        } else {
          usersWithOtherPlans++;
          userPlanDetails.push({
            userId: user._id,
            email: user.email,
            planName: activePlan.planName,
            planId: activePlan.planId,
            assignedAt: activePlan.assignedAt,
            expiresAt: activePlan.expiresAt,
            status: 'Other Plan'
          });
        }
      } else {
        usersWithoutPlans++;
        userPlanDetails.push({
          userId: user._id,
          email: user.email,
          planName: 'None',
          planId: 'None',
          assignedAt: 'None',
          expiresAt: 'None',
          status: 'No Active Plan'
        });
      }
    }

    // Summary
    logger.info('=== User Plan Verification Summary ===');
    logger.info(`Total users: ${users.length}`);
    logger.info(`Users with active plans: ${usersWithActivePlans}`);
    logger.info(`Users with Free plan: ${usersWithFreePlan}`);
    logger.info(`Users with other plans: ${usersWithOtherPlans}`);
    logger.info(`Users without plans: ${usersWithoutPlans}`);

    // Show details for first 10 users
    logger.info('\n=== Sample User Plan Details (First 10) ===');
    userPlanDetails.slice(0, 10).forEach((detail, index) => {
      logger.info(`${index + 1}. ${detail.email} - ${detail.status} (${detail.planName})`);
    });

    // Check total active plans in database
    const totalActivePlans = await UserPlan.countDocuments({ isActive: true });
    logger.info(`\nTotal active UserPlan records in database: ${totalActivePlans}`);

    // Check plans by type
    const planBreakdown = await UserPlan.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$planName', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    logger.info('\n=== Plan Distribution ===');
    planBreakdown.forEach(plan => {
      logger.info(`${plan._id}: ${plan.count} users`);
    });

  } catch (error) {
    logger.error('Error in verifyUserPlans:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
verifyUserPlans()
  .then(() => {
    logger.info('Verification completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Verification failed:', error);
    process.exit(1);
  }); 