import mongoose from 'mongoose';
import { TransferLogModel } from '../models/transfer-log.model';
import { User } from '../models/User';
import { logger } from '../utils/logger';

// Sample data for seeding transfer logs
const sampleTransferLogs = [
  {
    type: 'profile' as const,
    action: 'claimed' as const,
    fromUserName: 'System',
    toUserName: '<PERSON>',
    icon: '👤✅',
    actionText: 'Claimed Personal Profile',
    myPts: 12,
    details: {
      profileType: 'personal',
      reason: 'Initial profile claim'
    }
  },
  {
    type: 'profile' as const,
    action: 'transferred' as const,
    fromUserName: '<PERSON>',
    toUserName: '<PERSON>',
    icon: '🔁👤',
    actionText: 'Transferred Personal Profile',
    myPts: 24,
    details: {
      profileType: 'personal',
      reason: 'Transfer to colleague'
    }
  },
  {
    type: 'account' as const,
    action: 'transferred' as const,
    fromUserName: '<PERSON>',
    toUserName: '<PERSON>',
    icon: '🔁💼',
    actionText: 'Transferred Secondary Account',
    myPts: 19,
    details: {
      accountType: 'SECONDARY_ACCOUNT',
      reason: 'Transfer to family member'
    }
  },
  {
    type: 'account' as const,
    action: 'ownership_change' as const,
    fromUserName: '<PERSON>',
    toUserName: 'Maria Garcia',
    icon: '🔄🔐',
    actionText: 'Transferred Account Ownership',
    myPts: 25,
    details: {
      accountType: 'SECONDARY_ACCOUNT',
      reason: 'Business transfer'
    }
  },
  {
    type: 'profile' as const,
    action: 'claimed' as const,
    fromUserName: 'System',
    toUserName: 'David Wilson',
    icon: '👤✅',
    actionText: 'Claimed Business Profile',
    myPts: 12,
    details: {
      profileType: 'business',
      reason: 'Business profile claim'
    }
  },
  {
    type: 'profile' as const,
    action: 'transferred' as const,
    fromUserName: 'Emily Davis',
    toUserName: 'Michael Brown',
    icon: '🔁👤',
    actionText: 'Transferred Academic Profile',
    myPts: 24,
    details: {
      profileType: 'academic',
      reason: 'Academic collaboration'
    }
  }
];

async function seedTransferLogs() {
  try {
    logger.info('Starting transfer logs seeding...');

    // Get some sample users for the transfer logs
    const users = await User.find().limit(10);
    
    if (users.length < 6) {
      logger.warn('Not enough users found. Creating sample transfer logs with system users.');
      
      // Create transfer logs with system users
      for (const logData of sampleTransferLogs) {
        const transferLog = new TransferLogModel({
          ...logData,
          fromUserId: new mongoose.Types.ObjectId(), // System user ID
          toUserId: new mongoose.Types.ObjectId(), // Sample user ID
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
        });
        
        await transferLog.save();
      }
    } else {
      // Create transfer logs with real users
      for (let i = 0; i < sampleTransferLogs.length; i++) {
        const logData = sampleTransferLogs[i];
        const fromUser = users[i % users.length];
        const toUser = users[(i + 1) % users.length];
        
        const transferLog = new TransferLogModel({
          ...logData,
          fromUserId: fromUser._id,
          toUserId: toUser._id,
          fromUserName: fromUser.fullName,
          toUserName: toUser.fullName,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
        });
        
        await transferLog.save();
      }
    }

    logger.info('Transfer logs seeded successfully!');
    
    // Log some statistics
    const totalLogs = await TransferLogModel.countDocuments();
    const accountLogs = await TransferLogModel.countDocuments({ type: 'account' });
    const profileLogs = await TransferLogModel.countDocuments({ type: 'profile' });
    
    logger.info(`Transfer logs statistics:`);
    logger.info(`  Total logs: ${totalLogs}`);
    logger.info(`  Account transfers: ${accountLogs}`);
    logger.info(`  Profile transfers: ${profileLogs}`);
    
  } catch (error) {
    logger.error('Error seeding transfer logs:', error);
    throw error;
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  // Connect to MongoDB
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database';
  
  mongoose.connect(mongoUri)
    .then(() => {
      logger.info('Connected to MongoDB');
      return seedTransferLogs();
    })
    .then(() => {
      logger.info('Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seedTransferLogs };