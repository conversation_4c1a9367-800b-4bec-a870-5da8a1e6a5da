#!/usr/bin/env ts-node

/**
 * Fixed Account Permissions Seed Script
 * 
 * This script fixes the bug in the original seeder where it was comparing
 * permission IDs with permission names instead of names with names.
 */

import mongoose from 'mongoose';
import { config } from '../config/config';
import { Permission, PermissionCategory } from '../models/Permission';
import { Role, RoleType } from '../models/Role';
import { logger } from '../utils/logger';

// Account permissions based on the account controller
const ACCOUNT_PERMISSIONS = [
  // Account creation
  {
    name: 'account.create',
    displayName: 'Create Accounts',
    description: 'Create new user accounts (primary or secondary)',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'create',
    conditions: [],
    metadata: { riskLevel: 'medium', auditRequired: true }
  },

  // Account reading permissions
  {
    name: 'account.read.own',
    displayName: 'View Own Accounts',
    description: 'View own account information and linked accounts',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'read',
    conditions: ['own_only'],
    metadata: { riskLevel: 'low' }
  },
  {
    name: 'account.read.all',
    displayName: 'View All Accounts',
    description: 'View any user account information (admin privilege)',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'read',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },

  // Account updating permissions
  {
    name: 'account.update.own',
    displayName: 'Update Own Accounts',
    description: 'Update own account information',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'update',
    conditions: ['own_only'],
    metadata: { riskLevel: 'low' }
  },
  {
    name: 'account.update.all',
    displayName: 'Update Any Account',
    description: 'Update any user account information (admin privilege)',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'update',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },

  // Account deactivation permissions
  {
    name: 'account.deactivate.own',
    displayName: 'Deactivate Own Accounts',
    description: 'Deactivate own account',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'deactivate',
    conditions: ['own_only'],
    metadata: { riskLevel: 'medium', auditRequired: true }
  },
  {
    name: 'account.deactivate.all',
    displayName: 'Deactivate Any Account',
    description: 'Deactivate any user account (admin privilege)',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'deactivate',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },

  // Account deletion permissions
  {
    name: 'account.delete.own',
    displayName: 'Delete Own Accounts',
    description: 'Delete own account',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'delete',
    conditions: ['own_only'],
    metadata: { riskLevel: 'high', auditRequired: true }
  },
  {
    name: 'account.delete.all',
    displayName: 'Delete Any Account',
    description: 'Delete any user account (admin privilege)',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'delete',
    conditions: [],
    metadata: { riskLevel: 'critical', auditRequired: true, requiresTwoFactor: true }
  },

  // Account management permissions
  {
    name: 'account.manage',
    displayName: 'Manage Accounts',
    description: 'Full account management capabilities',
    category: PermissionCategory.USER_MANAGEMENT,
    resource: 'account',
    action: 'manage',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },

  // Subscription management permissions
  {
    name: 'account.subscription.manage.own',
    displayName: 'Manage Own Subscriptions',
    description: 'Manage subscription plans for own accounts',
    category: PermissionCategory.FINANCE,
    resource: 'account',
    action: 'subscription_manage',
    conditions: ['own_only'],
    metadata: { riskLevel: 'medium', auditRequired: true }
  },
  {
    name: 'account.subscription.manage.all',
    displayName: 'Manage Any Subscription',
    description: 'Manage subscription plans for any user account (admin privilege)',
    category: PermissionCategory.FINANCE,
    resource: 'account',
    action: 'subscription_manage',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },

  // Account activities permissions
  {
    name: 'account.activities.read.own',
    displayName: 'View Own Account Activities',
    description: 'View activity logs for own accounts',
    category: PermissionCategory.ANALYTICS,
    resource: 'account',
    action: 'activities_read',
    conditions: ['own_only'],
    metadata: { riskLevel: 'low' }
  },
  {
    name: 'account.activities.read.all',
    displayName: 'View All Account Activities',
    description: 'View activity logs for any user account (admin privilege)',
    category: PermissionCategory.ANALYTICS,
    resource: 'account',
    action: 'activities_read',
    conditions: [],
    metadata: { riskLevel: 'high', auditRequired: true }
  },
  {
    name: 'account.activities.create.own',
    displayName: 'Log Own Account Activities',
    description: 'Create activity logs for own accounts',
    category: PermissionCategory.ANALYTICS,
    resource: 'account',
    action: 'activities_create',
    conditions: ['own_only'],
    metadata: { riskLevel: 'low' }
  },
  {
    name: 'account.activities.create.all',
    displayName: 'Log Any Account Activities',
    description: 'Create activity logs for any user account (admin privilege)',
    category: PermissionCategory.ANALYTICS,
    resource: 'account',
    action: 'activities_create',
    conditions: [],
    metadata: { riskLevel: 'medium', auditRequired: true }
  },

  // Account export permissions
  {
    name: 'account.export',
    displayName: 'Export Account Data',
    description: 'Export account data to CSV or JSON format',
    category: PermissionCategory.ANALYTICS,
    resource: 'account',
    action: 'export',
    conditions: [],
    metadata: { riskLevel: 'medium', auditRequired: true }
  }
];

// Role-permission assignments
const ROLE_PERMISSIONS = {
  [RoleType.REGULAR_USER]: [
    'account.read.own',
    'account.update.own',
    'account.deactivate.own',
    'account.delete.own',
    'account.subscription.manage.own',
    'account.activities.read.own',
    'account.activities.create.own'
  ],
  [RoleType.BETA_TESTER]: [
    'account.read.own',
    'account.update.own',
    'account.deactivate.own',
    'account.delete.own',
    'account.subscription.manage.own',
    'account.activities.read.own',
    'account.activities.create.own',
    'account.create', // Beta testers can create secondary accounts
    'account.manage'
  ],
  [RoleType.MERCHANT]: [
    'account.read.own',
    'account.update.own',
    'account.deactivate.own',
    'account.delete.own',
    'account.subscription.manage.own',
    'account.activities.read.own',
    'account.activities.create.own',
    'account.create',
    'account.manage',
    'account.export'
  ],
  [RoleType.CONTENT_MODERATOR]: [
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.subscription.manage.own',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.create',
    'account.manage'
  ],
  [RoleType.FINANCE_AUDITOR]: [
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.PROXY_ADMIN]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.ADMIN_USER]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.delete.own',
    'account.delete.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.MAJOR_ADMIN]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.delete.own',
    'account.delete.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.SUPRA_ADMIN]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.delete.own',
    'account.delete.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.SUPER_ADMIN]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.delete.own',
    'account.delete.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ],
  [RoleType.DEVELOPER]: [
    'account.create',
    'account.read.own',
    'account.read.all',
    'account.update.own',
    'account.update.all',
    'account.deactivate.own',
    'account.deactivate.all',
    'account.delete.own',
    'account.delete.all',
    'account.subscription.manage.own',
    'account.subscription.manage.all',
    'account.activities.read.own',
    'account.activities.read.all',
    'account.activities.create.own',
    'account.activities.create.all',
    'account.manage',
    'account.export'
  ]
};

async function seedAccountPermissionsFixed() {
  try {
    await mongoose.connect(config.MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Create account permissions
    logger.info('Creating account permissions...');
    for (const permissionData of ACCOUNT_PERMISSIONS) {
      const existingPermission = await Permission.findOne({ name: permissionData.name });
      
      if (!existingPermission) {
        const permission = new Permission({
          name: permissionData.name,
          displayName: permissionData.displayName,
          description: permissionData.description,
          category: permissionData.category,
          resource: permissionData.resource,
          action: permissionData.action,
          conditions: permissionData.conditions,
          metadata: permissionData.metadata,
          isActive: true
        });
        
        await permission.save();
        logger.info(`✅ Created permission: ${permissionData.name}`);
      } else {
        logger.info(`⏭️  Permission already exists: ${permissionData.name}`);
      }
    }

    // Assign permissions to roles - FIXED VERSION
    logger.info('Assigning permissions to roles...');
    for (const [roleType, permissionNames] of Object.entries(ROLE_PERMISSIONS)) {
      const role = await Role.findOne({ name: roleType });
      
      if (!role) {
        logger.warn(`⚠️  Role not found: ${roleType}`);
        continue;
      }

      // FIXED: Compare permission names with permission names, not IDs
      const existingPermissions = role.permissions || [];
      
      // Find permissions that should be added (not already assigned)
      const newPermissions = permissionNames.filter(permissionName => 
        !existingPermissions.includes(permissionName)
      );

      if (newPermissions.length > 0) {
        // Add new permissions to existing ones
        role.permissions = [...existingPermissions, ...newPermissions];
        await role.save();
        
        logger.info(`✅ Assigned ${newPermissions.length} new permissions to ${roleType}`);
        logger.info(`   Permissions: ${newPermissions.join(', ')}`);
      } else {
        logger.info(`⏭️  All permissions already assigned to ${roleType}`);
      }
    }

    logger.info('✅ Account permissions seeding completed successfully');

  } catch (error) {
    logger.error('❌ Error seeding account permissions:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  seedAccountPermissionsFixed();
}

export { seedAccountPermissionsFixed }; 