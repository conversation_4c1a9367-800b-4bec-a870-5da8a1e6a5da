#!/usr/bin/env ts-node

/**
 * Fix Role Validation Error <PERSON>ript
 * 
 * This script fixes the "admin_user is not a valid enum value" error
 * by ensuring the User model's role field is properly configured
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { RoleType } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function fixRoleValidationError() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Check all users with invalid role values
    logger.info('🔍 Checking for users with invalid role values...');
    
    // Get all users
    const users = await User.find({});
    logger.info(`📊 Found ${users.length} total users`);

    let fixedUsers = 0;
    let invalidUsers = 0;

    for (const user of users) {
      try {
        // Check if the user's role is valid
        if (user.role && !Object.values(RoleType).includes(user.role)) {
          logger.warn(`❌ User ${user.email} has invalid role: "${user.role}"`);
          invalidUsers++;
          
          // Fix the invalid role by setting it to REGULAR_USER
          user.role = RoleType.REGULAR_USER;
          await user.save();
          logger.info(`✅ Fixed user ${user.email} role to REGULAR_USER`);
          fixedUsers++;
        } else if (!user.role) {
          logger.warn(`⚠️  User ${user.email} has no role assigned`);
          invalidUsers++;
          
          // Set default role
          user.role = RoleType.REGULAR_USER;
          await user.save();
          logger.info(`✅ Set default role for user ${user.email}`);
          fixedUsers++;
        }
      } catch (error) {
        logger.error(`❌ Error processing user ${user.email}:`, error);
      }
    }

    logger.info('\n📋 SUMMARY:');
    logger.info(`Total users: ${users.length}`);
    logger.info(`Users with invalid roles: ${invalidUsers}`);
    logger.info(`Users fixed: ${fixedUsers}`);

    // Verify the fix by checking a few users
    logger.info('\n🔍 Verifying fix...');
    const sampleUsers = await User.find({}).limit(5);
    for (const user of sampleUsers) {
      logger.info(`User ${user.email}: role = "${user.role}" (valid: ${Object.values(RoleType).includes(user.role)})`);
    }

    logger.info('\n🎉 Role validation error fix completed!');

  } catch (error) {
    logger.error('❌ Error fixing role validation:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
fixRoleValidationError(); 