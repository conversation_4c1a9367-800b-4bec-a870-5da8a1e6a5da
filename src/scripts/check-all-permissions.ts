#!/usr/bin/env ts-node

/**
 * Check All Permissions Script
 * 
 * This script checks all permissions across all roles and identifies what's missing
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function checkAllPermissions() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Get all roles
    const roles = await Role.find({}).sort({ level: 1 });
    
    // All permissions used in controllers
    const allRequiredPermissions = [
      // Account permissions
      'account.create',
      'account.read.own',
      'account.read.all',
      'account.update.own',
      'account.update.all',
      'account.deactivate.own',
      'account.deactivate.all',
      'account.delete.own',
      'account.delete.all',
      'account.subscription.manage.own',
      'account.subscription.manage.all',
      'account.activities.read.own',
      'account.activities.read.all',
      'account.activities.create.own',
      'account.activities.create.all',
      'account.manage',
      'account.export',
      
      // User permissions
      'user.create',
      'user.read.all',
      
      // Profile permissions
      'profile.publish.any',
      'profile.unpublish.any',
      'profile.publication.view.any'
    ];

    logger.info(`📊 Checking ${roles.length} roles for ${allRequiredPermissions.length} required permissions\n`);

    for (const role of roles) {
      logger.info(`🎭 Role: ${role.name} (Level: ${role.level})`);
      logger.info(`📋 Total permissions: ${role.permissions.length}`);
      
      // Check which required permissions are missing
      const missingPermissions = allRequiredPermissions.filter(
        permission => !role.permissions.includes(permission)
      );
      
      if (missingPermissions.length > 0) {
        logger.info(`❌ Missing permissions (${missingPermissions.length}):`);
        missingPermissions.forEach(permission => {
          logger.info(`   - ${permission}`);
        });
      } else {
        logger.info(`✅ All required permissions are assigned`);
      }
      
      logger.info(''); // Empty line for readability
    }

    // Summary
    logger.info('📋 SUMMARY:');
    logger.info('Permissions that should be added to regular_user role:');
    logger.info('- account.export (for exporting accounts)');
    logger.info('- user.create (for creating users)');
    logger.info('- user.read.all (for reading all users)');
    logger.info('- profile.publish.any (for publishing profiles)');
    logger.info('- profile.unpublish.any (for unpublishing profiles)');
    logger.info('- profile.publication.view.any (for viewing published profiles)');

  } catch (error) {
    logger.error('❌ Error checking permissions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
checkAllPermissions(); 