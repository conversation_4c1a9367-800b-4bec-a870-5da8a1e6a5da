#!/usr/bin/env ts-node

/**
 * Fix Regular User Permissions Script
 * 
 * This script manually adds the missing account permissions to the regular_user role
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function fixRegularUserPermissions() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Find the regular_user role
    const regularUserRole = await Role.findOne({ name: 'regular_user' });
    if (!regularUserRole) {
      logger.error('❌ Regular user role not found!');
      return;
    }

    logger.info(`\n👤 Current Regular User Role:`);
    logger.info(`  Name: ${regularUserRole.name}`);
    logger.info(`  Total Permissions: ${regularUserRole.permissions.length}`);

    // Account permissions that should be assigned to regular_user
    const accountPermissionsToAdd = [
      'account.read.own',
      'account.update.own',
      'account.deactivate.own',
      'account.delete.own',
      'account.subscription.manage.own',
      'account.activities.read.own',
      'account.activities.create.own'
    ];

    logger.info(`\n🔧 Adding account permissions to regular_user role...`);

    // Check which permissions are missing
    const missingPermissions = accountPermissionsToAdd.filter(
      perm => !regularUserRole.permissions.includes(perm)
    );

    if (missingPermissions.length === 0) {
      logger.info('✅ All account permissions are already assigned!');
      return;
    }

    logger.info(`📋 Missing permissions: ${missingPermissions.length}`);
    missingPermissions.forEach(perm => logger.info(`  - ${perm}`));

    // Add the missing permissions
    const newPermissions = [...regularUserRole.permissions, ...missingPermissions];
    regularUserRole.permissions = newPermissions;

    // Save the updated role
    await regularUserRole.save();
    logger.info(`✅ Successfully added ${missingPermissions.length} permissions to regular_user role`);

    // Verify the changes
    const updatedRole = await Role.findOne({ name: 'regular_user' });
    if (updatedRole) {
      const accountPerms = updatedRole.permissions.filter(p => p.startsWith('account.'));
      logger.info(`\n✅ Verification - Account permissions in role: ${accountPerms.length}`);
      accountPerms.forEach(perm => logger.info(`  ✅ ${perm}`));
    }

    logger.info(`\n🎉 Regular user role now has ${updatedRole?.permissions.length} total permissions`);

  } catch (error) {
    logger.error('❌ Error fixing regular user permissions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

async function main() {
  try {
    await fixRegularUserPermissions();
  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
} 