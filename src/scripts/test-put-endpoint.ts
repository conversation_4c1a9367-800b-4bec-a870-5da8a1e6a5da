#!/usr/bin/env ts-node

/**
 * Test PUT Account Endpoint Script
 * 
 * This script tests the PUT /api/accounts/:id endpoint with secondary account IDs
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { AccountService } from '../services/account.service';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function testPutEndpoint() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Find a user with a secondaryId
    const user = await User.findOne({ secondaryId: { $exists: true, $ne: null } });
    
    if (!user) {
      logger.error('❌ No user with secondaryId found');
      return;
    }

    logger.info(`👤 Found user: ${user.email}`);
    logger.info(`🆔 Primary ID: ${user._id}`);
    logger.info(`🆔 Secondary ID: ${user.secondaryId}`);

    // Test 1: Get account by secondaryId
    logger.info('\n🧪 Test 1: Get account by secondaryId');
    try {
      const account = await AccountService.getAccountById(user.secondaryId as string);
      logger.info(`✅ Account found: ${account.account.firstName} ${account.account.lastName}`);
      logger.info(`   UserId: ${account.account.userId}`);
      logger.info(`   Account Type: ${account.account.accountType}`);
    } catch (error) {
      logger.error(`❌ Error getting account by secondaryId: ${error}`);
    }

    // Test 2: Update account by secondaryId
    logger.info('\n🧪 Test 2: Update account by secondaryId');
    try {
      const updateData = {
        fullName: `${user.fullName} (Test Update)`,
        phoneNumber: '+**********'
      };
      
      const updatedAccount = await AccountService.updateAccount(user.secondaryId as string, updateData);
      logger.info(`✅ Account updated successfully`);
      logger.info(`   New name: ${updatedAccount.account.firstName} ${updatedAccount.account.lastName}`);
      logger.info(`   UserId: ${updatedAccount.account.userId}`);
    } catch (error) {
      logger.error(`❌ Error updating account by secondaryId: ${error}`);
    }

    // Test 3: Get account by MongoDB ObjectId
    logger.info('\n🧪 Test 3: Get account by MongoDB ObjectId');
    try {
      const account = await AccountService.getAccountById(user._id.toString());
      logger.info(`✅ Account found: ${account.account.firstName} ${account.account.lastName}`);
      logger.info(`   UserId: ${account.account.userId}`);
    } catch (error) {
      logger.error(`❌ Error getting account by ObjectId: ${error}`);
    }

    // Test 4: Update account by MongoDB ObjectId
    logger.info('\n🧪 Test 4: Update account by MongoDB ObjectId');
    try {
      const updateData = {
        fullName: `${user.fullName} (ObjectId Update)`,
        phoneNumber: '+**********'
      };
      
      const updatedAccount = await AccountService.updateAccount(user._id.toString(), updateData);
      logger.info(`✅ Account updated successfully`);
      logger.info(`   New name: ${updatedAccount.account.firstName} ${updatedAccount.account.lastName}`);
      logger.info(`   UserId: ${updatedAccount.account.userId}`);
    } catch (error) {
      logger.error(`❌ Error updating account by ObjectId: ${error}`);
    }

    logger.info('\n🎉 All tests completed!');

  } catch (error) {
    logger.error('❌ Error testing PUT endpoint:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
testPutEndpoint(); 