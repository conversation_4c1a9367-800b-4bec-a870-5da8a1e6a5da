#!/usr/bin/env ts-node

/**
 * Check Free Plan Exists Script
 * 
 * This script checks if the free plan exists in the database
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import UserPlan from '../models/UserPlan';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function checkFreePlanExists() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Check if free plan exists in PLAN_TEMPLATES
    const freePlanId = '688b0d48fed319825c93120d';
    const freePlanTemplate = {
      name: 'Free',
      planType: 'free',
      price: 0,
      individualProfiles: 1,
      secondaryAccounts: 0,
      accessoryProfiles: 1,
      groupProfiles: 0,
      storageGB: 0.1,
      insightsAccess: 'none',
      nfcProductLinking: 0,
      qrCodeAccess: 'basic',
      scannerAccess: 'limited',
      communityAccess: 'view',
      myPtsRate: 1,
      payoutPriority: 'none',
      payoutDays: 0,
      rewardSystemAccess: false,
      bonusTriggers: 'none',
      gamifiedActivities: false,
      circleCreation: 'none',
      leadsReferrals: 'none',
      teamProxyRoles: false,
      affiliateProgram: 'none',
      addonMarketplace: false,
      supportType: 'community'
    };

    logger.info(`✅ Free plan template found: ${freePlanTemplate.name} (ID: ${freePlanId})`);
    logger.info(`📋 Plan details:`);
    logger.info(`   - Name: ${freePlanTemplate.name}`);
    logger.info(`   - Type: ${freePlanTemplate.planType}`);
    logger.info(`   - Price: $${freePlanTemplate.price}`);
    logger.info(`   - Individual Profiles: ${freePlanTemplate.individualProfiles}`);
    logger.info(`   - QR Code Access: ${freePlanTemplate.qrCodeAccess}`);
    logger.info(`   - Scanner Access: ${freePlanTemplate.scannerAccess}`);

    // Get user statistics
    const totalUsers = await User.countDocuments({});
    const usersWithActiveSubscriptions = await UserPlan.countDocuments({ isActive: true });
    const usersWithoutSubscriptions = totalUsers - usersWithActiveSubscriptions;

    logger.info(`\n📊 USER STATISTICS:`);
    logger.info(`   - Total users: ${totalUsers}`);
    logger.info(`   - Users with active subscriptions: ${usersWithActiveSubscriptions}`);
    logger.info(`   - Users without subscriptions: ${usersWithoutSubscriptions}`);

    if (usersWithoutSubscriptions > 0) {
      logger.info(`\n🚀 Ready to assign free subscriptions to ${usersWithoutSubscriptions} users!`);
      logger.info(`💡 Run the assign-free-subscription-to-all-users.ts script to proceed.`);
    } else {
      logger.info(`\n✅ All users already have active subscriptions!`);
    }

  } catch (error) {
    logger.error('❌ Error checking free plan:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
checkFreePlanExists(); 