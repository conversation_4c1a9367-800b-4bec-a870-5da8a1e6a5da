import mongoose from 'mongoose';
import { ProfileModel } from '../models/profile.model';
import { SharingService } from '../services/sharing.service';
import { logger } from '../utils/logger';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

class ProfileLinkRegenerator {
  private sharingService: SharingService;

  constructor() {
    this.sharingService = new SharingService();
  }

  /**
   * Regenerate profile links for all existing profiles
   */
  async regenerateAllProfileLinks() {
    try {
      logger.info('Starting profile link regeneration...');

      // Get ALL profiles (not just ones with secondaryId)
      const profiles = await ProfileModel.find({})
        .populate('profileInformation.creator', 'firstName lastName email');

      logger.info(`Found ${profiles.length} total profiles to process`);

      let successCount = 0;
      let errorCount = 0;
      const errors: Array<{ profileId: string; error: string }> = [];

      // Process profiles in batches to avoid memory issues
      const batchSize = 10;
      for (let i = 0; i < profiles.length; i += batchSize) {
        const batch = profiles.slice(i, i + batchSize);
        logger.info(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(profiles.length / batchSize)}`);

        // Process each profile in the batch
        for (const profile of batch) {
          try {
            await this.regenerateProfileLink(profile);
            successCount++;
            logger.info(`✅ Updated profile link for profile ${profile._id} (${profile.secondaryId || 'no secondaryId'})`);
          } catch (error) {
            errorCount++;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            errors.push({
              profileId: profile._id.toString(),
              error: errorMessage
            });
            logger.error(`❌ Failed to update profile link for profile ${profile._id}: ${errorMessage}`);
          }
        }

        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < profiles.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Log summary
      logger.info('=== Profile Link Regeneration Summary ===');
      logger.info(`Total profiles processed: ${profiles.length}`);
      logger.info(`Successful updates: ${successCount}`);
      logger.info(`Failed updates: ${errorCount}`);
      
      if (errors.length > 0) {
        logger.info('Errors encountered:');
        errors.forEach(({ profileId, error }) => {
          logger.error(`  - Profile ${profileId}: ${error}`);
        });
      }

      logger.info('Profile link regeneration completed!');
      return {
        total: profiles.length,
        success: successCount,
        errors: errorCount,
        errorDetails: errors
      };

    } catch (error) {
      logger.error('Error during profile link regeneration:', error);
      throw error;
    }
  }

  /**
   * Regenerate profile link for a single profile and update the database
   */
  private async regenerateProfileLink(profile: any) {
    try {
      // Generate new profile link using the sharing service
      const profileLinkData = await this.sharingService.generateProfileLink(profile._id);

      // Log the generated links for verification
      logger.info(`Generated links for profile ${profile._id} (${profile.secondaryId || 'no secondaryId'}):`);
      logger.info(`  Profile Link: ${profileLinkData.profileLink}`);
      logger.info(`  Share URL: ${profileLinkData.shareUrl}`);

      // Update the profile with the new profileLink and shareToken
      const updateData = {
        'profileInformation.profileLink': profileLinkData.profileLink,
        'profileInformation.shareToken': profileLinkData.shareUrl.split('/').pop() // Extract token from URL
      };

      // Update the profile in the database
      await ProfileModel.findByIdAndUpdate(profile._id, updateData, { new: true });

      logger.info(`✅ Database updated for profile ${profile._id} with new profileLink`);
      
      return profileLinkData;

    } catch (error) {
      logger.error(`Error regenerating profile link for ${profile._id}:`, error);
      throw error;
    }
  }

  /**
   * Regenerate profile link for a specific profile by ID
   */
  async regenerateProfileLinkById(profileId: string) {
    try {
      const profile = await ProfileModel.findById(profileId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      if (!profile.secondaryId) {
        throw new Error('Profile does not have a secondaryId');
      }

      logger.info(`Regenerating profile link for profile ${profileId} (${profile.secondaryId})`);
      
      const result = await this.regenerateProfileLink(profile);
      
      logger.info(`✅ Successfully regenerated profile link for ${profileId}`);
      return result;

    } catch (error) {
      logger.error(`Error regenerating profile link for ${profileId}:`, error);
      throw error;
    }
  }

  /**
   * Regenerate profile link for a specific profile by secondaryId
   */
  async regenerateProfileLinkBySecondaryId(secondaryId: string) {
    try {
      const profile = await ProfileModel.findOne({ secondaryId });
      if (!profile) {
        throw new Error('Profile not found');
      }

      logger.info(`Regenerating profile link for profile with secondaryId ${secondaryId}`);
      
      const result = await this.regenerateProfileLink(profile);
      
      logger.info(`✅ Successfully regenerated profile link for secondaryId ${secondaryId}`);
      return result;

    } catch (error) {
      logger.error(`Error regenerating profile link for secondaryId ${secondaryId}:`, error);
      throw error;
    }
  }
}

// Main execution function
async function main() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is required');
    }

    logger.info('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB successfully');

    const regenerator = new ProfileLinkRegenerator();

    // Check command line arguments
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
      // Regenerate all profile links
      logger.info('No specific profile specified. Regenerating all profile links...');
      const result = await regenerator.regenerateAllProfileLinks();
      console.log('Regeneration completed:', result);
    } else if (args[0] === '--profile-id' && args[1]) {
      // Regenerate specific profile by ID
      const result = await regenerator.regenerateProfileLinkById(args[1]);
      console.log('Profile link regenerated:', result);
    } else if (args[0] === '--secondary-id' && args[1]) {
      // Regenerate specific profile by secondaryId
      const result = await regenerator.regenerateProfileLinkBySecondaryId(args[1]);
      console.log('Profile link regenerated:', result);
    } else {
      console.log('Usage:');
      console.log('  npm run regenerate-links                    # Regenerate all profile links');
      console.log('  npm run regenerate-links --profile-id <id>  # Regenerate specific profile by ID');
      console.log('  npm run regenerate-links --secondary-id <id> # Regenerate specific profile by secondaryId');
    }

  } catch (error) {
    logger.error('Error in main execution:', error);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

export { ProfileLinkRegenerator }; 