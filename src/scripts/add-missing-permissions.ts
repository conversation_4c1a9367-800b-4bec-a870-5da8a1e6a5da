#!/usr/bin/env ts-node

/**
 * Add Missing Permissions Script
 * 
 * This script adds all missing permissions to the appropriate roles
 * based on the permission analysis
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function addMissingPermissions() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Define permissions to add for each role
    const rolePermissions = {
      regular_user: [
        'account.export',           // For exporting accounts
        'user.create',              // For creating users
        'user.read.all',            // For reading all users
        'profile.publish.any',      // For publishing profiles
        'profile.unpublish.any',    // For unpublishing profiles
        'profile.publication.view.any' // For viewing published profiles
      ],
      admin_user: [
        'account.create',
        'account.read.own',
        'account.read.all',
        'account.update.own',
        'account.update.all',
        'account.deactivate.own',
        'account.deactivate.all',
        'account.delete.own',
        'account.delete.all',
        'account.subscription.manage.own',
        'account.subscription.manage.all',
        'account.activities.read.own',
        'account.activities.read.all',
        'account.activities.create.own',
        'account.activities.create.all',
        'account.manage',
        'account.export',
        'user.create'
      ],
      major_admin: [
        'account.create',
        'account.read.own',
        'account.read.all',
        'account.update.own',
        'account.update.all',
        'account.deactivate.own',
        'account.deactivate.all',
        'account.delete.own',
        'account.delete.all',
        'account.subscription.manage.own',
        'account.subscription.manage.all',
        'account.activities.read.own',
        'account.activities.read.all',
        'account.activities.create.own',
        'account.activities.create.all',
        'account.manage',
        'account.export',
        'user.create'
      ],
      super_admin: [
        'account.create',
        'account.read.own',
        'account.read.all',
        'account.update.own',
        'account.update.all',
        'account.deactivate.own',
        'account.deactivate.all',
        'account.delete.own',
        'account.delete.all',
        'account.subscription.manage.own',
        'account.subscription.manage.all',
        'account.activities.read.own',
        'account.activities.read.all',
        'account.activities.create.own',
        'account.activities.create.all',
        'account.manage',
        'account.export'
      ],
      supra_admin: [
        'account.create',
        'account.read.own',
        'account.read.all',
        'account.update.own',
        'account.update.all',
        'account.deactivate.own',
        'account.deactivate.all',
        'account.delete.own',
        'account.delete.all',
        'account.subscription.manage.own',
        'account.subscription.manage.all',
        'account.activities.read.own',
        'account.activities.read.all',
        'account.activities.create.own',
        'account.activities.create.all',
        'account.manage',
        'account.export'
      ],
      developer: [
        'account.create',
        'account.read.own',
        'account.read.all',
        'account.update.own',
        'account.update.all',
        'account.deactivate.own',
        'account.deactivate.all',
        'account.delete.own',
        'account.delete.all',
        'account.subscription.manage.own',
        'account.subscription.manage.all',
        'account.activities.read.own',
        'account.activities.read.all',
        'account.activities.create.own',
        'account.activities.create.all',
        'account.manage',
        'account.export',
        'profile.publish.any',
        'profile.unpublish.any',
        'profile.publication.view.any'
      ]
    };

    // Process each role
    for (const [roleName, permissionsToAdd] of Object.entries(rolePermissions)) {
      const role = await Role.findOne({ name: roleName });
      if (!role) {
        logger.warn(`⚠️  Role not found: ${roleName}`);
        continue;
      }

      logger.info(`🎭 Processing role: ${roleName}`);
      logger.info(`📋 Current permissions: ${role.permissions.length}`);

      // Find permissions that are not already assigned
      const newPermissions = permissionsToAdd.filter(
        permission => !role.permissions.includes(permission)
      );

      if (newPermissions.length > 0) {
        // Add new permissions
        role.permissions.push(...newPermissions);
        await role.save();
        
        logger.info(`✅ Added ${newPermissions.length} new permissions to ${roleName}:`);
        newPermissions.forEach(permission => {
          logger.info(`   + ${permission}`);
        });
      } else {
        logger.info(`ℹ️  All permissions already assigned to ${roleName}`);
      }

      logger.info(`📋 Total permissions after update: ${role.permissions.length}\n`);
    }

    logger.info('🎉 All missing permissions have been added successfully!');

  } catch (error) {
    logger.error('❌ Error adding missing permissions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
addMissingPermissions(); 