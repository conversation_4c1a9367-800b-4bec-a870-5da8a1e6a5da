#!/usr/bin/env ts-node

/**
 * Seed Plans Script
 * 
 * This script creates the three main plans: Free, Basic, and Premium
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import Plan from '../models/Plan';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

const plans = [
  {
    name: 'Free',
    planType: 'free',
    price: 0,
    billingCycle: 'monthly',
    isActive: true,
    
    // Profile Limits
    individualProfiles: 1,
    secondaryAccounts: 0,
    accessoryProfiles: 1,
    groupProfiles: 0,
    
    // Storage
    storageGB: 0.1,
    
    // Features
    insightsAccess: 'none',
    nfcProductLinking: 0,
    qrCodeAccess: 'basic',
    scannerAccess: 'limited',
    communityAccess: 'view',
    
    // MyPts & Rewards
    myPtsRate: 1.0,
    payoutPriority: 'none',
    payoutDays: 0,
    rewardSystemAccess: false,
    bonusTriggers: 'none',
    gamifiedActivities: false,
    
    // Circles & Leads
    circleCreation: 'none',
    leadsReferrals: 'none',
    
    // Team & Affiliate
    teamProxyRoles: false,
    affiliateProgram: 'none',
    addonMarketplace: false,
    
    // Support
    supportType: 'community',
    
    // Metadata
    description: 'Perfect for getting started with digital business cards',
    features: [
      'Basic QR code generation',
      'Limited scanner access',
      'Community support',
      '1 individual profile',
      '0.1GB storage'
    ],
    benefits: [
      'Free forever',
      'No credit card required',
      'Basic digital business card',
      'Community access'
    ]
  },
  {
    name: 'Basic',
    planType: 'basic',
    price: 9.99,
    billingCycle: 'monthly',
    isActive: true,
    
    // Profile Limits
    individualProfiles: 3,
    secondaryAccounts: 1,
    accessoryProfiles: 5,
    groupProfiles: 1,
    
    // Storage
    storageGB: 1,
    
    // Features
    insightsAccess: 'basic',
    nfcProductLinking: 10,
    qrCodeAccess: 'branded',
    scannerAccess: 'standard',
    communityAccess: 'join',
    
    // MyPts & Rewards
    myPtsRate: 1.2,
    payoutPriority: 'standard',
    payoutDays: 30,
    rewardSystemAccess: true,
    bonusTriggers: 'manual',
    gamifiedActivities: true,
    
    // Circles & Leads
    circleCreation: 'join-only',
    leadsReferrals: 'basic',
    
    // Team & Affiliate
    teamProxyRoles: false,
    affiliateProgram: 'invite-only',
    addonMarketplace: false,
    
    // Support
    supportType: 'email',
    
    // Metadata
    description: 'Ideal for professionals and small businesses',
    features: [
      '3 individual profiles',
      '1 secondary account',
      '5 accessory profiles',
      '1 group profile',
      '1GB storage',
      'Basic insights',
      'Branded QR codes',
      'Standard scanner',
      'Community participation',
      'MyPts rewards system',
      'Basic lead tracking',
      'Email support'
    ],
    benefits: [
      'Professional digital presence',
      'Enhanced MyPts earning',
      'Basic analytics',
      'Community engagement',
      'Lead generation tools'
    ]
  },
  {
    name: 'Premium',
    planType: 'premium',
    price: 29.99,
    billingCycle: 'monthly',
    isActive: true,
    
    // Profile Limits
    individualProfiles: 10,
    secondaryAccounts: 5,
    accessoryProfiles: 20,
    groupProfiles: 5,
    
    // Storage
    storageGB: 10,
    
    // Features
    insightsAccess: 'advanced',
    nfcProductLinking: 100,
    qrCodeAccess: 'animated',
    scannerAccess: 'smart',
    communityAccess: 'join-admin',
    
    // MyPts & Rewards
    myPtsRate: 1.5,
    payoutPriority: 'priority',
    payoutDays: 15,
    rewardSystemAccess: true,
    bonusTriggers: 'automated',
    gamifiedActivities: true,
    
    // Circles & Leads
    circleCreation: 'create-3',
    leadsReferrals: 'dashboard',
    
    // Team & Affiliate
    teamProxyRoles: true,
    affiliateProgram: 'tiered',
    addonMarketplace: true,
    
    // Support
    supportType: 'priority-email',
    
    // Metadata
    description: 'Complete solution for businesses and power users',
    features: [
      '10 individual profiles',
      '5 secondary accounts',
      '20 accessory profiles',
      '5 group profiles',
      '10GB storage',
      'Advanced insights',
      'Animated QR codes',
      'Smart scanner',
      'Community admin access',
      'Enhanced MyPts rewards',
      'Automated bonus triggers',
      'Lead dashboard',
      'Circle creation',
      'Team proxy roles',
      'Tiered affiliate program',
      'Addon marketplace',
      'Priority email support'
    ],
    benefits: [
      'Complete business solution',
      'Maximum MyPts earning',
      'Advanced analytics',
      'Community leadership',
      'Team management',
      'Revenue generation tools',
      'Priority support'
    ]
  }
];

async function seedPlans() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Clear existing plans
    logger.info('🗑️ Clearing existing plans...');
    await Plan.deleteMany({});
    logger.info('✅ Cleared existing plans');

    // Create new plans
    logger.info('🌱 Creating plans...');
    const createdPlans = await Plan.insertMany(plans);
    
    logger.info(`✅ Successfully created ${createdPlans.length} plans:`);
    
    for (const plan of createdPlans) {
      logger.info(`  📋 ${plan.name} - $${plan.price}/${plan.billingCycle}`);
      logger.info(`     Profiles: ${plan.individualProfiles} individual, ${plan.secondaryAccounts} secondary`);
      logger.info(`     Storage: ${plan.storageGB}GB, Features: ${plan.features.length} features`);
    }

    logger.info('\n🎉 Plan seeding completed successfully!');
    
    // Display plan comparison
    logger.info('\n📊 PLAN COMPARISON:');
    logger.info('=' .repeat(80));
    logger.info('Plan Name    | Price  | Profiles | Storage | MyPts Rate | Support');
    logger.info('-' .repeat(80));
    
    for (const plan of createdPlans) {
      logger.info(`${plan.name.padEnd(11)} | $${plan.price.toString().padStart(5)} | ${plan.individualProfiles.toString().padStart(8)} | ${plan.storageGB.toString().padStart(6)}GB | ${plan.myPtsRate.toString().padStart(10)}x | ${plan.supportType}`);
    }

  } catch (error) {
    logger.error('❌ Error seeding plans:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
seedPlans(); 