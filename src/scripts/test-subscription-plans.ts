#!/usr/bin/env ts-node

/**
 * Test Subscription Plans Script
 * 
 * This script tests the subscription plans API endpoints directly
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import Plan from '../models/Plan';
import { logger } from '../utils/logger';
import { getAllSubscriptionPlans, getSubscriptionPlanByName, getSubscriptionPlanComparison } from '../controllers/subscription-plans.controller';

// Load environment variables first
dotenv.config();

// Mock request and response objects for testing
const createMockRequest = (params: any = {}, query: any = {}) => ({
    params,
    query,
    headers: {}
});

const createMockResponse = () => {
    const res: any = {};
    res.status = (code: number) => {
        res.statusCode = code;
        return res;
    };
    res.json = (data: any) => {
        res.data = data;
        return res;
    };
    return res;
};

const createMockNext = () => {
    return () => {}; // No-op function
};

async function testSubscriptionPlansAPI() {
    try {
        // Connect to database using config
        logger.info('Connecting to database...');
        await mongoose.connect(config.MONGODB_URI);
        logger.info('✅ Connected to database successfully');

        // Test 1: Get all subscription plans
        logger.info('\n🧪 TEST 1: Get all subscription plans');
        const req1 = createMockRequest();
        const res1 = createMockResponse();
        
        await getAllSubscriptionPlans(req1 as any, res1 as any, createMockNext());
        
        if (res1.statusCode === 200) {
            logger.info('✅ Get all plans test passed');
            logger.info(`📊 Found ${res1.data.count} plans`);
            res1.data.data.forEach((plan: any) => {
                logger.info(`  - ${plan.name}: $${plan.price}/${plan.billingCycle}`);
            });
        } else {
            logger.error('❌ Get all plans test failed');
        }

        // Test 2: Get plan by name
        logger.info('\n🧪 TEST 2: Get plan by name');
        const req2 = createMockRequest({ name: 'Free' });
        const res2 = createMockResponse();
        
        await getSubscriptionPlanByName(req2 as any, res2 as any, createMockNext());
        
        if (res2.statusCode === 200) {
            logger.info('✅ Get plan by name test passed');
            logger.info(`📋 Retrieved plan: ${res2.data.data.name}`);
        } else {
            logger.error('❌ Get plan by name test failed');
        }

        // Test 3: Get plan comparison
        logger.info('\n🧪 TEST 3: Get plan comparison');
        const req3 = createMockRequest();
        const res3 = createMockResponse();
        
        await getSubscriptionPlanComparison(req3 as any, res3 as any, createMockNext());
        
        if (res3.statusCode === 200) {
            logger.info('✅ Get plan comparison test passed');
            logger.info(`📊 Comparison summary:`);
            logger.info(`  - Total plans: ${res3.data.data.summary.totalPlans}`);
            logger.info(`  - Price range: $${res3.data.data.summary.priceRange.min} - $${res3.data.data.summary.priceRange.max}`);
            logger.info(`  - Has free plan: ${res3.data.data.summary.features.hasFreePlan}`);
            logger.info(`  - Has premium features: ${res3.data.data.summary.features.hasPremiumFeatures}`);
        } else {
            logger.error('❌ Get plan comparison test failed');
        }

        // Test 4: Direct database query
        logger.info('\n🧪 TEST 4: Direct database query');
        const plans = await Plan.find({ isActive: true }).sort({ price: 1 });
        logger.info(`📊 Direct query found ${plans.length} active plans:`);
        
        plans.forEach(plan => {
            logger.info(`  📋 ${plan.name}:`);
            logger.info(`     Price: $${plan.price}/${plan.billingCycle}`);
            logger.info(`     Profiles: ${plan.individualProfiles} individual, ${plan.secondaryAccounts} secondary`);
            logger.info(`     Storage: ${plan.storageGB}GB`);
            logger.info(`     MyPts Rate: ${plan.myPtsRate}x`);
            logger.info(`     Features: ${plan.features.length} features`);
        });

        logger.info('\n🎉 All subscription plans API tests completed successfully!');

    } catch (error) {
        logger.error('❌ Error testing subscription plans API:', error);
    } finally {
        await mongoose.disconnect();
        logger.info('🔌 Disconnected from database');
    }
}

// Run the test
testSubscriptionPlansAPI(); 