#!/usr/bin/env ts-node

/**
 * Test Account Endpoints Script
 * 
 * This script tests the account endpoints to verify they work with secondary account IDs
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { AccountService } from '../services/account.service';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function testAccountEndpoints() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Find a user with secondary accounts
    const primaryUser = await User.findOne({ 
      accountCategory: 'PRIMARY_ACCOUNT',
      referredBy: { $exists: false }
    }).populate('referredBy');

    if (!primaryUser) {
      logger.error('❌ No primary user found');
      return;
    }

    logger.info(`👤 Primary User: ${primaryUser.email} (${primaryUser._id})`);
    logger.info(`🆔 Secondary ID: ${primaryUser.secondaryId || 'Not set'}`);

    // Find secondary accounts created by this user
    const secondaryAccounts = await User.find({ 
      referredBy: primaryUser._id,
      accountCategory: 'SECONDARY_ACCOUNT'
    });

    logger.info(`📊 Found ${secondaryAccounts.length} secondary accounts`);

    if (secondaryAccounts.length === 0) {
      logger.info('ℹ️  No secondary accounts found. Creating one for testing...');
      
      // Create a test secondary account
      const testSecondaryAccount = await AccountService.createAccount({
        fullName: 'Test Secondary User',
        gender: 'Male',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        address: 'Test Address',
        country: 'Test Country',
        subscription: 'Free',
        dataSize: '1GB',
        accountType: 'secondary'
      }, primaryUser._id.toString());

      logger.info(`✅ Created test secondary account: ${testSecondaryAccount.account.userId}`);
      
      // Test the endpoints with the secondary account ID
      await testAccountEndpointsWithId(testSecondaryAccount.account.userId, primaryUser._id.toString());
    } else {
      // Test with existing secondary accounts
      for (const secondaryAccount of secondaryAccounts) {
        logger.info(`🧪 Testing with secondary account: ${secondaryAccount.secondaryId || secondaryAccount._id}`);
        await testAccountEndpointsWithId(secondaryAccount.secondaryId || secondaryAccount._id.toString(), primaryUser._id.toString());
      }
    }

  } catch (error) {
    logger.error('❌ Error testing account endpoints:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

async function testAccountEndpointsWithId(accountId: string, primaryUserId: string) {
  try {
    logger.info(`\n🧪 Testing account endpoints with ID: ${accountId}`);
    
    // Test 1: Get account by ID
    logger.info('📖 Testing GET /api/accounts/:id');
    const account = await AccountService.getAccountById(accountId);
    const fullName = `${account.account.firstName} ${account.account.lastName}`.trim();
    logger.info(`✅ Account found: ${fullName} (${account.account.userId})`);
    logger.info(`   Account Type: ${account.account.accountType}`);
    logger.info(`   Username: ${account.account.username}`);
    
    // Test 2: Update account
    logger.info('✏️  Testing PUT /api/accounts/:id');
    const updateData = {
      fullName: `${fullName} (Updated)`,
      phoneNumber: '+**********'
    };
    
    const updatedAccount = await AccountService.updateAccount(accountId, updateData);
    const updatedFullName = `${updatedAccount.account.firstName} ${updatedAccount.account.lastName}`.trim();
    logger.info(`✅ Account updated successfully`);
    logger.info(`   New name: ${updatedFullName}`);
    logger.info(`   New userId: ${updatedAccount.account.userId}`);
    
    // Test 3: Verify the account can be found by both primary and secondary IDs
    logger.info('🔍 Testing account lookup by different ID types');
    
    // Try to find by MongoDB ObjectId
    const accountByObjectId = await AccountService.getAccountById(accountId);
    const objectIdFullName = `${accountByObjectId.account.firstName} ${accountByObjectId.account.lastName}`.trim();
    logger.info(`✅ Found by ObjectId: ${objectIdFullName}`);
    
    // Try to find by secondaryId if it exists
    if (account.account.userId !== accountId) {
      const accountBySecondaryId = await AccountService.getAccountById(account.account.userId);
      const secondaryIdFullName = `${accountBySecondaryId.account.firstName} ${accountBySecondaryId.account.lastName}`.trim();
      logger.info(`✅ Found by secondaryId: ${secondaryIdFullName}`);
    }
    
    logger.info('🎉 All tests passed!');
    
  } catch (error) {
    logger.error(`❌ Error testing account ID ${accountId}:`, error);
  }
}

// Run the script
testAccountEndpoints(); 