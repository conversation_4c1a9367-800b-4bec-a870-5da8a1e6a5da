#!/usr/bin/env ts-node

/**
 * View Plans Script
 * 
 * This script displays all created plans with their details
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import Plan from '../models/Plan';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function viewPlans() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Get all plans
    const plans = await Plan.find({}).sort({ price: 1 });
    logger.info(`📊 Found ${plans.length} plans in database`);

    if (plans.length === 0) {
      logger.info('❌ No plans found in database');
      return;
    }

    // Display each plan in detail
    for (const plan of plans) {
      logger.info('\n' + '='.repeat(80));
      logger.info(`📋 PLAN: ${plan.name.toUpperCase()}`);
      logger.info('='.repeat(80));
      
      // Basic info
      logger.info(`💰 Price: $${plan.price}/${plan.billingCycle}`);
      logger.info(`📊 Plan Type: ${plan.planType}`);
      logger.info(`✅ Active: ${plan.isActive}`);
      
      // Profile limits
      logger.info('\n👥 PROFILE LIMITS:');
      logger.info(`  Individual Profiles: ${plan.individualProfiles}`);
      logger.info(`  Secondary Accounts: ${plan.secondaryAccounts}`);
      logger.info(`  Accessory Profiles: ${plan.accessoryProfiles}`);
      logger.info(`  Group Profiles: ${plan.groupProfiles}`);
      logger.info(`  Storage: ${plan.storageGB}GB`);
      
      // Features
      logger.info('\n🔧 FEATURES:');
      logger.info(`  Insights Access: ${plan.insightsAccess}`);
      logger.info(`  NFC Product Linking: ${plan.nfcProductLinking} links`);
      logger.info(`  QR Code Access: ${plan.qrCodeAccess}`);
      logger.info(`  Scanner Access: ${plan.scannerAccess}`);
      logger.info(`  Community Access: ${plan.communityAccess}`);
      
      // MyPts & Rewards
      logger.info('\n🎁 MYPTS & REWARDS:');
      logger.info(`  MyPts Rate: ${plan.myPtsRate}x`);
      logger.info(`  Payout Priority: ${plan.payoutPriority}`);
      logger.info(`  Payout Days: ${plan.payoutDays} days`);
      logger.info(`  Reward System Access: ${plan.rewardSystemAccess}`);
      logger.info(`  Bonus Triggers: ${plan.bonusTriggers}`);
      logger.info(`  Gamified Activities: ${plan.gamifiedActivities}`);
      
      // Circles & Leads
      logger.info('\n🔄 CIRCLES & LEADS:');
      logger.info(`  Circle Creation: ${plan.circleCreation}`);
      logger.info(`  Leads Referrals: ${plan.leadsReferrals}`);
      
      // Team & Affiliate
      logger.info('\n👥 TEAM & AFFILIATE:');
      logger.info(`  Team Proxy Roles: ${plan.teamProxyRoles}`);
      logger.info(`  Affiliate Program: ${plan.affiliateProgram}`);
      logger.info(`  Addon Marketplace: ${plan.addonMarketplace}`);
      
      // Support
      logger.info('\n📞 SUPPORT:');
      logger.info(`  Support Type: ${plan.supportType}`);
      
      // Metadata
      logger.info('\n📝 METADATA:');
      logger.info(`  Description: ${plan.description}`);
      logger.info(`  Features Count: ${plan.features.length}`);
      logger.info(`  Benefits Count: ${plan.benefits.length}`);
      
      // Features list
      if (plan.features.length > 0) {
        logger.info('\n  📋 FEATURES:');
        plan.features.forEach((feature, index) => {
          logger.info(`    ${index + 1}. ${feature}`);
        });
      }
      
      // Benefits list
      if (plan.benefits.length > 0) {
        logger.info('\n  🎯 BENEFITS:');
        plan.benefits.forEach((benefit, index) => {
          logger.info(`    ${index + 1}. ${benefit}`);
        });
      }
      
      logger.info('\n' + '-'.repeat(80));
    }

    // Summary table
    logger.info('\n📊 SUMMARY TABLE:');
    logger.info('='.repeat(100));
    logger.info('Plan Name    | Price  | Profiles | Storage | MyPts | Features | Support');
    logger.info('-' .repeat(100));
    
    for (const plan of plans) {
      logger.info(
        `${plan.name.padEnd(11)} | $${plan.price.toString().padStart(5)} | ${plan.individualProfiles.toString().padStart(8)} | ${plan.storageGB.toString().padStart(6)}GB | ${plan.myPtsRate.toString().padStart(4)}x | ${plan.features.length.toString().padStart(8)} | ${plan.supportType}`
      );
    }

  } catch (error) {
    logger.error('❌ Error viewing plans:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
viewPlans(); 