#!/usr/bin/env ts-node

/**
 * Add Account Create Permission Script
 * 
 * This script adds the account.create permission to the regular_user role
 * so users can create secondary accounts
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function addCreatePermission() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Find the regular_user role
    const regularUserRole = await Role.findOne({ name: 'regular_user' });
    if (!regularUserRole) {
      logger.error('❌ regular_user role not found');
      return;
    }

    logger.info(`📋 Current regular_user role permissions: ${regularUserRole.permissions.length}`);
    logger.info(`📝 Permissions: ${regularUserRole.permissions.join(', ')}`);

    // Check if account.create permission already exists
    if (regularUserRole.permissions.includes('account.create')) {
      logger.info('✅ account.create permission already exists for regular_user role');
      return;
    }

    // Add account.create permission
    regularUserRole.permissions.push('account.create');
    await regularUserRole.save();

    logger.info('✅ Successfully added account.create permission to regular_user role');
    logger.info(`📋 Updated permissions: ${regularUserRole.permissions.join(', ')}`);

  } catch (error) {
    logger.error('❌ Error adding create permission:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
addCreatePermission(); 