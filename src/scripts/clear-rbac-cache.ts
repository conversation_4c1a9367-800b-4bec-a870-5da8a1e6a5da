#!/usr/bin/env ts-node

/**
 * Clear RBAC Cache Script
 * 
 * This script clears the RBAC cache and then checks permissions
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function clearRBACCache() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Clear RBAC cache
    logger.info('🧹 Clearing RBAC cache...');
    
    // Clear the static caches in RBACService
    (RBACService as any).permissionCache.clear();
    (RBACService as any).roleCache.clear();
    (RBACService as any).userRoleCache.clear();
    
    logger.info('✅ RBAC cache cleared');

    // Find your user
    const user = await User.findOne({ email: '<EMAIL>' }).select('_id email role fullName');
    if (!user) {
      logger.error('❌ User not found');
      return;
    }

    logger.info(`👤 User: ${user.email} (${user.role})`);

    // Check permissions after cache clear
    logger.info('🔍 Checking permissions after cache clear...');
    
    const userPermissions = await RBACService.getUserPermissions(user._id);
    logger.info(`📊 Total permissions: ${userPermissions.length}`);
    
    const accountPerms = userPermissions.filter(p => p.startsWith('account.'));
    logger.info(`💼 Account permissions: ${accountPerms.length}`);
    
    if (accountPerms.length > 0) {
      accountPerms.forEach(perm => logger.info(`  ✅ ${perm}`));
    } else {
      logger.warn('⚠️  No account permissions found!');
    }

    // Test specific account permissions
    const accountPermissions = [
      'account.read.own',
      'account.read.all',
      'account.update.own',
      'account.update.all',
      'account.create',
      'account.manage'
    ];

    logger.info('\n🧪 Testing specific account permissions:');
    for (const permission of accountPermissions) {
      const hasPermission = await RBACService.hasPermission(user._id, permission, {
        ipAddress: '127.0.0.1'
      });
      const status = hasPermission ? '✅' : '❌';
      logger.info(`  ${status} ${permission}`);
    }

    // Check user roles
    const userRoles = await RBACService.getUserActiveRoles(user._id);
    logger.info(`\n🎭 Active roles: ${userRoles.length}`);
    userRoles.forEach(role => {
      logger.info(`  - ${role.roleType} (Active: ${role.isActive})`);
    });

  } catch (error) {
    logger.error('❌ Error clearing RBAC cache:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

async function main() {
  try {
    await clearRBACCache();
  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
} 