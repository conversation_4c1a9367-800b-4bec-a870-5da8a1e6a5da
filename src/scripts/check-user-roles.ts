// #!/usr/bin/env ts-node

// /**
//  * Check User Roles Script
//  * 
//  * This script checks all users and identifies any roles that are not in the RoleType enum
//  */

// import dotenv from 'dotenv';
// import mongoose from 'mongoose';
// import { config } from '../config/config';
// import { User } from '../models/User';
// import { RoleType } from '../models/Role';
// import { logger } from '../utils/logger';

// // Load environment variables first
// dotenv.config();

// async function checkUserRoles() {
//   try {
//     // Connect to database using config
//     logger.info('Connecting to database...');
//     await mongoose.connect(config.MONGODB_URI);
//     logger.info('✅ Connected to database successfully');

//     // Get all users
//     const users = await User.find({}).select('_id email username role createdAt');
//     logger.info(`📊 Found ${users.length} total users`);

//     // Valid role types
//     const validRoles = Object.values(RoleType);
//     logger.info(`📋 Valid roles: ${validRoles.join(', ')}`);

//     // Analyze user roles
//     const roleAnalysis = {
//       totalUsers: users.length,
//       usersWithRoles: 0,
//       usersWithoutRoles: 0,
//       validRoles: {} as Record<string, number>,
//       invalidRoles: {} as Record<string, number>,
//       usersWithInvalidRoles: [] as Array<{
//         _id: string;
//         email: string;
//         username: string;
//         role: string;
//         createdAt: Date;
//       }>
//     };

//     for (const user of users) {
//       if (user.role) {
//         roleAnalysis.usersWithRoles++;
        
//         if (validRoles.includes(user.role)) {
//           // Valid role
//           roleAnalysis.validRoles[user.role] = (roleAnalysis.validRoles[user.role] || 0) + 1;
//         } else {
//           // Invalid role
//           roleAnalysis.invalidRoles[user.role] = (roleAnalysis.invalidRoles[user.role] || 0) + 1;
//           roleAnalysis.usersWithInvalidRoles.push({
//             _id: user._id.toString(),
//             email: user.email,
//             username: user.username,
//             role: user.role,
//             createdAt: user.createdAt
//           });
//         }
//       } else {
//         roleAnalysis.usersWithoutRoles++;
//       }
//     }


//     // Display results
//     logger.info('\n📊 ROLE ANALYSIS RESULTS:');
//     logger.info('=' .repeat(50));
//     logger.info(`Total users: ${roleAnalysis.totalUsers}`);
//     logger.info(`Users with roles: ${roleAnalysis.usersWithRoles}`);
//     logger.info(`Users without roles: ${roleAnalysis.usersWithoutRoles}`);

//     logger.info('\n✅ VALID ROLES:');
//     for (const [role, count] of Object.entries(roleAnalysis.validRoles)) {
//       logger.info(`  ${role}: ${count} users`);
//     }

//     if (Object.keys(roleAnalysis.invalidRoles).length > 0) {
//       logger.info('\n❌ INVALID ROLES FOUND:');
//       for (const [role, count] of Object.entries(roleAnalysis.invalidRoles)) {
//         logger.info(`  "${role}": ${count} users`);
//       }

//       logger.info('\n👥 USERS WITH INVALID ROLES:');
//       for (const user of roleAnalysis.usersWithInvalidRoles) {
//         logger.info(`  - ${user.email} (${user.username}): "${user.role}" - Created: ${user.createdAt.toISOString().split('T')[0]}`);
//       }
//     } else {
//       logger.info('\n✅ No invalid roles found!');
//     }

//     // Summary
//     logger.info('\n📋 SUMMARY:');
//     if (Object.keys(roleAnalysis.invalidRoles).length > 0) {
//       logger.info(`❌ Found ${Object.keys(roleAnalysis.invalidRoles).length} invalid role types affecting ${roleAnalysis.usersWithInvalidRoles.length} users`);
//       logger.info('💡 These users need to have their roles fixed to valid RoleType values');
//     } else {
//       logger.info('✅ All users have valid roles!');
//     }

//   } catch (error) {
//     logger.error('❌ Error checking user roles:', error);
//   } finally {
//     await mongoose.disconnect();
//     logger.info('🔌 Disconnected from database');
//   }
// }

// // Run the script
// checkUserRoles(); 