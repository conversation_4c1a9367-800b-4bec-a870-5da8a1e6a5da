#!/usr/bin/env ts-node

/**
 * Assign Free Subscription to All Users Script
 * 
 * This script assigns the free subscription plan to all users who don't already have an active subscription
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import UserPlan from '../models/UserPlan';
import { subscriptionService } from '../services/subscription.service';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function assignFreeSubscriptionToAllUsers() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Use the free plan ID from PLAN_TEMPLATES
    const freePlanId = '688b0d48fed319825c93120d'; // Free plan ID from PLAN_TEMPLATES
    logger.info(`📋 Using free plan ID: ${freePlanId}`);

    // Get all users
    const allUsers = await User.find({}).select('_id email username');
    logger.info(`📊 Found ${allUsers.length} total users`);

    // Get users who already have active subscriptions
    const usersWithActiveSubscriptions = await UserPlan.find({ isActive: true })
      .distinct('userId');
    
    const usersWithActiveSubscriptionsSet = new Set(
      usersWithActiveSubscriptions.map((id: any) => id.toString())
    );

    // Filter users who don't have active subscriptions
    const usersWithoutSubscriptions = allUsers.filter(
      user => !usersWithActiveSubscriptionsSet.has(user._id.toString())
    );

    logger.info(`📊 Users with active subscriptions: ${usersWithActiveSubscriptions.length}`);
    logger.info(`📊 Users without subscriptions: ${usersWithoutSubscriptions.length}`);

    if (usersWithoutSubscriptions.length === 0) {
      logger.info('✅ All users already have active subscriptions!');
      return;
    }

    // Assign free subscription to users without subscriptions
    let successCount = 0;
    let errorCount = 0;
    const errors: Array<{ userId: string; email: string; error: string }> = [];

    logger.info(`🚀 Starting to assign free subscriptions to ${usersWithoutSubscriptions.length} users...`);

    for (const user of usersWithoutSubscriptions) {
      try {
        await subscriptionService.assignPlanToUser(
          user._id.toString(),
          freePlanId,
          'monthly',
          'test' // Use test payment provider for free plans
        );
        
        successCount++;
        logger.info(`✅ Assigned free subscription to ${user.email} (${user.username})`);
      } catch (error) {
        errorCount++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({
          userId: user._id.toString(),
          email: user.email,
          error: errorMessage
        });
        logger.error(`❌ Failed to assign free subscription to ${user.email}: ${errorMessage}`);
      }
    }

    // Display results
    logger.info('\n📊 ASSIGNMENT RESULTS:');
    logger.info('=' .repeat(50));
    logger.info(`Total users processed: ${usersWithoutSubscriptions.length}`);
    logger.info(`✅ Successful assignments: ${successCount}`);
    logger.info(`❌ Failed assignments: ${errorCount}`);

    if (errors.length > 0) {
      logger.info('\n❌ FAILED ASSIGNMENTS:');
      for (const error of errors) {
        logger.info(`  - ${error.email}: ${error.error}`);
      }
    }

    // Summary
    logger.info('\n📋 SUMMARY:');
    if (errorCount === 0) {
      logger.info('✅ Successfully assigned free subscriptions to all eligible users!');
    } else {
      logger.info(`⚠️  Completed with ${errorCount} errors. Check the logs above for details.`);
    }

  } catch (error) {
    logger.error('❌ Error assigning free subscriptions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
assignFreeSubscriptionToAllUsers(); 