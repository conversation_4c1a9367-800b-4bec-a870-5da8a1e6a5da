#!/usr/bin/env ts-node

/**
 * Check Current User Permissions Script
 * 
 * This script checks the permissions for a specific user by email
 */

import mongoose from 'mongoose';
import { config } from '../config/config';
import { User } from '../models/User';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import { RBACService } from '../services/rbac.service';
import { logger } from '../utils/logger';

async function checkUserPermissions(email: string) {
  try {
    // Connect to database
    await mongoose.connect(config.MONGODB_URI);
    logger.info('Connected to database');

    // Find the user
    const user = await User.findOne({ email }).select('_id email role fullName');
    if (!user) {
      logger.error(`User with email ${email} not found`);
      return;
    }

    logger.info('\n=== USER INFO ===');
    logger.info(`ID: ${user._id}`);
    logger.info(`Email: ${user.email}`);
    logger.info(`Legacy Role: ${user.role}`);
    logger.info(`Full Name: ${user.fullName}`);

    // Check direct UserRole records
    const userRoles = await UserRole.find({ 
      userId: user._id,
      isActive: true
    }).sort({ assignedAt: -1 });

    logger.info('\n=== RBAC USER ROLES ===');
    if (userRoles.length === 0) {
      logger.warn('No active UserRole records found');
    } else {
      userRoles.forEach((userRole, index) => {
        logger.info(`\nRole ${index + 1}:`);
        logger.info(`  Role Type: ${userRole.roleType}`);
        logger.info(`  Is Active: ${userRole.isActive}`);
        logger.info(`  Assigned At: ${userRole.assignedAt}`);
        logger.info(`  Expires At: ${userRole.expiresAt || 'Never'}`);
        logger.info(`  Assigned By: ${userRole.assignedBy}`);
      });
    }

    // Check active roles using RBAC service
    const activeRoles = await RBACService.getUserActiveRoles(user._id);
    logger.info('\n=== ACTIVE RBAC ROLES (via RBACService) ===');
    if (activeRoles.length === 0) {
      logger.warn('No active RBAC roles found via RBACService');
    } else {
      activeRoles.forEach((userRole, index) => {
        logger.info(`\nActive Role ${index + 1}:`);
        logger.info(`  Role Type: ${userRole.roleType}`);
        logger.info(`  Is Active: ${userRole.isActive}`);
        logger.info(`  Valid for Current Time: ${(userRole as any).isValidForCurrentTime()}`);
      });
    }

    // Check permissions
    const permissions = await RBACService.getUserPermissions(user._id);
    logger.info('\n=== USER PERMISSIONS ===');
    if (permissions.length === 0) {
      logger.warn('No permissions found');
    } else {
      logger.info(`Found ${permissions.length} permissions:`);
      permissions.slice(0, 20).forEach(perm => logger.info(`  - ${perm}`));
      if (permissions.length > 20) {
        logger.info(`  ... and ${permissions.length - 20} more`);
      }
    }

    // Check specific account permissions
    const accountPermissions = [
      'account.read.own',
      'account.read.all',
      'account.update.own',
      'account.update.all',
      'account.create',
      'account.manage'
    ];

    logger.info('\n=== ACCOUNT PERMISSIONS CHECK ===');
    for (const permission of accountPermissions) {
      const hasPermission = await RBACService.hasPermission(user._id, permission, {
        ipAddress: '127.0.0.1'
      });
      const status = hasPermission ? '✅' : '❌';
      logger.info(`  ${status} ${permission}`);
    }

    // Check highest role level
    const highestLevel = await RBACService.getUserHighestRoleLevel(user._id);
    logger.info(`\n=== HIGHEST ROLE LEVEL ===`);
    logger.info(`Level: ${highestLevel}`);

    // Check if user should have admin access
    const isLegacyAdmin = user.role === 'admin_user' || user.role === 'super_admin';
    logger.info(`\n=== LEGACY ADMIN STATUS ===`);
    logger.info(`Is Legacy Admin: ${isLegacyAdmin ? '✅ Yes' : '❌ No'}`);

    // Check role details
    logger.info('\n=== ROLE DETAILS ===');
    for (const userRole of userRoles) {
      const role = await Role.findOne({ name: userRole.roleType });
      if (role) {
        logger.info(`\nRole: ${role.name} (Level: ${role.level})`);
        logger.info(`Display Name: ${role.displayName}`);
        logger.info(`Description: ${role.description}`);
        logger.info(`Is Active: ${role.isActive}`);
        logger.info(`Permissions (${role.permissions.length}):`);
        role.permissions.slice(0, 10).forEach(perm => logger.info(`  - ${perm}`));
        if (role.permissions.length > 10) {
          logger.info(`  ... and ${role.permissions.length - 10} more`);
        }
      }
    }

  } catch (error) {
    logger.error('Error checking user permissions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from database');
  }
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const email = args[0];

    if (!email) {
      console.error('Usage: ts-node src/scripts/check-current-user-permissions.ts <email>');
      console.error('Example: ts-node src/scripts/check-current-user-permissions.ts <EMAIL>');
      process.exit(1);
    }

    await checkUserPermissions(email);

  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
} 