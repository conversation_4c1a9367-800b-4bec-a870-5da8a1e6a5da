#!/usr/bin/env ts-node

/**
 * Add Account Export Permission Script
 * 
 * This script adds the account.export permission to the regular_user role
 * so users can export their accounts
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function addExportPermission() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Find the regular_user role
    const regularUserRole = await Role.findOne({ name: 'regular_user' });
    if (!regularUserRole) {
      logger.error('❌ regular_user role not found');
      return;
    }

    logger.info(`📋 Current regular_user role permissions: ${regularUserRole.permissions.length}`);
    logger.info(`📝 Permissions: ${regularUserRole.permissions.join(', ')}`);

    // Check if account.export permission already exists
    if (regularUserRole.permissions.includes('account.export')) {
      logger.info('✅ account.export permission already exists for regular_user role');
      return;
    }

    // Add account.export permission
    regularUserRole.permissions.push('account.export');
    await regularUserRole.save();

    logger.info('✅ Successfully added account.export permission to regular_user role');
    logger.info(`📋 Updated permissions: ${regularUserRole.permissions.join(', ')}`);

  } catch (error) {
    logger.error('❌ Error adding export permission:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

// Run the script
 