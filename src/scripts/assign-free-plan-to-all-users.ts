import mongoose from 'mongoose';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { User } from '../models/User';
import Plan, { IPlan } from '../models/Plan';
import UserPlan from '../models/UserPlan';
import { subscriptionService } from '../services/subscription.service';

async function assignFreePlanToAllUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Use the Free plan ID provided by the user
    const freePlanId = '688b300120fdcb1fabb5e585';
    
    // Verify the plan exists
    const freePlan = await Plan.findById(freePlanId) as IPlan | null;
    if (!freePlan) {
      logger.error('Free plan not found in database');
      return;
    }

    logger.info(`Found Free plan: ${freePlan._id} - ${freePlan.name}`);

    // Get all users
    const users = await User.find({});
    logger.info(`Found ${users.length} users in database`);

    let assignedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const user of users) {
      try {
        // Check if user already has an active plan
        const existingActivePlan = await UserPlan.findOne({
          userId: user._id,
          isActive: true
        });

        if (existingActivePlan) {
          logger.info(`User ${user.email} (${user._id}) already has active plan: ${existingActivePlan.planName}`);
          skippedCount++;
          continue;
        }

        // Assign Free plan to user
        await subscriptionService.assignPlanToUser(
          user._id.toString(),
          freePlanId,
          'monthly',
          'test'
        );

        logger.info(`Successfully assigned Free plan to user: ${user.email} (${user._id})`);
        assignedCount++;

      } catch (error) {
        logger.error(`Error assigning plan to user ${user.email} (${user._id}):`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info('=== Assignment Summary ===');
    logger.info(`Total users processed: ${users.length}`);
    logger.info(`Plans assigned: ${assignedCount}`);
    logger.info(`Users skipped (already have active plan): ${skippedCount}`);
    logger.info(`Errors: ${errorCount}`);

    // Verify results
    const totalActivePlans = await UserPlan.countDocuments({ isActive: true });
    logger.info(`Total active plans in database: ${totalActivePlans}`);

  } catch (error) {
    logger.error('Error in assignFreePlanToAllUsers:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
assignFreePlanToAllUsers()
  .then(() => {
    logger.info('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Script failed:', error);
    process.exit(1);
  }); 