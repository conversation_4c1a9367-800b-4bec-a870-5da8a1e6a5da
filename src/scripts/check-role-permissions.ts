#!/usr/bin/env ts-node

/**
 * Check Role Permissions Script
 * 
 * This script directly checks what permissions are assigned to roles in the database
 */

import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { config } from '../config/config';
import { Role } from '../models/Role';
import { Permission } from '../models/Permission';
import { logger } from '../utils/logger';

// Load environment variables first
dotenv.config();

async function checkRolePermissions() {
  try {
    // Connect to database using config
    logger.info('Connecting to database...');
    await mongoose.connect(config.MONGODB_URI);
    logger.info('✅ Connected to database successfully');

    // Check regular_user role directly
    const regularUserRole = await Role.findOne({ name: 'regular_user' });
    if (!regularUserRole) {
      logger.error('❌ Regular user role not found!');
      return;
    }

    logger.info(`\n👤 Regular User Role Details:`);
    logger.info(`  Name: ${regularUserRole.name}`);
    logger.info(`  Display Name: ${regularUserRole.displayName}`);
    logger.info(`  Level: ${regularUserRole.level}`);
    logger.info(`  Total Permissions: ${regularUserRole.permissions.length}`);

    // Check for account permissions specifically
    const accountPerms = regularUserRole.permissions.filter(p => p.startsWith('account.'));
    logger.info(`  Account Permissions: ${accountPerms.length}`);
    
    if (accountPerms.length > 0) {
      accountPerms.forEach(perm => logger.info(`    ✅ ${perm}`));
    } else {
      logger.warn('    ⚠️  No account permissions found!');
    }

    // Show all permissions for regular_user
    logger.info(`\n📋 All permissions for regular_user:`);
    regularUserRole.permissions.slice(0, 20).forEach(perm => logger.info(`  - ${perm}`));
    if (regularUserRole.permissions.length > 20) {
      logger.info(`  ... and ${regularUserRole.permissions.length - 20} more`);
    }

    // Check if account permissions exist in the database
    const accountPermissions = await Permission.find({
      name: { $regex: /^account\./ }
    }).select('name category resource action');
    
    logger.info(`\n💼 Account Permissions in Database: ${accountPermissions.length}`);
    accountPermissions.forEach(perm => {
      logger.info(`  - ${perm.name} (${perm.resource}.${perm.action})`);
    });

    // Check if the permissions that should be assigned actually exist
    const expectedAccountPerms = [
      'account.read.own',
      'account.update.own',
      'account.deactivate.own',
      'account.delete.own',
      'account.subscription.manage.own',
      'account.activities.read.own',
      'account.activities.create.own'
    ];

    logger.info(`\n🔍 Checking expected account permissions:`);
    for (const expectedPerm of expectedAccountPerms) {
      const existsInDB = accountPermissions.some(p => p.name === expectedPerm);
      const assignedToRole = regularUserRole.permissions.includes(expectedPerm);
      
      const dbStatus = existsInDB ? '✅' : '❌';
      const roleStatus = assignedToRole ? '✅' : '❌';
      
      logger.info(`  ${expectedPerm}:`);
      logger.info(`    Database: ${dbStatus}`);
      logger.info(`    Role Assignment: ${roleStatus}`);
    }

    // Check other roles for comparison
    const otherRoles = await Role.find({ 
      name: { $ne: 'regular_user' } 
    }).select('name displayName permissions').limit(5);
    
    logger.info(`\n🎭 Other roles for comparison:`);
    otherRoles.forEach(role => {
      const accountPermsInRole = role.permissions.filter(p => p.startsWith('account.'));
      logger.info(`  ${role.name}: ${accountPermsInRole.length} account permissions`);
    });

  } catch (error) {
    logger.error('❌ Error checking role permissions:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('🔌 Disconnected from database');
  }
}

async function main() {
  try {
    await checkRolePermissions();
  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
} 