const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_ID = '507f1f77bcf86cd799439011'; // Replace with a real user ID

const config = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE' // Replace with actual token
  }
};

const adminConfig = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ADMIN_JWT_TOKEN_HERE' // Replace with admin token
  }
};

async function testRateReviewAPI() {
  console.log('🧪 Testing Simplified Rate & Review API...\n');

  try {
    // Test 1: Create a review
    console.log('1️⃣ Testing POST /api/reviews - Create Review');
    try {
      const reviewData = {
        rating: 5,
        subject: 'Great Experience with MyProfile',
        reason: 'overall_satisfaction',
        feedback: 'This product exceeded my expectations. Great quality and fast delivery.',
        improvementCategories: [
          { category: 'overall_service' },
          { category: 'customer_support' },
          { category: 'speed_efficiency' },
          { category: 'profile_setup_experience' },
          { category: 'ease_of_use_navigation' },
          { category: 'other' }
        ]
      };

      const response = await axios.post(`${BASE_URL}/reviews`, reviewData, config);
      console.log('✅ Success:', response.data.status);
      console.log('   Review ID:', response.data.data._id);
      
      const reviewId = response.data.data._id;
      
      // Test 2: Get all reviews
      console.log('\n2️⃣ Testing GET /api/reviews - Get All Reviews');
      try {
        const response = await axios.get(`${BASE_URL}/reviews?page=1&limit=5`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Reviews found:', response.data.data.reviews.length);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 3: Get review by ID
      console.log('\n3️⃣ Testing GET /api/reviews/' + reviewId);
      try {
        const response = await axios.get(`${BASE_URL}/reviews/${reviewId}`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Review subject:', response.data.data.subject);
        console.log('   Review feedback:', response.data.data.feedback);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 4: Update review
      console.log('\n4️⃣ Testing PUT /api/reviews/' + reviewId + ' - Update Review');
      try {
        const updateData = {
          rating: 4,
          subject: 'Updated Experience with MyProfile',
          reason: 'improved_satisfaction',
          feedback: 'Updated feedback: Still great but could be better.',
          improvementCategories: [
            { category: 'overall_service' },
            { category: 'customer_support' },
            { category: 'speed_efficiency' },
            { category: 'profile_setup_experience' },
            { category: 'ease_of_use_navigation' },
            { category: 'other' }
          ]
        };

        const response = await axios.put(`${BASE_URL}/reviews/${reviewId}`, updateData, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Updated rating:', response.data.data.rating);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 5: Get review statistics
      console.log('\n5️⃣ Testing GET /api/reviews/stats');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/stats`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Average rating:', response.data.data.averageRating);
        console.log('   Total reviews:', response.data.data.totalReviews);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 6: Get user's reviews
      console.log('\n6️⃣ Testing GET /api/reviews/user/me');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/user/me?page=1&limit=5`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   User reviews found:', response.data.data.reviews.length);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 7: Admin - Get all reviews from all users
      console.log('\n7️⃣ Testing GET /api/reviews/admin/all - Admin Route');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/admin/all?page=1&limit=10`, adminConfig);
        console.log('✅ Success:', response.data.status);
        console.log('   All reviews found:', response.data.data.reviews.length);
        console.log('   Total reviews in system:', response.data.data.total);
        
        if (response.data.data.reviews.length > 0) {
          const firstReview = response.data.data.reviews[0];
          console.log('   Sample review - Rating:', firstReview.rating);
          console.log('   Sample review - Subject:', firstReview.subject);
        }
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
        console.log('   Note: This requires admin privileges');
      }

      // Test 8: Delete review
      console.log('\n8️⃣ Testing DELETE /api/reviews/' + reviewId);
      try {
        const response = await axios.delete(`${BASE_URL}/reviews/${reviewId}`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Message:', response.data.data.message);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

    } catch (error) {
      console.log('❌ Error creating review:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }

  console.log('\n🎉 Rate & Review API testing completed!');
}

// Run the tests
testRateReviewAPI(); 