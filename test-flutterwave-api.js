const fetch = require('node-fetch');
require('dotenv').config();

async function testFlutterwaveAPI() {
  try {
    console.log('🧪 Testing Flutterwave API endpoints...\n');
    
    const secretKey = process.env.FLUTTERWAVE_SECRET_KEY;
    const publicKey = process.env.FLUTTERWAVE_PUBLIC_KEY;
    
    console.log('Environment variables:');
    console.log('Secret Key:', secretKey ? '✅ Set' : '❌ Not set');
    console.log('Public Key:', publicKey ? '✅ Set' : '❌ Not set\n');
    
    if (!secretKey) {
      console.log('❌ FLUTTERWAVE_SECRET_KEY not set in environment');
      return;
    }
    
    // Test different Flutterwave API endpoints
    const endpoints = [
      'https://api.flutterwave.com/v3/subscriptions',
      'https://api.flutterwave.com/v3/subscription',
      'https://api.flutterwave.com/v3/payment-plans',
      'https://api.flutterwave.com/v3/payment_plans'
    ];
    
    const testPayload = {
      amount: 1000,
      currency: 'USD',
      interval: 'monthly',
      plan: 'Test Plan',
      customer: '<EMAIL>',
      metadata: {
        userId: 'test_user_123',
        planId: 'test_plan_123',
        planType: 'test'
      }
    };
    
    for (const endpoint of endpoints) {
      console.log(`\n🔍 Testing endpoint: ${endpoint}`);
      
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${secretKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testPayload)
        });
        
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        
        const responseText = await response.text();
        console.log('Response:', responseText.substring(0, 200) + '...');
        
        if (response.ok) {
          console.log('✅ This endpoint works!');
          break;
        }
        
      } catch (error) {
        console.log('❌ Error:', error.message);
      }
    }
    
    // Also test GET request to see available endpoints
    console.log('\n🔍 Testing GET request to see available endpoints...');
    try {
      const response = await fetch('https://api.flutterwave.com/v3/', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${secretKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Status:', response.status);
      const responseText = await response.text();
      console.log('Response:', responseText.substring(0, 500) + '...');
      
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error testing Flutterwave API:', error.message);
  }
}

testFlutterwaveAPI(); 