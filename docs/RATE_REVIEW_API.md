# Rate & Review API Documentation

## Overview

The Rate & Review API provides a simple and clean review and rating functionality for the My Profile application. It supports basic review features with ratings, feedback, and improvement categories.

## Base URL

```
https://api.getmyprofile.online/api/reviews
```

## Authentication

All endpoints require JWT authentication. Include the authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Data Models

### Review Data Structure

```typescript
interface IReviewData {
  rating: number;
  subject?: string;
  reason?: string;
  feedback: string;
  improvementCategories?: Array<{
    category: 'overall_service' | 'customer_support' | 'speed_efficiency' | 'profile_setup_experience' | 'ease_of_use_navigation' | 'other';
  }>;
}
```

### Review Response Structure

```typescript
interface IReviewResponse {
  _id: string;
  userId: string;
  rating: number;
  subject?: string;
  reason?: string;
  feedback: string;
  improvementCategories: Array<{
    category: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}
```

### Review Statistics Structure

```typescript
interface IReviewStats {
  averageRating: number;
  totalReviews: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}
```

## API Endpoints

### 1. Create Review

**POST** `/api/reviews`

Creates a new review.

#### Request Body

```json
{
  "rating": 5,
  "subject": "Great Experience with MyProfile",
  "reason": "overall_satisfaction",
  "feedback": "This product exceeded my expectations. Great quality and fast delivery.",
  "improvementCategories": [
    { "category": "overall_service" },
    { "category": "customer_support" },
    { "category": "speed_efficiency" },
    { "category": "profile_setup_experience" },
    { "category": "ease_of_use_navigation" },
    { "category": "other" }
  ]
}
```

#### Response

```json
{
  "status": "success",
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "rating": 5,
    "subject": "Great Experience with MyProfile",
    "reason": "overall_satisfaction",
    "feedback": "This product exceeded my expectations. Great quality and fast delivery.",
    "improvementCategories": [
      { "category": "overall_service" },
      { "category": "customer_support" },
      { "category": "speed_efficiency" },
      { "category": "profile_setup_experience" },
      { "category": "ease_of_use_navigation" },
      { "category": "other" }
    ],
    "createdAt": "2025-01-15T10:30:00.000Z",
    "updatedAt": "2025-01-15T10:30:00.000Z"
  }
}
```

### 2. Get All Reviews

**GET** `/api/reviews`

Retrieves all reviews with filtering and pagination.

#### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of reviews per page (default: 10)
- `rating` (optional): Filter by rating (1-5)
- `startDate` (optional): Start date for filtering (ISO format)
- `endDate` (optional): End date for filtering (ISO format)
- `sortBy` (optional): Sort field ('rating', 'recent', 'oldest')
- `sortOrder` (optional): Sort order ('asc', 'desc')

#### Response

```json
{
  "status": "success",
  "data": {
    "reviews": [
      {
        "_id": "507f1f77bcf86cd799439011",
        "userId": "507f1f77bcf86cd799439012",
        "rating": 5,
        "subject": "Great Experience with MyProfile",
        "reason": "overall_satisfaction",
        "feedback": "This product exceeded my expectations.",
        "improvementCategories": [
          { "category": "overall_service" },
          { "category": "customer_support" }
        ],
        "createdAt": "2025-01-15T10:30:00.000Z",
        "updatedAt": "2025-01-15T10:30:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 3. Get Review by ID

**GET** `/api/reviews/:reviewId`

Retrieves a specific review by its ID.

#### Response

```json
{
  "status": "success",
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "rating": 5,
    "subject": "Great Experience with MyProfile",
    "reason": "overall_satisfaction",
    "feedback": "This product exceeded my expectations.",
    "improvementCategories": [
      { "category": "overall_service" },
      { "category": "customer_support" }
    ],
    "createdAt": "2025-01-15T10:30:00.000Z",
    "updatedAt": "2025-01-15T10:30:00.000Z"
  }
}
```

### 4. Update Review

**PUT** `/api/reviews/:reviewId`

Updates an existing review (only by the review author).

#### Request Body

```json
{
  "rating": 4,
  "subject": "Updated Experience with MyProfile",
  "reason": "improved_satisfaction",
  "feedback": "Updated feedback: Still great but could be better.",
  "improvementCategories": [
    { "category": "overall_service" },
    { "category": "customer_support" },
    { "category": "speed_efficiency" }
  ]
}
```

#### Response

```json
{
  "status": "success",
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "rating": 4,
    "subject": "Updated Experience with MyProfile",
    "reason": "improved_satisfaction",
    "feedback": "Updated feedback: Still great but could be better.",
    "improvementCategories": [
      { "category": "overall_service" },
      { "category": "customer_support" },
      { "category": "speed_efficiency" }
    ],
    "createdAt": "2025-01-15T10:30:00.000Z",
    "updatedAt": "2025-01-15T11:45:00.000Z"
  }
}
```

### 5. Delete Review

**DELETE** `/api/reviews/:reviewId`

Deletes a review (only by the review author).

#### Response

```json
{
  "status": "success",
  "data": {
    "success": true,
    "message": "Review deleted successfully"
  }
}
```

### 6. Get Review Statistics

**GET** `/api/reviews/stats`

Retrieves overall review statistics.

#### Response

```json
{
  "status": "success",
  "data": {
    "averageRating": 4.2,
    "totalReviews": 150,
    "distribution": {
      "5": 60,
      "4": 45,
      "3": 25,
      "2": 15,
      "1": 5
    }
  }
}
```

### 7. Get User's Reviews

**GET** `/api/reviews/user/me`

Retrieves the current user's reviews.

#### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of reviews per page (default: 10)

#### Response

```json
{
  "status": "success",
  "data": {
    "reviews": [
      {
        "_id": "507f1f77bcf86cd799439011",
        "userId": "507f1f77bcf86cd799439012",
        "rating": 5,
        "subject": "Great Experience with MyProfile",
        "reason": "overall_satisfaction",
        "feedback": "This product exceeded my expectations.",
        "improvementCategories": [
          { "category": "overall_service" },
          { "category": "customer_support" }
        ],
        "createdAt": "2025-01-15T10:30:00.000Z",
        "updatedAt": "2025-01-15T10:30:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 8. Admin - Get All Reviews from All Users

**GET** `/api/reviews/admin/all`

Retrieves all reviews from all users (admin only).

#### Access Control

- **Required Role**: `admin` or `superadmin`
- **Authentication**: Required

#### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of reviews per page (default: 20)
- `rating` (optional): Filter by rating (1-5)
- `startDate` (optional): Start date for filtering (ISO format)
- `endDate` (optional): End date for filtering (ISO format)
- `sortBy` (optional): Sort field ('rating', 'recent', 'oldest')
- `sortOrder` (optional): Sort order ('asc', 'desc')

#### Response

```json
{
  "status": "success",
  "data": {
    "reviews": [
      {
        "_id": "507f1f77bcf86cd799439011",
        "userId": "507f1f77bcf86cd799439012",
        "rating": 5,
        "subject": "Great Experience with MyProfile",
        "reason": "overall_satisfaction",
        "feedback": "This product exceeded my expectations.",
        "improvementCategories": [
          { "category": "overall_service" },
          { "category": "customer_support" }
        ],
        "createdAt": "2025-01-15T10:30:00.000Z",
        "updatedAt": "2025-01-15T10:30:00.000Z"
      }
    ],
    "total": 150,
    "page": 1,
    "limit": 20
  }
}
```

## Error Responses

### 400 Bad Request

```json
{
  "status": "error",
  "message": "Rating must be between 1 and 5"
}
```

### 401 Unauthorized

```json
{
  "status": "error",
  "message": "Authentication required"
}
```

### 403 Forbidden

```json
{
  "status": "error",
  "message": "You can only update your own reviews"
}
```

### 404 Not Found

```json
{
  "status": "error",
  "message": "Review not found"
}
```

### 500 Internal Server Error

```json
{
  "status": "error",
  "message": "Failed to get reviews"
}
```

## Usage Examples

### JavaScript Example

```javascript
const axios = require('axios');

const config = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
};

// Create a review
const createReview = async () => {
  try {
    const response = await axios.post('https://api.getmyprofile.online/api/reviews', {
      rating: 5,
      subject: 'Great Experience',
      reason: 'overall_satisfaction',
      feedback: 'This product exceeded my expectations.',
      improvementCategories: [
        { category: 'overall_service' },
        { category: 'customer_support' }
      ]
    }, config);
    
    console.log('Review created:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Get all reviews
const getReviews = async () => {
  try {
    const response = await axios.get('https://api.getmyprofile.online/api/reviews?page=1&limit=10', config);
    console.log('Reviews:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Admin: Get all reviews from all users
const getAllReviewsForAdmin = async () => {
  try {
    const adminConfig = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_ADMIN_JWT_TOKEN'
      }
    };
    
    const response = await axios.get('https://api.getmyprofile.online/api/reviews/admin/all?page=1&limit=20', adminConfig);
    console.log('All reviews:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};
```

### cURL Examples

#### Create Review

```bash
curl -X POST https://api.getmyprofile.online/api/reviews \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "rating": 5,
    "subject": "Great Experience with MyProfile",
    "reason": "overall_satisfaction",
    "feedback": "This product exceeded my expectations. Great quality and fast delivery.",
    "improvementCategories": [
      { "category": "overall_service" },
      { "category": "customer_support" },
      { "category": "speed_efficiency" }
    ]
  }'
```

#### Get All Reviews

```bash
curl -X GET "https://api.getmyprofile.online/api/reviews?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Admin - Get All Reviews from All Users

```bash
curl -X GET "https://api.getmyprofile.online/api/reviews/admin/all?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

## Notes

- All reviews are associated with the authenticated user
- Users can only update and delete their own reviews
- Admin routes require appropriate role permissions
- The API is simplified and focused on core review functionality
- No entity concept - reviews are general feedback
- Improvement categories help categorize feedback areas

