# Contact Settings API

## Overview

Manage contact settings for users including contact access, linked accounts, sync preferences, custom fields, and permissions.

## Base URL
```
/api/contact-settings
```

## Endpoints

### Create Default Contact Settings
```
POST /api/contact-settings
```

**Description**: Creates default contact settings for a user.

**Headers**:
```
Authorization: Bearer <accessToken>
Content-Type: application/json
```

**Request Body**:
```json
{
  "profileId": "string" // Optional
}
```

**Response (201)**:
```json
{
  "success": true,
  "message": "Contact settings created successfully",
  "settings": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "profileId": "64f8a1b2c3d4e5f6a7b8c9d2",
    "contactAccess": "connectionsOnly",
    "linkedContactAccounts": [],
    "contactSync": false,
    "contactFields": [],
    "phonePermissions": {
      "callLogs": false,
      "sms": false,
      "contacts": false
    },
    "contactSaveTo": {
      "phone": false,
      "myprofile": true
    },
    "contactCircles": {
      "phone": false,
      "myprofile": true
    },
    "createdAt": "2023-12-07T10:30:00.000Z",
    "updatedAt": "2023-12-07T10:30:00.000Z"
  }
}
```

### Get Contact Settings
```
GET /api/contact-settings
```

**Query Parameters**:
- `profileId` (optional): Profile ID to get settings for

**Response (200)**:
```json
{
  "success": true,
  "settings": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "contactAccess": "public",
    "linkedContactAccounts": [
      {
        "platform": "google",
        "isLinked": true,
        "accountId": "google-account-123",
        "email": "<EMAIL>",
        "displayName": "John Doe"
      }
    ],
    "contactSync": true,
    "contactFields": [
      {
        "title": "Phone Number",
        "type": "phone",
        "description": "Primary contact number",
        "required": true
      }
    ],
    "phonePermissions": {
      "callLogs": true,
      "sms": true,
      "contacts": true
    }
  }
}
```

### Update All Contact Settings
```
PUT /api/contact-settings
```

**Request Body**:
```json
{
  "profileId": "string", // Optional
  "contactAccess": "public|connectionsOnly|private",
  "linkedContactAccounts": [
    {
      "platform": "phone|google|outlook|whatsapp|facebook|instagram",
      "isLinked": true,
      "accountId": "account-id",
      "email": "<EMAIL>",
      "displayName": "Display Name"
    }
  ],
  "contactSync": true,
  "contactFields": [
    {
      "title": "Field Title",
      "type": "text|email|phone|url|date|number|select|multiselect",
      "description": "Field description",
      "image": "image-url",
      "required": false,
      "options": ["option1", "option2"] // For select/multiselect
    }
  ],
  "phonePermissions": {
    "callLogs": true,
    "sms": true,
    "contacts": true
  },
  "contactSaveTo": {
    "phone": true,
    "myprofile": true
  },
  "contactCircles": {
    "phone": true,
    "myprofile": true
  }
}
```

### Update Contact Access
```
PATCH /api/contact-settings/access
```

**Request Body**:
```json
{
  "contactAccess": "public|connectionsOnly|private",
  "profileId": "string" // Optional
}
```

### Update Linked Accounts
```
PATCH /api/contact-settings/linked-accounts
```

**Request Body**:
```json
{
  "linkedContactAccounts": [
    {
      "platform": "google",
      "isLinked": true,
      "accountId": "google-123",
      "email": "<EMAIL>"
    }
  ],
  "profileId": "string" // Optional
}
```

### Update Contact Sync
```
PATCH /api/contact-settings/sync
```

**Request Body**:
```json
{
  "contactSync": true,
  "profileId": "string" // Optional
}
```

### Update Contact Fields
```
PATCH /api/contact-settings/fields
```

**Request Body**:
```json
{
  "contactFields": [
    {
      "title": "Custom Field",
      "type": "text",
      "description": "Description",
      "required": false
    }
  ],
  "profileId": "string" // Optional
}
```

### Update Phone Permissions
```
PATCH /api/contact-settings/phone-permissions
```

**Request Body**:
```json
{
  "phonePermissions": {
    "callLogs": true,
    "sms": false,
    "contacts": true
  },
  "profileId": "string" // Optional
}
```

### Update Save To Settings
```
PATCH /api/contact-settings/save-to
```

**Request Body**:
```json
{
  "contactSaveTo": {
    "phone": true,
    "myprofile": true
  },
  "profileId": "string" // Optional
}
```

### Update Contact Circles
```
PATCH /api/contact-settings/circles
```

**Request Body**:
```json
{
  "contactCircles": {
    "phone": false,
    "myprofile": true
  },
  "profileId": "string" // Optional
}
```

### Delete Contact Settings
```
DELETE /api/contact-settings
```

**Query Parameters**:
- `profileId` (optional): Profile ID

**Response (200)**:
```json
{
  "success": true,
  "message": "Contact settings deleted successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid input data",
  "error": "Validation error details"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Contact settings not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Data Types

### Contact Access
- `public`: Visible to everyone
- `connectionsOnly`: Visible to connections only
- `private`: Private/hidden

### Platform Types
- `phone`: Phone contacts
- `google`: Google Contacts
- `outlook`: Outlook Contacts
- `whatsapp`: WhatsApp
- `facebook`: Facebook
- `instagram`: Instagram

### Field Types
- `text`: Text input
- `email`: Email address
- `phone`: Phone number
- `url`: Website URL
- `date`: Date picker
- `number`: Numeric input
- `select`: Single selection dropdown
- `multiselect`: Multiple selection dropdown

## Notes

- All endpoints require authentication
- Settings are user-specific and profile-specific when profileId is provided
- Default settings are created automatically if none exist
- Partial updates are supported via PATCH endpoints
- Full updates use PUT endpoint 