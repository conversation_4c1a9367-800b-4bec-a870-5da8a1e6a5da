# Mobile Google Sign-In Integration Guide

## Overview
This guide explains how to integrate Google Sign-In in your mobile app (Flutter/React Native) with your backend authentication endpoint.

## Backend Endpoint

### **POST /api/auth/google/mobile**

**Base URL:** `https://my-profile-server-api.onrender.com`  
**Endpoint:** `/api/auth/google/mobile`

## Complete Integration Flow

### 1. Mobile App Setup

#### **Flutter Setup:**

**Add dependencies to `pubspec.yaml`:**
```yaml
dependencies:
  google_sign_in: ^6.1.6
  http: ^1.1.0
  flutter_secure_storage: ^9.0.0  # For storing tokens securely
```

**Install dependencies:**
```bash
flutter pub get
```

#### **React Native Setup:**

**Install dependencies:**
```bash
npm install @react-native-google-signin/google-signin
npm install react-native-keychain  # For secure token storage
```

### 2. Google Cloud Console Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Go to **APIs & Services** → **Credentials**
4. Create or use existing **Web application** OAuth 2.0 client ID
5. Note down the **Client ID** (this is your `serverClientId`)

**Your Client ID:** `***********-49htsukh1n89r38fjd5gmv5fekbu9ler.apps.googleusercontent.com`

### 3. Mobile App Implementation

#### **Flutter Implementation:**

```dart
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class GoogleAuthService {
  static const String _baseUrl = 'https://my-profile-server-api.onrender.com';
  static const String _serverClientId = '***********-49htsukh1n89r38fjd5gmv5fekbu9ler.apps.googleusercontent.com';
  
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    serverClientId: _serverClientId,
  );
  
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  Future<Map<String, dynamic>?> signInWithGoogle() async {
    try {
      print('🔄 Starting Google Sign-In...');
      
      // 1. Trigger Google Sign-In
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        print('❌ User cancelled Google Sign-In');
        return null;
      }

      print('✅ Google Sign-In successful for: ${googleUser.email}');
      
      // 2. Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final String? idToken = googleAuth.idToken;
      
      if (idToken == null) {
        print('❌ Failed to get ID token from Google');
        return null;
      }

      print('🔑 Got ID token from Google');
      
      // 3. Send ID token to your backend
      final response = await _authenticateWithBackend(idToken);
      
      if (response != null) {
        // 4. Store tokens securely
        await _storeTokens(response['accessToken'], response['refreshToken']);
        print('✅ Authentication successful, tokens stored');
        return response;
      }
      
    } catch (error) {
      print('❌ Google Sign-In Error: $error');
      rethrow;
    }
    
    return null;
  }

  Future<Map<String, dynamic>?> _authenticateWithBackend(String idToken) async {
    try {
      print('🌐 Sending ID token to backend...');
      
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/google/mobile'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'idToken': idToken,
        }),
      );

      print('📡 Backend response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('✅ Backend authentication successful');
        return data;
      } else {
        final errorData = jsonDecode(response.body);
        print('❌ Backend authentication failed: ${errorData['error']}');
        return null;
      }
      
    } catch (error) {
      print('❌ Network error: $error');
      return null;
    }
  }

  Future<void> _storeTokens(String accessToken, String refreshToken) async {
    await _storage.write(key: 'access_token', value: accessToken);
    await _storage.write(key: 'refresh_token', value: refreshToken);
  }

  Future<String?> getAccessToken() async {
    return await _storage.read(key: 'access_token');
  }

  Future<void> signOut() async {
    await _googleSignIn.signOut();
    await _storage.delete(key: 'access_token');
    await _storage.delete(key: 'refresh_token');
  }
}
```
export default GoogleAuthService;
```

### 4. Usage in Your App

#### **Flutter Usage:**

```dart
class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final GoogleAuthService _authService = GoogleAuthService();
  bool _isLoading = false;

  Future<void> _handleGoogleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _authService.signInWithGoogle();
      
      if (result != null) {
        // Navigate to main app
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Sign-in failed. Please try again.')),
        );
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $error')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Login')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _handleGoogleSignIn,
              child: _isLoading 
                ? CircularProgressIndicator()
                : Text('Sign in with Google'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 5. API Response Format

#### **Success Response (200):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "_id": "67de38b8c849568350604eb1",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "username": "johndoe123",
    "googleId": "**********",
    "isEmailVerified": true,
    "signupType": "google",
    "profileImage": "https://lh3.googleusercontent.com/...",
    "accountType": "MYSELF",
    "role": "regular_user"
  }
}
```

#### **Error Responses:**

**400 Bad Request:**
```json
{
  "error": "idToken is required"
}
```

**401 Unauthorized:**
```json
{
  "error": "Invalid ID token"
}
```

**403 Forbidden (Fraud Detection):**
```json
{
  "success": false,
  "error": {
    "code": "DEVICE_ALREADY_REGISTERED",
    "message": "This device is not eligible for registration. Only one account per device is allowed.",
    "riskScore": 100,
    "flags": ["device_already_registered"],
    "deviceBlocked": true,
    "requiresManualReview": true
  }
}
```

### 6. Using the Access Token

After successful authentication, use the access token for API requests:

```dart
// Flutter
Future<void> makeAuthenticatedRequest() async {
  final accessToken = await _authService.getAccessToken();
  
  final response = await http.get(
    Uri.parse('$_baseUrl/api/user/profile'),
    headers: {
      'Authorization': 'Bearer $accessToken',
    },
  );
  
  // Handle response...
}
```

```javascript
// React Native
async makeAuthenticatedRequest() {
  const accessToken = await this.authService.getAccessToken();
  
  const response = await fetch(`${this.baseUrl}/api/user/profile`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  
  // Handle response...
}
```

### 7. Troubleshooting

#### **Common Issues:**

1. **`clientConfigurationError`**: Make sure `serverClientId` is set correctly
2. **`network_error`**: Check internet connection and backend URL
3. **`invalid_id_token`**: Verify Google Cloud Console configuration
4. **`device_already_registered`**: User needs to use a different device

#### **Debug Steps:**

1. Check console logs for detailed error messages
2. Verify Google Cloud Console OAuth 2.0 configuration
3. Ensure backend is running and accessible
4. Test with different Google accounts

### 8. Security Best Practices

1. **Store tokens securely** using `flutter_secure_storage` or `react-native-keychain`
2. **Never log sensitive data** like tokens
3. **Handle token expiration** by implementing refresh logic
4. **Validate responses** before storing user data
5. **Implement proper error handling** for all network requests

This integration provides a complete, secure Google Sign-In flow for your mobile app with your backend authentication system. 