# Exchange API Documentation

## Overview

The Exchange API enables profile exchanges between users with support for multiple channels, notifications, and MyPts rewards. This API handles the complete lifecycle of profile exchanges from initiation to completion.

**Base URL:** `https://api.getmyprofile.online/api/exchange`

**Authentication:** All endpoints require JWT authentication via `Authorization: Bearer <token>` header.

---

## 🔐 Authentication

All endpoints require a valid JWT token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

## 📋 API Endpoints

### 1. Initiate Exchange

**POST** `/api/exchange/initiate`

Initiate a profile exchange with another user.

#### Request Body

```json
{
  "senderProfileId": "A01V1B2T",           // Profile ID or Secondary ID
  "receiverProfileId": "B84ZK3X9",         // Profile ID or Secondary ID
  "profileName": "Bezing Alain",           // Optional custom label
  "exchangeReason": "Met at BioTech 2025", // Reason for the exchange
  "channel": "in_app",                     // "in_app", "nfc", "qr", or "manual"
  "timestamp": "2025-07-31T19:10:00Z"      // Optional timestamp
}
```

#### Response

```json
{
  "success": true,
  "message": "Exchange initiated successfully",
  "exchangeId": "EXCH8734KXA",
  "myPtsAwarded": 5
}
```

#### Error Responses

- `400` - Missing required fields or invalid channel
- `404` - Profile not found
- `409` - Exchange already exists
- `500` - Internal server error

---

### 2. Get Exchange History

**GET** `/api/exchange/history`

Fetch all profile exchange history for a profile with pagination.

#### Query Parameters

- `profileId` (required) - Profile ID or Secondary ID
- `status` (optional) - Filter by status: "pending", "completed", "declined", "expired", "cancelled"
- `limit` (optional) - Number of results per page (default: 20)
- `offset` (optional) - Number of results to skip (default: 0)

#### Example Request

```http
GET /api/exchange/history?profileId=A01V1B2T&status=pending&limit=10&offset=0
```

#### Response

```json
{
  "success": true,
  "exchanges": [
    {
      "exchangeId": "EXCH8734KXA",
      "senderProfileId": "A01V1B2T",
      "receiverProfileId": "B84ZK3X9",
      "profileName": "Bezing Alain",
      "exchangeReason": "Met at BioTech 2025",
      "channel": "in_app",
      "timestamp": "2025-07-31T19:10:00Z",
      "status": "pending",
      "isSender": true,
      "otherProfile": {
        "id": "B84ZK3X9",
        "secondaryId": "B84ZK3X9",
        "name": "John Doe",
        "username": "johndoe"
      },
      "myPtsAwarded": 5
    }
  ],
  "pagination": {
    "total": 25,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  }
}
```

---

### 3. Get Exchange Details

**GET** `/api/exchange/:exchangeId`

Get detailed information for a specific exchange.

#### Example Request

```http
GET /api/exchange/EXCH8734KXA
```

#### Response

```json
{
  "success": true,
  "exchange": {
    "exchangeId": "EXCH8734KXA",
    "senderProfileId": "A01V1B2T",
    "receiverProfileId": "B84ZK3X9",
    "profileName": "Bezing Alain",
    "exchangeReason": "Met at BioTech 2025",
    "channel": "qr",
    "timestamp": "2025-07-31T19:10:00Z",
    "status": "pending",
    "exchangeData": {
      "senderProfile": {
        "id": "A01V1B2T",
        "secondaryId": "A01V1B2T",
        "name": "Bezing Alain",
        "username": "bezing",
        "title": "Software Engineer"
      },
      "receiverProfile": {
        "id": "B84ZK3X9",
        "secondaryId": "B84ZK3X9",
        "name": "John Doe",
        "username": "johndoe",
        "title": "Product Manager"
      },
      "sharedSections": ["contact", "social"],
      "customMessage": "Great meeting you at the conference!"
    },
    "analytics": {
      "viewed": true,
      "viewedAt": "2025-07-31T19:15:00Z",
      "responded": false,
      "myPtsAwarded": 5,
      "engagementScore": 75
    },
    "metadata": {
      "deviceInfo": {
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "***********",
        "deviceType": "mobile"
      },
      "location": {
        "city": "San Francisco",
        "country": "US"
      },
      "tags": ["conference", "networking", "business"]
    },
    "expiresAt": "2025-08-30T19:10:00Z",
    "completedAt": null
  }
}
```

---

### 4. Complete Exchange

**POST** `/api/exchange/:exchangeId/complete`

Accept and complete an exchange.

#### Request Body

```json
{
  "profileId": "B84ZK3X9"  // ID of the profile completing the exchange
}
```

#### Response

```json
{
  "success": true,
  "message": "Exchange completed successfully",
  "myPtsAwarded": 10
}
```

---

### 5. Decline Exchange

**POST** `/api/exchange/:exchangeId/decline`

Decline an exchange request.

#### Request Body

```json
{
  "profileId": "B84ZK3X9",  // ID of the profile declining the exchange
  "reason": "Not interested at this time"  // Optional reason
}
```

#### Response

```json
{
  "success": true,
  "message": "Exchange declined successfully"
}
```

---

### 6. Cancel Exchange

**DELETE** `/api/exchange/:exchangeId`

Cancel/retract an exchange (only sender can cancel).

#### Request Body

```json
{
  "profileId": "A01V1B2T"  // ID of the profile cancelling the exchange
}
```

#### Response

```json
{
  "success": true,
  "message": "Exchange cancelled successfully"
}
```

---

## 🔄 Exchange Status Flow

```
PENDING → COMPLETED (accepted)
       → DECLINED (rejected)
       → CANCELLED (retracted by sender)
       → EXPIRED (automatic after 30 days)
```

---

## 🏆 MyPts Rewards

- **Initiate Exchange:** 5 MyPts
- **Complete Exchange:** 10 MyPts (both parties)
- **Total per completed exchange:** 15 MyPts

---

## 📱 Supported Channels

- **in_app** - Exchange initiated within the app
- **nfc** - Exchange via NFC tap
- **qr** - Exchange via QR code scan
- **manual** - Manually entered exchange

---

## 🔔 Notifications

The API automatically sends notifications for:

- **Exchange Initiated** - Receiver gets notified
- **Exchange Completed** - Both parties get notified
- **Exchange Declined** - Sender gets notified
- **Exchange Cancelled** - Receiver gets notified

Notifications are sent via:
- In-app notifications
- Email notifications
- Push notifications (if configured)

---

## 📊 Analytics & Tracking

Each exchange tracks:

- **View Analytics** - When exchange was viewed
- **Response Analytics** - When exchange was responded to
- **Device Information** - IP, user agent, device type
- **Location Data** - City, country (if available)
- **Engagement Score** - Calculated based on interactions

---

## 🛡️ Security Features

- **Rate Limiting** - 10 exchange initiations per 15 minutes
- **Authentication Required** - All endpoints protected
- **Profile Validation** - Verifies profile ownership
- **Duplicate Prevention** - Prevents duplicate exchanges
- **Self-Exchange Prevention** - Cannot exchange with yourself

---

## 📝 Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 400 | Missing required fields | Required parameters not provided |
| 400 | Invalid channel | Channel must be in_app, nfc, qr, or manual |
| 400 | Cannot exchange with yourself | Self-exchange not allowed |
| 404 | Profile not found | Profile ID or Secondary ID not found |
| 404 | Exchange not found | Exchange ID not found |
| 409 | Exchange already exists | Exchange between profiles already exists |
| 403 | Not authorized | User not authorized for this action |
| 500 | Internal server error | Server error occurred |

---

## 🧪 Example Usage

### Complete Flow Example

```javascript
// 1. Initiate exchange
const initiateResponse = await fetch('/api/exchange/initiate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    senderProfileId: 'A01V1B2T',
    receiverProfileId: 'B84ZK3X9',
    profileName: 'Bezing Alain',
    exchangeReason: 'Met at BioTech 2025',
    channel: 'in_app'
  })
});

// 2. Get exchange history
const historyResponse = await fetch('/api/exchange/history?profileId=A01V1B2T', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

// 3. Complete exchange
const completeResponse = await fetch('/api/exchange/EXCH8734KXA/complete', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    profileId: 'B84ZK3X9'
  })
});
```

---

## 📞 Support

For API support or questions:
- Email: <EMAIL>
- Documentation: https://docs.getmyprofile.online
- Status: https://status.getmyprofile.online 