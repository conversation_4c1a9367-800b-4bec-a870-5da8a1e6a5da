# Mobile Google Authentication API

## Endpoint

```
POST /api/auth/google/mobile
```

## Description

Authenticates a user using a Google ID token obtained from the Google Sign-In SDK for mobile applications.

## Request

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "idToken": "string"
}
```

#### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `idToken` | string | Yes | Google ID token obtained from Google Sign-In SDK |

## Response

### Success Response (200)
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "_id": "67de38b8c849568350604eb1",
    "email": "<EMAIL>",
    "fullName": "<PERSON>",
    "username": "johndoe123",
    "googleId": "**********",
    "isEmailVerified": true,
    "signupType": "google",
    "profileImage": "https://lh3.googleusercontent.com/...",
    "accountType": "MYSELF",
    "role": "user"
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "error": "idToken is required"
}
```

#### 401 Unauthorized
```json
{
  "error": "Invalid ID token"
}
```

#### 403 Forbidden (Fraud Detection)
```json
{
  "success": false,
  "error": {
    "code": "DEVICE_ALREADY_REGISTERED",
    "message": "This device is not eligible for registration. Only one account per device is allowed.",
    "riskScore": 100,
    "flags": ["device_already_registered"],
    "deviceBlocked": true,
    "requiresManualReview": true
  }
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Usage Example

```bash
curl -X POST https://your-api-url/api/auth/google/mobile \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6..."
  }'
```

## Token Usage

### Access Token
- **Type**: JWT
- **Expires**: 4 hours (with activity-based refresh)
- **Usage**: Include in Authorization header for protected endpoints
```
Authorization: Bearer <accessToken>
```

### Refresh Token
- **Type**: JWT  
- **Expires**: 15 days (standard) / 30 days (with "Remember Me")
- **Usage**: Use to obtain new access tokens when they expire

### Activity-Based Refresh
- Tokens are automatically refreshed when you have less than 30 minutes remaining
- Continuous usage keeps you logged in without manual token refresh
- Access tokens refresh automatically during API calls when needed

## Notes

- User accounts are automatically created if they don't exist
- Existing users with matching email will have their Google account linked
- Email verification is automatically set to `true` for Google authenticated users
- The endpoint includes fraud detection and device fingerprinting
- Only one account per device is allowed by default 