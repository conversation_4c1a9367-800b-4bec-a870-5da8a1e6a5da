# Connection Settings API

## Overview

Manage connection settings for users including connection access, linked accounts, sync preferences, custom fields, and permissions.

## Base URL
```
/api/connection-settings
```

## Endpoints

### Create Default Connection Settings
```
POST /api/connection-settings
```

**Description**: Creates default connection settings for a user.

**Headers**:
```
Authorization: Bearer <accessToken>
Content-Type: application/json
```

**Request Body**:
```json
{
  "profileId": "string" // Optional
}
```

**Response (201)**:
```json
{
  "success": true,
  "message": "Connection settings created successfully",
  "settings": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "profileId": "64f8a1b2c3d4e5f6a7b8c9d2",
    "connectionAccess": "connectionsOnly",
    "linkedConnectionAccounts": [],
    "connectionSync": false,
    "connectionFields": [],
    "phonePermissions": {
      "callLogs": false,
      "sms": false,
      "contacts": false
    },
    "connectionSaveTo": {
      "phone": false,
      "myprofile": true
    },
    "connectionCircles": {
      "phone": false,
      "myprofile": true
    },
    "createdAt": "2023-12-07T10:30:00.000Z",
    "updatedAt": "2023-12-07T10:30:00.000Z"
  }
}
```

### Get Connection Settings
```
GET /api/connection-settings
```

**Query Parameters**:
- `profileId` (optional): Profile ID to get settings for

**Response (200)**:
```json
{
  "success": true,
  "settings": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "connectionAccess": "public",
    "linkedConnectionAccounts": [
      {
        "platform": "linkedin",
        "isLinked": true,
        "accountId": "linkedin-account-123",
        "email": "<EMAIL>",
        "displayName": "John Doe"
      }
    ],
    "connectionSync": true,
    "connectionFields": [
      {
        "title": "Professional Title",
        "type": "text",
        "description": "Current job title",
        "required": false
      }
    ],
    "phonePermissions": {
      "callLogs": false,
      "sms": false,
      "contacts": true
    }
  }
}
```

### Update All Connection Settings
```
PUT /api/connection-settings
```

**Request Body**:
```json
{
  "profileId": "string", // Optional
  "connectionAccess": "public|connectionsOnly|private",
  "linkedConnectionAccounts": [
    {
      "platform": "phone|google|outlook|whatsapp|facebook|instagram",
      "isLinked": true,
      "accountId": "account-id",
      "email": "<EMAIL>",
      "displayName": "Display Name"
    }
  ],
  "connectionSync": true,
  "connectionFields": [
    {
      "title": "Field Title",
      "type": "text|email|phone|url|date|number|select|multiselect",
      "description": "Field description",
      "image": "image-url",
      "required": false,
      "options": ["option1", "option2"] // For select/multiselect
    }
  ],
  "phonePermissions": {
    "callLogs": false,
    "sms": false,
    "contacts": true
  },
  "connectionSaveTo": {
    "phone": true,
    "myprofile": true
  },
  "connectionCircles": {
    "phone": false,
    "myprofile": true
  }
}
```

### Update Connection Access
```
PATCH /api/connection-settings/access
```

**Request Body**:
```json
{
  "connectionAccess": "public|connectionsOnly|private",
  "profileId": "string" // Optional
}
```

### Update Linked Accounts
```
PATCH /api/connection-settings/linked-accounts
```

**Request Body**:
```json
{
  "linkedConnectionAccounts": [
    {
      "platform": "linkedin",
      "isLinked": true,
      "accountId": "linkedin-123",
      "email": "<EMAIL>"
    }
  ],
  "profileId": "string" // Optional
}
```

### Update Connection Sync
```
PATCH /api/connection-settings/sync
```

**Request Body**:
```json
{
  "connectionSync": true,
  "profileId": "string" // Optional
}
```

### Update Connection Fields
```
PATCH /api/connection-settings/fields
```

**Request Body**:
```json
{
  "connectionFields": [
    {
      "title": "Professional Title",
      "type": "text",
      "description": "Current job title",
      "required": false
    }
  ],
  "profileId": "string" // Optional
}
```

### Update Phone Permissions
```
PATCH /api/connection-settings/phone-permissions
```

**Request Body**:
```json
{
  "phonePermissions": {
    "callLogs": false,
    "sms": false,
    "contacts": true
  },
  "profileId": "string" // Optional
}
```

### Update Save To Settings
```
PATCH /api/connection-settings/save-to
```

**Request Body**:
```json
{
  "connectionSaveTo": {
    "phone": false,
    "myprofile": true
  },
  "profileId": "string" // Optional
}
```

### Update Connection Circles
```
PATCH /api/connection-settings/circles
```

**Request Body**:
```json
{
  "connectionCircles": {
    "phone": false,
    "myprofile": true
  },
  "profileId": "string" // Optional
}
```

### Delete Connection Settings
```
DELETE /api/connection-settings
```

**Query Parameters**:
- `profileId` (optional): Profile ID

**Response (200)**:
```json
{
  "success": true,
  "message": "Connection settings deleted successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid input data",
  "error": "Validation error details"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Connection settings not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Data Types

### Connection Access
- `public`: Visible to everyone
- `connectionsOnly`: Visible to connections only
- `private`: Private/hidden

### Platform Types
- `phone`: Phone contacts
- `google`: Google Contacts
- `outlook`: Outlook Contacts
- `whatsapp`: WhatsApp
- `facebook`: Facebook
- `instagram`: Instagram

### Field Types
- `text`: Text input
- `email`: Email address
- `phone`: Phone number
- `url`: Website URL
- `date`: Date picker
- `number`: Numeric input
- `select`: Single selection dropdown
- `multiselect`: Multiple selection dropdown

## Use Cases

### Professional Networking
- Set connection access to "connectionsOnly" for professional privacy
- Link LinkedIn and Outlook accounts for professional networking
- Create custom fields for job title, company, industry
- Disable phone permissions for privacy

### Business Connections
- Enable connection sync for CRM integration
- Create custom fields for lead status, company size, interests
- Save connections to both phone and myprofile for backup

### Social Connections
- Set access to "public" for open networking
- Link multiple social platforms (Facebook, Instagram, WhatsApp)
- Enable all phone permissions for seamless contact management

## Notes

- All endpoints require authentication
- Settings are user-specific and profile-specific when profileId is provided
- Default settings are created automatically if none exist
- Partial updates are supported via PATCH endpoints
- Full updates use PUT endpoint
- Connection settings focus on professional and business networking scenarios
