# Transfer Logs API Documentation

## Overview

The Transfer Logs API provides comprehensive tracking of account and profile transfers with visual icons for easy identification. This API allows users to view transfer history, transfer account ownership (secondary accounts only), and transfer/claim profiles with proper subscription validation.

**Key Features:**
- **Database Storage**: All transfer logs are stored in MongoDB using the `TransferLog` model
- **Visual Icons**: Each transfer log entry includes an appropriate icon for easy identification
- **Real-time Logging**: All transfers are automatically logged to the database
- **Statistics**: Get transfer statistics and analytics
- **Pagination**: Support for large datasets with efficient querying
- **Filtering**: Filter by transfer type, user, and date ranges

## Database Model

### TransferLog Schema

The API uses a dedicated `TransferLog` model to store all transfer activities:

```typescript
interface ITransferLog {
  type: 'account' | 'profile';
  action: 'claimed' | 'transferred' | 'ownership_change';
  fromUserId: ObjectId;
  toUserId: ObjectId;
  fromUserName: string;
  toUserName: string;
  icon: string;
  actionText: string;
  myPts: number;
  details: {
    profileId?: ObjectId;
    accountId?: ObjectId;
    reason?: string;
    profileType?: string;
    accountType?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}
```

**Database Indexes:**
- `type` and `action` for efficient filtering
- `fromUserId` and `toUserId` for user-specific queries
- `createdAt` for chronological sorting
- `profileId` and `accountId` for specific resource queries

## Base URL

```
http://localhost:3000/api
```

## Authentication

All endpoints require authentication using JWT Bearer token:

```
Authorization: Bearer <your-jwt-token>
```

## Data Models

### ITransferLog Interface

```typescript
interface ITransferLog {
  type: 'account' | 'profile';
  user: string;
  action: string;
  icon: string;
  date: string;
  myPts: number;
  details?: {
    fromUserId?: string;
    toUserId?: string;
    profileId?: string;
    accountId?: string;
    reason?: string;
  };
}
```

### ITransferLogsResponse Interface

```typescript
interface ITransferLogsResponse {
  logs: ITransferLog[];
  total: number;
  page: number;
  limit: number;
}
```

## API Endpoints

### 1. Get Transfer Logs

**Endpoint:** `GET /api/accounts/transfer/logs`

**Description:** Retrieve transfer logs with icons for account and profile transfers.

**Access:** Authenticated users

**Query Parameters:**
- `type` (optional): Filter by type - `'account'` | `'profile'`
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `userId` (optional): Filter by specific user ID

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "logs": [
      {
        "type": "profile",
        "user": "Benny Spanbauer",
        "action": "Claimed Personal Profile",
        "icon": "👤✅",
        "date": "2022-01-15T08:30:00Z",
        "myPts": 12
      },
      {
        "type": "profile",
        "user": "Charlotte Hanlin",
        "action": "Transferred Personal Profile",
        "icon": "🔁👤",
        "date": "2022-01-15T08:30:00Z",
        "myPts": 24
      },
      {
        "type": "account",
        "user": "Clinton Mcclure",
        "action": "Transferred Secondary Account",
        "icon": "🔁💼",
        "date": "2022-01-15T08:30:00Z",
        "myPts": 19
      },
      {
        "type": "account",
        "user": "Joseph Castillo",
        "action": "Transferred Account Ownership",
        "icon": "🔄🔐",
        "date": "2022-01-15T08:30:00Z",
        "myPts": 25
      }
    ],
    "total": 4,
    "page": 1,
    "limit": 20
  }
}
```

### 2. Transfer Account Ownership

**Endpoint:** `POST /api/accounts/:accountId/transfer`

**Description:** Transfer ownership of a secondary account to another user.

**Access:** Authenticated users (account owner)

**Path Parameters:**
- `accountId`: The ID of the account to transfer

**Request Body:**
```json
{
  "toUserId": "60d5ecb54b24c0001f5f3e8d",
  "reason": "Transfer to family member"
}
```

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "success": true,
    "message": "Account ownership transferred successfully"
  }
}
```

**Validation Rules:**
- Only secondary accounts can be transferred
- Receiving user must have active subscription
- Receiving user must have available secondary account slots

### 3. Transfer Profile

**Endpoint:** `POST /api/profiles/:profileId/transfer`

**Description:** Transfer a profile to another user.

**Access:** Authenticated users (profile owner)

**Path Parameters:**
- `profileId`: The ID of the profile to transfer

**Request Body:**
```json
{
  "toUserId": "60d5ecb54b24c0001f5f3e8d",
  "reason": "Transfer to colleague"
}
```

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "success": true,
    "message": "Profile transferred successfully"
  }
}
```

**Validation Rules:**
- User can only transfer their own profiles
- Receiving user must have active subscription
- Receiving user must have available profile slots for the profile type

### 4. Claim Profile

**Endpoint:** `POST /api/profiles/:profileId/claim`

**Description:** Claim a shared or unclaimed profile.

**Access:** Authenticated users

**Path Parameters:**
- `profileId`: The ID of the profile to claim

**Response Example:**
```json
{
  "status": "success",
  "data": {
    "success": true,
    "message": "Profile claimed successfully"
  }
}
```

**Validation Rules:**
- Profile must be available for claiming (unclaimed or shared)
- User must have active subscription
- User must have available profile slots for the profile type

### 5. Get Transfer Statistics

**Endpoint:** `GET /api/accounts/transfer/stats`

**Description:** Get transfer statistics and analytics.

**Access:** Authenticated users

**Query Parameters:**
- `userId` (optional): Filter statistics by specific user ID

**Response Example:**
```json
{
  "status": "success",
  "data": [
    {
      "_id": "account",
      "actions": [
        {
          "action": "transferred",
          "count": 5,
          "totalMyPts": 95
        },
        {
          "action": "ownership_change",
          "count": 2,
          "totalMyPts": 50
        }
      ],
      "totalCount": 7,
      "totalMyPts": 145
    },
    {
      "_id": "profile",
      "actions": [
        {
          "action": "claimed",
          "count": 10,
          "totalMyPts": 120
        },
        {
          "action": "transferred",
          "count": 3,
          "totalMyPts": 72
        }
      ],
      "totalCount": 13,
      "totalMyPts": 192
    }
  ]
}
```

## Icon Legend

The API uses visual icons to distinguish between different types of transfers and actions:

| Transfer Type | Action | Icon | Description |
|---------------|--------|------|-------------|
| Profile | Claimed | 👤✅ | Profile claimed by a user |
| Profile | Transferred | 🔁👤 | Profile moved to another account |
| Account | Transferred | 🔁💼 | Account transferred |
| Account | Ownership Change | 🔄🔐 | Account ownership changed |

## MyPts Rewards

Different transfer actions award different amounts of MyPts:

- **Profile Claim**: 12 MyPts
- **Profile Transfer**: 24 MyPts
- **Account Transfer**: 19 MyPts
- **Account Ownership Transfer**: 25 MyPts

## Subscription Validation

### Account Transfers
- Only secondary accounts can be transferred
- Receiving user must have an active subscription
- Receiving user must have available secondary account slots based on their plan

### Profile Transfers
- All profile types can be transferred
- Receiving user must have an active subscription
- Profile limits are checked based on profile type:
  - Personal/Business/Academic profiles: Check individual profile limits
  - Group profiles: Check group profile limits

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Only secondary accounts can be transferred"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "User authentication required"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "You can only transfer your own profiles"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Profile or user not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to get transfer logs"
}
```

## Usage Examples

### JavaScript (Axios)

```javascript
const axios = require('axios');

// Get all transfer logs
const getTransferLogs = async () => {
  try {
    const response = await axios.get('http://localhost:3000/api/accounts/transfer/logs', {
      headers: {
        'Authorization': 'Bearer your-jwt-token'
      }
    });
    console.log('Transfer logs:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Get only account transfer logs
const getAccountLogs = async () => {
  try {
    const response = await axios.get('http://localhost:3000/api/accounts/transfer/logs?type=account', {
      headers: {
        'Authorization': 'Bearer your-jwt-token'
      }
    });
    console.log('Account transfer logs:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Transfer account ownership
const transferAccount = async (accountId, toUserId, reason) => {
  try {
    const response = await axios.post(`http://localhost:3000/api/accounts/${accountId}/transfer`, {
      toUserId,
      reason
    }, {
      headers: {
        'Authorization': 'Bearer your-jwt-token',
        'Content-Type': 'application/json'
      }
    });
    console.log('Transfer result:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Transfer profile
const transferProfile = async (profileId, toUserId, reason) => {
  try {
    const response = await axios.post(`http://localhost:3000/api/profiles/p/${profileId}/transfer`, {
      toUserId,
      reason
    }, {
      headers: {
        'Authorization': 'Bearer your-jwt-token',
        'Content-Type': 'application/json'
      }
    });
    console.log('Profile transfer result:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Claim profile
const claimProfile = async (profileId) => {
  try {
    const response = await axios.post(`http://localhost:3000/api/profiles/p/${profileId}/claim`, {}, {
      headers: {
        'Authorization': 'Bearer your-jwt-token',
        'Content-Type': 'application/json'
      }
    });
    console.log('Claim result:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};
```

### cURL

```bash
# Get all transfer logs
curl -X GET "http://localhost:3000/api/accounts/transfer/logs" \
  -H "Authorization: Bearer your-jwt-token"

# Get account transfer logs only
curl -X GET "http://localhost:3000/api/accounts/transfer/logs?type=account" \
  -H "Authorization: Bearer your-jwt-token"

# Transfer account ownership
curl -X POST "http://localhost:3000/api/accounts/account-id/transfer" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "toUserId": "60d5ecb54b24c0001f5f3e8d",
    "reason": "Transfer to family member"
  }'

# Transfer profile
curl -X POST "http://localhost:3000/api/profiles/p/profile-id/transfer" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "toUserId": "60d5ecb54b24c0001f5f3e8d",
    "reason": "Transfer to colleague"
  }'

# Claim profile
curl -X POST "http://localhost:3000/api/profiles/p/profile-id/claim" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

## Testing

Use the provided test script to verify the API functionality:

```bash
node test-transfer-logs-api.js
```

The test script includes:
- Transfer logs retrieval with different filters
- Account ownership transfer
- Profile transfer and claiming
- Icon legend verification
- Response format validation
- Error handling tests
- Database integration tests
- Transfer statistics testing

## Database Management

### Seeding Transfer Logs

To populate the database with sample transfer logs for testing:

```bash
# Run the seeding script
npx ts-node src/scripts/seed-transfer-logs.ts
```

The seeding script will:
- Create sample transfer logs with realistic data
- Use existing users if available, or create system users
- Generate logs with random dates within the last 30 days
- Provide statistics on the seeded data

### Database Queries

The TransferLog model provides efficient querying capabilities:

```javascript
// Get all transfer logs for a user
const userLogs = await TransferLogModel.find({
  $or: [
    { fromUserId: userId },
    { toUserId: userId }
  ]
}).sort({ createdAt: -1 });

// Get account transfers only
const accountTransfers = await TransferLogModel.find({
  type: 'account'
}).sort({ createdAt: -1 });

// Get recent profile claims
const recentClaims = await TransferLogModel.find({
  type: 'profile',
  action: 'claimed'
}).sort({ createdAt: -1 }).limit(10);
```

### Performance Optimization

The model includes optimized indexes for common queries:
- **Type and Action**: For filtering by transfer type and action
- **User IDs**: For user-specific queries
- **Timestamps**: For chronological sorting
- **Resource IDs**: For specific profile/account queries

## Notes

1. **Secondary Accounts Only**: Only secondary accounts can be transferred, not primary accounts
2. **Profile Limits**: Profile transfers are validated against the receiving user's subscription limits
3. **MyPts Rewards**: Different actions award different amounts of MyPts
4. **Icon System**: Visual icons help distinguish between different transfer types and actions
5. **Subscription Validation**: All transfers require proper subscription validation
6. **Ownership Verification**: Users can only transfer their own profiles and accounts

## Security Considerations

- All endpoints require authentication
- Users can only transfer their own resources
- Subscription limits are enforced
- Transfer logs are maintained for audit purposes
- Proper error handling prevents unauthorized transfers