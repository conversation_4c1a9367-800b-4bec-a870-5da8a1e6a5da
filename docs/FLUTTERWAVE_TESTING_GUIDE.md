# Flutterwave Subscription Testing Guide

## 🔧 Prerequisites

### Environment Variables
Add these to your `.env` file:

```env
# Flutterwave Configuration
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST_... # Your Flutterwave test secret key
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST_... # Your Flutterwave test public key
FLUTTERWAVE_SECRET_HASH=your_secret_hash # Your Flutterwave secret hash for webhook validation
```

### Flutterwave Dashboard Setup
1. Log into your Flutterwave dashboard
2. Go to Settings → API Keys
3. Copy your test keys (starts with `FLWSECK_TEST_` and `FLWPUBK_TEST_`)
4. Set up webhook URL: `https://your-domain.com/api/subscriptions/webhook/flutterwave`

## 🧪 Testing Methods

### Method 1: Direct API Testing

#### 1.1 Test Subscription Creation
```bash
# Subscribe to a plan using Flutterwave
curl -X POST http://localhost:3000/api/subscriptions/subscribe \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "688b300120fdcb1fabb5e585",
    "billingCycle": "monthly",
    "paymentProvider": "flutterwave"
  }'
```

#### 1.2 Test Webhook Processing
```bash
# Simulate Flutterwave webhook for charge.completed
curl -X POST http://localhost:3000/api/subscriptions/webhook/flutterwave \
  -H "Content-Type: application/json" \
  -H "flutterwave-signature: YOUR_SIGNATURE_HASH" \
  -d '{
    "event": "charge.completed",
    "data": {
      "id": "test_charge_123",
      "tx_ref": "test_transaction_123",
      "flw_ref": "FLW123456789",
      "amount": 1000,
      "currency": "USD",
      "customer": {
        "email": "<EMAIL>"
      },
      "meta": {
        "userId": "681d8a68d3bffe4846ffca7f",
        "planId": "688b300120fdcb1fabb5e585",
        "planType": "basic"
      },
      "status": "successful",
      "payment_type": "card",
      "created_at": "2025-07-31T10:00:00.000Z"
    }
  }'
```

#### 1.3 Test Subscription Activation
```bash
# Simulate subscription.activated webhook
curl -X POST http://localhost:3000/api/subscriptions/webhook/flutterwave \
  -H "Content-Type: application/json" \
  -H "flutterwave-signature: YOUR_SIGNATURE_HASH" \
  -d '{
    "event": "subscription.activated",
    "data": {
      "subscription_id": "test_sub_123",
      "customer": {
        "email": "<EMAIL>"
      },
      "meta": {
        "userId": "681d8a68d3bffe4846ffca7f",
        "planId": "688b300120fdcb1fabb5e585"
      },
      "status": "active",
      "created_at": "2025-07-31T10:00:00.000Z"
    }
  }'
```

### Method 2: Using Flutterwave Test Cards

#### 2.1 Test Card Numbers
Use these test card numbers in your Flutterwave integration:

| Card Type | Card Number | Expiry | CVV | Expected Result |
|-----------|-------------|--------|-----|-----------------|
| Visa | **************** | 01/25 | 408 | Success |
| Mastercard | **************** | 01/25 | 408 | Success |
| Verve | 5061460410120223210 | 01/25 | 408 | Success |
| Failed Card | **************** | 01/25 | 408 | Failed |

#### 2.2 Test OTP Codes
- **Success OTP**: `123456`
- **Failed OTP**: `000000`

### Method 3: Automated Testing Script

Create a test script to automate the testing process:

```javascript
// test-flutterwave-subscription.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const USER_TOKEN = 'YOUR_USER_JWT_TOKEN';

async function testFlutterwaveSubscription() {
  try {
    console.log('🧪 Testing Flutterwave Subscription System...\n');

    // 1. Get available plans
    console.log('1. Getting available plans...');
    const plansResponse = await axios.get(`${BASE_URL}/api/subscriptions/plans`);
    console.log('✅ Plans retrieved:', plansResponse.data.data.length, 'plans found\n');

    // 2. Subscribe to Basic plan with Flutterwave
    console.log('2. Subscribing to Basic plan with Flutterwave...');
    const subscribeResponse = await axios.post(
      `${BASE_URL}/api/subscriptions/subscribe`,
      {
        planId: '688b300120fdcb1fabb5e585', // Basic plan ID
        billingCycle: 'monthly',
        paymentProvider: 'flutterwave'
      },
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Subscription created:', subscribeResponse.data.data.subscriptionId, '\n');

    // 3. Simulate webhook for successful payment
    console.log('3. Simulating Flutterwave webhook for successful payment...');
    const webhookPayload = {
      event: 'charge.completed',
      data: {
        id: 'test_charge_' + Date.now(),
        tx_ref: 'test_transaction_' + Date.now(),
        flw_ref: 'FLW' + Date.now(),
        amount: 1000,
        currency: 'USD',
        customer: {
          email: '<EMAIL>'
        },
        meta: {
          userId: '681d8a68d3bffe4846ffca7f',
          planId: '688b300120fdcb1fabb5e585',
          planType: 'basic'
        },
        status: 'successful',
        payment_type: 'card',
        created_at: new Date().toISOString()
      }
    };

    const webhookResponse = await axios.post(
      `${BASE_URL}/api/subscriptions/webhook/flutterwave`,
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'flutterwave-signature': 'test_signature'
        }
      }
    );
    console.log('✅ Webhook processed successfully\n');

    // 4. Check subscription status
    console.log('4. Checking subscription status...');
    const subscriptionResponse = await axios.get(
      `${BASE_URL}/api/subscriptions/my-subscription`,
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`
        }
      }
    );
    console.log('✅ Current subscription:', subscriptionResponse.data.data.planName, '\n');

    console.log('🎉 All tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFlutterwaveSubscription();
```

### Method 4: Using Flutterwave Test Dashboard

1. **Log into Flutterwave Test Dashboard**
   - Go to https://sandbox.flutterwave.com
   - Use your test account credentials

2. **Create Test Subscription**
   - Navigate to Subscriptions
   - Create a new subscription plan
   - Set up webhook URL: `https://your-domain.com/api/subscriptions/webhook/flutterwave`

3. **Test Payment Flow**
   - Use test card numbers provided above
   - Complete the payment flow
   - Check webhook delivery in your logs

## 🔍 Monitoring and Debugging

### 1. Check Webhook Logs
```bash
# Monitor webhook requests
tail -f logs/app.log | grep "Flutterwave webhook"
```

### 2. Verify Database Updates
```javascript
// Check if UserPlan was created/updated
db.userplans.findOne({ userId: ObjectId("681d8a68d3bffe4846ffca7f") })
```

### 3. Test Webhook Signature Validation
```javascript
// Generate test signature
const crypto = require('crypto');
const payload = JSON.stringify(webhookPayload);
const signature = crypto
  .createHmac('sha512', process.env.FLUTTERWAVE_SECRET_HASH)
  .update(payload)
  .digest('hex');
```

## 🚨 Common Issues and Solutions

### Issue 1: Webhook Signature Validation Fails
**Solution**: Ensure `FLUTTERWAVE_SECRET_HASH` is correctly set and matches your Flutterwave dashboard.

### Issue 2: Subscription Not Created
**Solution**: Check if the plan ID exists in your database and the user has proper permissions.

### Issue 3: Payment Fails
**Solution**: Verify you're using the correct test card numbers and the Flutterwave account is properly configured.

### Issue 4: Webhook Not Received
**Solution**: 
1. Ensure your webhook URL is publicly accessible
2. Check if your server can receive POST requests
3. Verify the webhook URL in Flutterwave dashboard

## 📊 Testing Checklist

- [ ] Environment variables configured
- [ ] Flutterwave test account set up
- [ ] Webhook URL configured in Flutterwave dashboard
- [ ] Test subscription creation
- [ ] Test webhook processing
- [ ] Test payment success flow
- [ ] Test payment failure flow
- [ ] Test subscription cancellation
- [ ] Test subscription upgrade
- [ ] Verify database updates
- [ ] Check error handling

## 🔗 Useful Links

- [Flutterwave API Documentation](https://developer.flutterwave.com/docs)
- [Flutterwave Test Cards](https://developer.flutterwave.com/docs/test-cards)
- [Webhook Testing Guide](https://developer.flutterwave.com/docs/webhooks) 