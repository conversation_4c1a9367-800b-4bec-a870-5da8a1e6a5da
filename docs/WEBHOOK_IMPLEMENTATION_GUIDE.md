# Webhook Implementation Guide

## Overview

This guide covers the implementation of real Stripe and Flutterwave webhook processing for the MyProfile subscription system. The webhooks handle payment events, subscription lifecycle management, and ensure data consistency between payment providers and our database.

## 🔧 Prerequisites

### Environment Variables

Add these environment variables to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_... # Your Stripe secret key
STRIPE_WEBHOOK_SECRET=whsec_... # Your Stripe webhook secret

# Flutterwave Configuration
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST_... # Your Flutterwave secret key
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST_... # Your Flutterwave public key
FLUTTERWAVE_SECRET_HASH=your_secret_hash # Your Flutterwave secret hash
```

### Dependencies

```bash
npm install stripe @types/stripe
```

## 🏗️ Architecture

### Payment Provider Interface

```typescript
interface PaymentProvider {
  createSubscription(userId: string, planId: string, billingCycle: 'monthly' | 'yearly'): Promise<{
    subscriptionId: string;
    paymentMethodId?: string;
    status: string;
  }>;
  
  cancelSubscription(subscriptionId: string): Promise<boolean>;
  
  updateSubscription(subscriptionId: string, planId: string): Promise<boolean>;
  
  validateWebhook(payload: any, signature: string): Promise<boolean>;
}
```

### Webhook Processing Flow

1. **Webhook Reception** → `POST /api/subscriptions/webhook/:provider`
2. **Signature Validation** → Verify webhook authenticity
3. **Event Processing** → Handle specific event types
4. **Database Update** → Update user subscription status
5. **Logging** → Record all webhook activities

## 💳 Stripe Implementation

### Supported Events

| Event Type | Description | Handler Method |
|------------|-------------|----------------|
| `invoice.payment_succeeded` | Payment completed successfully | `handleStripePaymentSucceeded` |
| `invoice.payment_failed` | Payment failed | `handleStripePaymentFailed` |
| `customer.subscription.created` | New subscription created | `handleStripeSubscriptionCreated` |
| `customer.subscription.updated` | Subscription updated | `handleStripeSubscriptionUpdated` |
| `customer.subscription.deleted` | Subscription cancelled | `handleStripeSubscriptionDeleted` |
| `customer.subscription.trial_will_end` | Trial ending soon | `handleStripeTrialWillEnd` |

### Stripe Webhook Payload Example

```json
{
  "type": "invoice.payment_succeeded",
  "data": {
    "object": {
      "id": "in_1234567890",
      "subscription": "sub_1234567890",
      "status": "paid",
      "amount_paid": 499,
      "currency": "usd",
      "next_payment_attempt": 1640995200,
      "customer": "cus_1234567890",
      "metadata": {
        "userId": "60d5ecb54b24c0001f5f3e8c",
        "planId": "60d5ecb54b24c0001f5f3e8a"
      }
    }
  }
}
```

### Stripe Signature Validation

```typescript
async validateWebhook(payload: any, signature: string): Promise<boolean> {
  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
    const event = this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);
    return !!event;
  } catch (error) {
    logger.error('Error validating Stripe webhook signature:', error);
    return false;
  }
}
```

## 🌊 Flutterwave Implementation

### Supported Events

| Event Type | Description | Handler Method |
|------------|-------------|----------------|
| `charge.completed` | Payment completed successfully | `handleFlutterwaveChargeCompleted` |
| `subscription.activated` | Subscription activated | `handleFlutterwaveSubscriptionActivated` |
| `subscription.cancelled` | Subscription cancelled | `handleFlutterwaveSubscriptionCancelled` |
| `subscription.expired` | Subscription expired | `handleFlutterwaveSubscriptionExpired` |
| `subscription.updated` | Subscription updated | `handleFlutterwaveSubscriptionUpdated` |

### Flutterwave Webhook Payload Example

```json
{
  "event": "charge.completed",
  "data": {
    "id": "1234567890",
    "tx_ref": "sub_1234567890",
    "status": "successful",
    "amount": 499,
    "currency": "USD",
    "customer": {
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "meta": {
      "userId": "60d5ecb54b24c0001f5f3e8c",
      "planId": "60d5ecb54b24c0001f5f3e8a"
    }
  }
}
```

### Flutterwave Signature Validation

```typescript
async validateWebhook(payload: any, signature: string): Promise<boolean> {
  try {
    const secretHash = process.env.FLUTTERWAVE_SECRET_HASH!;
    const hash = crypto
      .createHmac('sha512', secretHash)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return hash === signature;
  } catch (error) {
    logger.error('Error validating Flutterwave webhook signature:', error);
    return false;
  }
}
```

## 🔄 Database Updates

### UserPlan Model Updates

The webhook handlers update the following fields in the UserPlan model:

- `paymentStatus`: 'active' | 'past_due' | 'canceled' | 'expired'
- `lastPaymentDate`: Date of last successful payment
- `nextBillingDate`: Date of next billing cycle
- `isActive`: Boolean indicating if subscription is active
- `autoRenew`: Boolean indicating if subscription auto-renews

### User Model Updates

The User model includes payment provider customer IDs:

- `stripeCustomerId`: Stripe customer ID
- `flutterwaveCustomerId`: Flutterwave customer ID

## 🧪 Testing

### Test Webhook Endpoint

For development and testing, use the test webhook endpoint:

```bash
curl -X POST http://localhost:3000/api/subscriptions/test-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "subscriptionId": "test_sub_1234567890",
    "status": "active",
    "userId": "60d5ecb54b24c0001f5f3e8c"
  }'
```

### Comprehensive Test Script

Run the comprehensive test script to verify all webhook implementations:

```bash
node test-webhook-implementation.js
```

This script tests:
- ✅ Stripe webhook events
- ✅ Flutterwave webhook events
- ✅ Subscription creation
- ✅ Payment processing
- ✅ Error handling

## 🔐 Security Considerations

### Webhook Security

1. **Signature Validation**: Always validate webhook signatures
2. **HTTPS Only**: Use HTTPS in production
3. **Rate Limiting**: Implement rate limiting on webhook endpoints
4. **Idempotency**: Handle duplicate webhook events gracefully
5. **Logging**: Log all webhook activities for audit trails

### Environment Security

1. **Secret Management**: Store secrets securely (use environment variables)
2. **Access Control**: Restrict webhook endpoint access
3. **Monitoring**: Monitor webhook failures and retries
4. **Backup**: Implement backup webhook endpoints

## 📊 Monitoring and Logging

### Webhook Logging

All webhook activities are logged with structured data:

```typescript
logger.info('Processing Stripe webhook:', event.type);
logger.info(`Updated user plan ${userPlan._id} payment status to active`);
logger.error('Error processing webhook:', error);
```

### Key Metrics to Monitor

- Webhook success/failure rates
- Payment processing times
- Subscription status changes
- Error rates by provider
- Database update success rates

## 🚀 Deployment Checklist

### Pre-Deployment

- [ ] Set up environment variables
- [ ] Configure webhook endpoints in Stripe/Flutterwave dashboards
- [ ] Test webhook signatures
- [ ] Verify database schema updates
- [ ] Run comprehensive tests

### Production Deployment

- [ ] Use production API keys
- [ ] Configure production webhook URLs
- [ ] Set up monitoring and alerting
- [ ] Test webhook endpoints with real data
- [ ] Document webhook URLs for support

### Post-Deployment

- [ ] Monitor webhook processing
- [ ] Verify payment processing
- [ ] Check subscription status updates
- [ ] Monitor error logs
- [ ] Test webhook retry mechanisms

## 🔧 Troubleshooting

### Common Issues

#### 1. Webhook Signature Validation Fails

**Symptoms**: 400 Bad Request errors
**Solutions**:
- Verify webhook secret is correct
- Check signature header format
- Ensure payload hasn't been modified

#### 2. Database Updates Fail

**Symptoms**: Webhook succeeds but data doesn't update
**Solutions**:
- Check database connection
- Verify UserPlan model exists
- Check for database constraints

#### 3. Payment Status Not Updating

**Symptoms**: Payments succeed but status remains unchanged
**Solutions**:
- Verify subscription ID mapping
- Check webhook event type handling
- Review database update logic

### Debug Commands

```bash
# Check webhook processing logs
tail -f logs/app.log | grep webhook

# Test webhook endpoint
curl -X POST http://localhost:3000/api/subscriptions/webhook/test \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Verify environment variables
echo $STRIPE_SECRET_KEY
echo $FLUTTERWAVE_SECRET_KEY
```

## 📚 API Reference

### Webhook Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/subscriptions/webhook/stripe` | POST | Process Stripe webhooks |
| `/api/subscriptions/webhook/flutterwave` | POST | Process Flutterwave webhooks |
| `/api/subscriptions/webhook/test` | POST | Test webhook endpoint |

### Response Format

```json
{
  "received": true
}
```

### Error Response

```json
{
  "success": false,
  "message": "Invalid webhook signature",
  "error": "Bad Request"
}
```

## 🔄 Webhook Event Flow

```mermaid
graph TD
    A[Payment Provider] --> B[Webhook Endpoint]
    B --> C[Signature Validation]
    C --> D{Valid?}
    D -->|Yes| E[Event Processing]
    D -->|No| F[Return Error]
    E --> G[Database Update]
    G --> H[Log Activity]
    H --> I[Return Success]
```

## 📞 Support

For webhook-related issues:

1. **Check logs** for detailed error messages
2. **Verify webhook configuration** in payment provider dashboards
3. **Test with webhook testing tools** provided by payment providers
4. **Contact support** with webhook payload and error details

## 🎯 Best Practices

1. **Always validate webhook signatures**
2. **Handle webhook events idempotently**
3. **Log all webhook activities**
4. **Implement proper error handling**
5. **Use environment-specific configurations**
6. **Monitor webhook processing performance**
7. **Test webhook endpoints thoroughly**
8. **Document webhook event handling**
9. **Implement webhook retry mechanisms**
10. **Keep webhook secrets secure**

---

*This guide covers the complete webhook implementation for Stripe and Flutterwave payment providers. For additional support, refer to the payment provider documentation or contact the development team.*