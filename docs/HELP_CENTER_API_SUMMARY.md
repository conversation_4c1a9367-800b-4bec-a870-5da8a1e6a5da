# Help Center API Documentation

## Overview

The Help Center API provides a comprehensive content management system for creating, managing, and serving help documentation. It includes both public endpoints for content consumption and admin endpoints for content management, with features like caching, search, analytics, and localization support.

## Base URL

```
https://api.getmyprofile.online/api/help
```

## Authentication

- **Public endpoints**: No authentication required
- **Admin endpoints**: JWT authentication with admin role required

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Data Models

### Help Topic

```typescript
interface IHelpTopic {
  _id: string;
  title: string;
  description?: string;
  slug: string;
  icon?: string;
  color?: string;
  order: number;
  isActive: boolean;
  parentId?: string;
  language: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Help Article

```typescript
interface IHelpArticle {
  _id: string;
  title: string;
  content: string;
  slug: string;
  summary?: string;
  keywords?: string[];
  metaDescription?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  readingTime: number;
  status: 'draft' | 'published' | 'archived';
  topics: string[];
  language: string;
  author: string;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Help Article Analytics

```typescript
interface IHelpArticleAnalytics {
  _id: string;
  articleId: string;
  views: number;
  searches: number;
  clicks: number;
  helpfulVotes: number;
  notHelpfulVotes: number;
  averageRating: number;
  lastViewed: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Help Feedback

```typescript
interface IHelpFeedback {
  _id: string;
  articleId: string;
  userId?: string;
  rating: number;
  comment?: string;
  isHelpful: boolean;
  createdAt: Date;
}
```

## Public API Endpoints

### 1. Get Topics

**GET** `/api/help/topics`

Retrieves the hierarchical topic structure.

#### Query Parameters

- `language` (optional): Language code (default: 'en')

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "title": "Getting Started",
      "description": "Learn the basics of MyProfile",
      "slug": "getting-started",
      "icon": "home",
      "color": "#3B82F6",
      "order": 1,
      "isActive": true,
      "parentId": null,
      "language": "en",
      "children": [
        {
          "_id": "507f1f77bcf86cd799439012",
          "title": "Account Setup",
          "description": "How to set up your account",
          "slug": "account-setup",
          "icon": "user",
          "color": "#10B981",
          "order": 1,
          "isActive": true,
          "parentId": "507f1f77bcf86cd799439011",
          "language": "en"
        }
      ]
    }
  ],
  "cache": {
    "hit": true,
    "ttl": 3600
  }
}
```

### 2. Get Articles

**GET** `/api/help/articles`

Retrieves articles with filtering and pagination.

#### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `topic` (optional): Filter by topic slug
- `difficulty` (optional): Filter by difficulty level
- `status` (optional): Filter by status (default: 'published')
- `language` (optional): Language code (default: 'en')
- `sortBy` (optional): Sort field ('title', 'createdAt', 'updatedAt', 'views')
- `sortOrder` (optional): Sort order ('asc', 'desc')

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "title": "How to Create Your Profile",
      "slug": "how-to-create-your-profile",
      "summary": "Learn how to set up your profile step by step",
      "difficulty": "beginner",
      "readingTime": 5,
      "topics": ["getting-started", "account-setup"],
      "language": "en",
      "author": "John Doe",
      "createdAt": "2025-01-15T10:30:00.000Z",
      "updatedAt": "2025-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### 3. Get Article by Slug

**GET** `/api/help/articles/:slug`

Retrieves a specific article by its slug.

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "title": "How to Create Your Profile",
    "content": "<h1>Creating Your Profile</h1><p>Follow these steps...</p>",
    "slug": "how-to-create-your-profile",
    "summary": "Learn how to set up your profile step by step",
    "keywords": ["profile", "setup", "account"],
    "metaDescription": "Complete guide to creating your MyProfile account",
    "difficulty": "beginner",
    "readingTime": 5,
    "status": "published",
    "topics": ["getting-started", "account-setup"],
    "language": "en",
    "author": "John Doe",
    "version": 1,
    "createdAt": "2025-01-15T10:30:00.000Z",
    "updatedAt": "2025-01-15T10:30:00.000Z",
    "analytics": {
      "views": 150,
      "averageRating": 4.5,
      "helpfulVotes": 12,
      "notHelpfulVotes": 2
    },
    "relatedArticles": [
      {
        "_id": "507f1f77bcf86cd799439012",
        "title": "Profile Customization",
        "slug": "profile-customization",
        "summary": "Customize your profile appearance"
      }
    ]
  }
}
```

### 4. Search Articles

**GET** `/api/help/search`

Searches articles using full-text search.

#### Query Parameters

- `q` (required): Search query
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `language` (optional): Language code (default: 'en')
- `topic` (optional): Filter by topic slug
- `difficulty` (optional): Filter by difficulty level

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "title": "How to Create Your Profile",
      "slug": "how-to-create-your-profile",
      "summary": "Learn how to set up your profile step by step",
      "difficulty": "beginner",
      "readingTime": 5,
      "topics": ["getting-started", "account-setup"],
      "relevanceScore": 0.95,
      "highlights": [
        "Create your <mark>profile</mark> in just a few steps",
        "Set up your <mark>profile</mark> information"
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5,
    "totalPages": 1
  },
  "searchAnalytics": {
    "query": "profile setup",
    "resultsCount": 5,
    "searchTime": 0.15
  }
}
```

### 5. Submit Article Feedback

**POST** `/api/help/articles/:id/feedback`

Submits feedback for an article.

#### Request Body

```json
{
  "rating": 5,
  "comment": "This article was very helpful!",
  "isHelpful": true
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "message": "Feedback submitted successfully",
    "feedbackId": "507f1f77bcf86cd799439011"
  }
}
```

### 6. Track Search Click

**POST** `/api/help/search/:id/click`

Tracks when a user clicks on a search result.

#### Response

```json
{
  "success": true,
  "data": {
    "message": "Click tracked successfully"
  }
}
```

## Admin API Endpoints

### Topic Management

#### Create Topic

**POST** `/api/admin/help/topics`

Creates a new help topic.

#### Request Body

```json
{
  "title": "Advanced Features",
  "description": "Learn about advanced MyProfile features",
  "slug": "advanced-features",
  "icon": "star",
  "color": "#F59E0B",
  "order": 3,
  "isActive": true,
  "parentId": null,
  "language": "en"
}
```

#### Update Topic

**PUT** `/api/admin/help/topics/:id`

Updates an existing topic.

#### Delete Topic

**DELETE** `/api/admin/help/topics/:id`

Deletes a topic and its children.

### Article Management

#### Create Article

**POST** `/api/admin/help/articles`

Creates a new help article.

#### Request Body

```json
{
  "title": "Advanced Profile Settings",
  "content": "<h1>Advanced Settings</h1><p>Learn about advanced options...</p>",
  "slug": "advanced-profile-settings",
  "summary": "Configure advanced profile settings",
  "keywords": ["advanced", "settings", "profile"],
  "metaDescription": "Complete guide to advanced profile settings",
  "difficulty": "advanced",
  "readingTime": 10,
  "status": "draft",
  "topics": ["advanced-features"],
  "language": "en"
}
```

#### Update Article

**PUT** `/api/admin/help/articles/:id`

Updates an existing article.

#### Update Article Status

**PUT** `/api/admin/help/articles/:id/status`

Updates the status of an article.

#### Request Body

```json
{
  "status": "published"
}
```

#### Delete Article

**DELETE** `/api/admin/help/articles/:id`

Deletes an article.

### Analytics & Reporting

#### Get Analytics

**GET** `/api/admin/help/analytics`

Retrieves comprehensive analytics data.

#### Query Parameters

- `startDate` (optional): Start date for analytics
- `endDate` (optional): End date for analytics
- `groupBy` (optional): Group by 'day', 'week', 'month'

#### Response

```json
{
  "success": true,
  "data": {
    "overview": {
      "totalArticles": 150,
      "totalViews": 25000,
      "totalSearches": 5000,
      "averageRating": 4.2
    },
    "topArticles": [
      {
        "title": "How to Create Your Profile",
        "views": 1500,
        "rating": 4.5,
        "helpfulVotes": 120
      }
    ],
    "searchAnalytics": {
      "totalSearches": 5000,
      "uniqueQueries": 800,
      "topQueries": [
        { "query": "profile setup", "count": 150 },
        { "query": "account creation", "count": 120 }
      ]
    },
    "userEngagement": {
      "averageSessionTime": 180,
      "bounceRate": 0.25,
      "returnVisitors": 0.4
    }
  }
}
```

#### Get Feedback

**GET** `/api/admin/help/feedback`

Retrieves article feedback.

#### Query Parameters

- `articleId` (optional): Filter by article ID
- `rating` (optional): Filter by rating
- `page` (optional): Page number
- `limit` (optional): Items per page

#### Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "articleId": "507f1f77bcf86cd799439012",
      "articleTitle": "How to Create Your Profile",
      "rating": 5,
      "comment": "Very helpful article!",
      "isHelpful": true,
      "createdAt": "2025-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### Bulk Operations

#### Bulk Update Articles

**POST** `/api/admin/help/articles/bulk-update`

Updates multiple articles at once.

#### Request Body

```json
{
  "articleIds": ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"],
  "updates": {
    "status": "published",
    "topics": ["getting-started"]
  }
}
```

### Cache Management

#### Clear Cache

**DELETE** `/api/admin/help/cache`

Clears the help center cache.

#### Response

```json
{
  "success": true,
  "data": {
    "message": "Cache cleared successfully",
    "clearedKeys": 15
  }
}
```

## Error Responses

### 400 Bad Request

```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    {
      "field": "title",
      "message": "Title is required"
    }
  ]
}
```

### 401 Unauthorized

```json
{
  "success": false,
  "message": "Authentication required"
}
```

### 403 Forbidden

```json
{
  "success": false,
  "message": "Admin access required"
}
```

### 404 Not Found

```json
{
  "success": false,
  "message": "Article not found"
}
```

### 429 Too Many Requests

```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "retryAfter": 60
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Rate Limiting

- **Search**: 10 requests per minute per IP
- **Feedback**: 5 submissions per hour per IP
- **Admin endpoints**: 100 requests per minute per user

## Caching

- **Topics**: 1 hour TTL
- **Articles**: 30 minutes TTL
- **Search results**: 15 minutes TTL
- Cache is automatically invalidated when content is updated

## Usage Examples

### JavaScript

```javascript
const axios = require('axios');

// Get topics
const getTopics = async () => {
  try {
    const response = await axios.get('https://api.getmyprofile.online/api/help/topics?language=en');
    console.log('Topics:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Search articles
const searchArticles = async (query) => {
  try {
    const response = await axios.get(`https://api.getmyprofile.online/api/help/search?q=${encodeURIComponent(query)}`);
    console.log('Search results:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Submit feedback (authenticated)
const submitFeedback = async (articleId, feedback) => {
  try {
    const response = await axios.post(
      `https://api.getmyprofile.online/api/help/articles/${articleId}/feedback`,
      feedback,
      {
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        }
      }
    );
    console.log('Feedback submitted:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};

// Admin: Create article
const createArticle = async (articleData) => {
  try {
    const response = await axios.post(
      'https://api.getmyprofile.online/api/admin/help/articles',
      articleData,
      {
        headers: {
          'Authorization': 'Bearer YOUR_ADMIN_JWT_TOKEN'
        }
      }
    );
    console.log('Article created:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data);
  }
};
```

### cURL Examples

#### Get Topics

```bash
curl -X GET "https://api.getmyprofile.online/api/help/topics?language=en"
```

#### Search Articles

```bash
curl -X GET "https://api.getmyprofile.online/api/help/search?q=profile%20setup&page=1&limit=10"
```

#### Get Article

```bash
curl -X GET "https://api.getmyprofile.online/api/help/articles/how-to-create-your-profile"
```

#### Submit Feedback

```bash
curl -X POST "https://api.getmyprofile.online/api/help/articles/507f1f77bcf86cd799439011/feedback" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "rating": 5,
    "comment": "Very helpful article!",
    "isHelpful": true
  }'
```

#### Admin: Create Topic

```bash
curl -X POST "https://api.getmyprofile.online/api/admin/help/topics" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -d '{
    "title": "Advanced Features",
    "description": "Learn about advanced features",
    "slug": "advanced-features",
    "icon": "star",
    "color": "#F59E0B",
    "order": 3,
    "isActive": true,
    "language": "en"
  }'
```

## Notes

- All public endpoints support caching for improved performance
- Search functionality includes relevance scoring and analytics tracking
- Admin endpoints require appropriate role permissions
- Content supports multiple languages with fallback to default
- Analytics provide insights into user engagement and content performance
- Rate limiting helps prevent abuse while maintaining service quality 