{"info": {"name": "Exchange API Collection", "description": "Complete Postman collection for testing the Exchange API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "https://api.getmyprofile.online", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "exchange_id", "value": "EXCH8734KXA", "type": "string"}, {"key": "sender_profile_id", "value": "A01V1B2T", "type": "string"}, {"key": "receiver_profile_id", "value": "B84ZK3X9", "type": "string"}], "item": [{"name": "1. Initiate Exchange", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"senderProfileId\": \"{{sender_profile_id}}\",\n  \"receiverProfileId\": \"{{receiver_profile_id}}\",\n  \"profileName\": \"<PERSON><PERSON> Alain\",\n  \"exchangeReason\": \"Met at BioTech 2025\",\n  \"channel\": \"in_app\",\n  \"timestamp\": \"2025-07-31T19:10:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/initiate", "host": ["{{base_url}}"], "path": ["api", "exchange", "initiate"]}, "description": "Initiate a profile exchange with another user"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"senderProfileId\": \"A01V1B2T\",\n  \"receiverProfileId\": \"B84ZK3X9\",\n  \"profileName\": \"Be<PERSON> Alain\",\n  \"exchangeReason\": \"Met at BioTech 2025\",\n  \"channel\": \"in_app\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/initiate", "host": ["{{base_url}}"], "path": ["api", "exchange", "initiate"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Exchange initiated successfully\",\n  \"exchangeId\": \"EXCH8734KXA\",\n  \"myPtsAwarded\": 5\n}"}]}, {"name": "2. Get Exchange History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exchange/history?profileId={{sender_profile_id}}&status=pending&limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "exchange", "history"], "query": [{"key": "profileId", "value": "{{sender_profile_id}}"}, {"key": "status", "value": "pending"}, {"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}, "description": "Fetch all profile exchange history for a profile with pagination"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exchange/history?profileId=A01V1B2T", "host": ["{{base_url}}"], "path": ["api", "exchange", "history"], "query": [{"key": "profileId", "value": "A01V1B2T"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"exchanges\": [\n    {\n      \"exchangeId\": \"EXCH8734KXA\",\n      \"senderProfileId\": \"A01V1B2T\",\n      \"receiverProfileId\": \"B84ZK3X9\",\n      \"profileName\": \"<PERSON><PERSON>\",\n      \"exchangeReason\": \"Met at BioTech 2025\",\n      \"channel\": \"in_app\",\n      \"timestamp\": \"2025-07-31T19:10:00Z\",\n      \"status\": \"pending\",\n      \"isSender\": true,\n      \"otherProfile\": {\n        \"id\": \"B84ZK3X9\",\n        \"secondaryId\": \"B84ZK3X9\",\n        \"name\": \"<PERSON>\",\n        \"username\": \"johndo<PERSON>\"\n      },\n      \"myPtsAwarded\": 5\n    }\n  ],\n  \"pagination\": {\n    \"total\": 25,\n    \"limit\": 10,\n    \"offset\": 0,\n    \"hasMore\": true\n  }\n}"}]}, {"name": "3. Get Exchange Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exchange/{{exchange_id}}", "host": ["{{base_url}}"], "path": ["api", "exchange", "{{exchange_id}}"]}, "description": "Get detailed information for a specific exchange"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/exchange/EXCH8734KXA", "host": ["{{base_url}}"], "path": ["api", "exchange", "EXCH8734KXA"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"exchange\": {\n    \"exchangeId\": \"EXCH8734KXA\",\n    \"senderProfileId\": \"A01V1B2T\",\n    \"receiverProfileId\": \"B84ZK3X9\",\n    \"profileName\": \"Be<PERSON> Alain\",\n    \"exchangeReason\": \"Met at BioTech 2025\",\n    \"channel\": \"qr\",\n    \"timestamp\": \"2025-07-31T19:10:00Z\",\n    \"status\": \"pending\",\n    \"exchangeData\": {\n      \"senderProfile\": {\n        \"id\": \"A01V1B2T\",\n        \"secondaryId\": \"A01V1B2T\",\n        \"name\": \"Be<PERSON> <PERSON>\",\n        \"username\": \"bezing\",\n        \"title\": \"Software Engineer\"\n      },\n      \"receiverProfile\": {\n        \"id\": \"B84ZK3X9\",\n        \"secondaryId\": \"B84ZK3X9\",\n        \"name\": \"<PERSON>\",\n        \"username\": \"johndoe\",\n        \"title\": \"Product Manager\"\n      },\n      \"sharedSections\": [\"contact\", \"social\"],\n      \"customMessage\": \"Great meeting you at the conference!\"\n    },\n    \"analytics\": {\n      \"viewed\": true,\n      \"viewedAt\": \"2025-07-31T19:15:00Z\",\n      \"responded\": false,\n      \"myPtsAwarded\": 5,\n      \"engagementScore\": 75\n    },\n    \"metadata\": {\n      \"deviceInfo\": {\n        \"userAgent\": \"Mozilla/5.0...\",\n        \"ipAddress\": \"***********\",\n        \"deviceType\": \"mobile\"\n      },\n      \"location\": {\n        \"city\": \"San Francisco\",\n        \"country\": \"US\"\n      },\n      \"tags\": [\"conference\", \"networking\", \"business\"]\n    },\n    \"expiresAt\": \"2025-08-30T19:10:00Z\",\n    \"completedAt\": null\n  }\n}"}]}, {"name": "4. Complete Exchange", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"{{receiver_profile_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/{{exchange_id}}/complete", "host": ["{{base_url}}"], "path": ["api", "exchange", "{{exchange_id}}", "complete"]}, "description": "Accept and complete an exchange"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"B84ZK3X9\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/EXCH8734KXA/complete", "host": ["{{base_url}}"], "path": ["api", "exchange", "EXCH8734KXA", "complete"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Exchange completed successfully\",\n  \"myPtsAwarded\": 10\n}"}]}, {"name": "5. Decline Exchange", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"{{receiver_profile_id}}\",\n  \"reason\": \"Not interested at this time\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/{{exchange_id}}/decline", "host": ["{{base_url}}"], "path": ["api", "exchange", "{{exchange_id}}", "decline"]}, "description": "Decline an exchange request"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"B84ZK3X9\",\n  \"reason\": \"Not interested at this time\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/EXCH8734KXA/decline", "host": ["{{base_url}}"], "path": ["api", "exchange", "EXCH8734KXA", "decline"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Exchange declined successfully\"\n}"}]}, {"name": "6. Cancel Exchange", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"{{sender_profile_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/{{exchange_id}}", "host": ["{{base_url}}"], "path": ["api", "exchange", "{{exchange_id}}"]}, "description": "Cancel/retract an exchange (only sender can cancel)"}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"profileId\": \"A01V1B2T\"\n}"}, "url": {"raw": "{{base_url}}/api/exchange/EXCH8734KXA", "host": ["{{base_url}}"], "path": ["api", "exchange", "EXCH8734KXA"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Exchange cancelled successfully\"\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set up environment variables if needed", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'https://api.getmyprofile.online');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test script for all requests", "pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has success property', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Store exchange ID for subsequent requests", "if (pm.response.json().exchangeId) {", "    pm.environment.set('exchange_id', pm.response.json().exchangeId);", "}"]}}]}