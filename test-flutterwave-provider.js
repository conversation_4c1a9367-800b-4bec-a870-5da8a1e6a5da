const mongoose = require('mongoose');
require('dotenv').config();

// Mock the logger
const logger = {
  info: console.log,
  error: console.error,
  warn: console.warn
};

// Mock the User and Plan models
const mockUser = {
  _id: '681d8a68d3bffe4846ffca7f',
  email: '<EMAIL>',
  name: 'Test User'
};

const mockPlan = {
  _id: '688b300120fdcb1fabb5e587',
  name: 'Premium',
  price: 29.99,
  planType: 'premium'
};

// Mock the models
const User = {
  findById: async (id) => mockUser
};

const Plan = {
  findById: async (id) => mockPlan
};

// FlutterwaveProvider class (simplified version)
class FlutterwaveProvider {
  constructor() {
    this.secretKey = process.env.FLUTTERWAVE_SECRET_KEY || 'test_key';
    this.publicKey = process.env.FLUTTERWAVE_PUBLIC_KEY || 'test_public_key';
  }
  
  async createSubscription(userId, planId, billingCycle) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Get plan template
      const planData = await Plan.findById(planId);
      if (!planData) {
        throw new Error('Plan not found');
      }
      
      // For now, use a simplified approach that creates a test subscription
      // This can be enhanced later when Flutterwave API is properly configured
      logger.info('Creating Flutterwave subscription with test approach');
      
      // Simulate a successful payment creation
      const subscriptionId = `flutterwave_sub_${Date.now()}_${userId}`;
      const paymentMethodId = `flutterwave_pm_${Date.now()}`;
      
      return {
        subscriptionId,
        paymentMethodId,
        status: 'pending' // Will be activated via webhook
      };
    } catch (error) {
      logger.error('Error creating Flutterwave subscription:', error);
      throw error;
    }
  }
  
  async cancelSubscription(subscriptionId) {
    try {
      // For Flutterwave, we can't cancel a one-time payment, but we can refund it
      // Check if the payment was successful first
      logger.info('Flutterwave subscription cancellation not supported for one-time payments');
      return true; // Simulate success
    } catch (error) {
      logger.error('Error canceling Flutterwave subscription:', error);
      return false;
    }
  }
  
  async updateSubscription(subscriptionId, planId) {
    try {
      // For Flutterwave, we can't update a one-time payment, but we can create a new payment
      // This would typically involve creating a new payment for the updated plan
      logger.info('Flutterwave subscription update not supported for one-time payments');
      return false;
    } catch (error) {
      logger.error('Error updating Flutterwave subscription:', error);
      return false;
    }
  }
  
  async validateWebhook(payload, signature) {
    try {
      // For testing, always return true
      return true;
    } catch (error) {
      logger.error('Error validating Flutterwave webhook signature:', error);
      return false;
    }
  }
}

async function testFlutterwaveProvider() {
  try {
    console.log('🧪 Testing Flutterwave Provider...\n');
    
    const provider = new FlutterwaveProvider();
    
    // Test subscription creation
    console.log('1. Testing subscription creation...');
    const result = await provider.createSubscription(
      '681d8a68d3bffe4846ffca7f',
      '688b300120fdcb1fabb5e587',
      'monthly'
    );
    
    console.log('✅ Subscription created successfully:');
    console.log('Subscription ID:', result.subscriptionId);
    console.log('Payment Method ID:', result.paymentMethodId);
    console.log('Status:', result.status);
    
    // Test subscription cancellation
    console.log('\n2. Testing subscription cancellation...');
    const cancelResult = await provider.cancelSubscription(result.subscriptionId);
    console.log('✅ Cancellation result:', cancelResult);
    
    // Test subscription update
    console.log('\n3. Testing subscription update...');
    const updateResult = await provider.updateSubscription(result.subscriptionId, '688b300120fdcb1fabb5e587');
    console.log('✅ Update result:', updateResult);
    
    // Test webhook validation
    console.log('\n4. Testing webhook validation...');
    const webhookPayload = { event: 'charge.completed', data: { id: 'test' } };
    const validationResult = await provider.validateWebhook(webhookPayload, 'test_signature');
    console.log('✅ Webhook validation result:', validationResult);
    
    console.log('\n🎉 All Flutterwave provider tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFlutterwaveProvider(); 