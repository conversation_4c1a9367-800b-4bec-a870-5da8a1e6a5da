const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const TEST_TOKEN = 'your-test-jwt-token'; // Replace with actual test token

// Headers
const headers = {
  'Authorization': `Bearer ${TEST_TOKEN}`,
  'Content-Type': 'application/json'
};

console.log('🧪 Testing Transfer Logs API with Icons\n');

// Test 1: Get All Transfer Logs
async function testGetAllTransferLogs() {
  console.log('1️⃣ Testing Get All Transfer Logs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs`, {
      headers
    });
    
    console.log('✅ Get All Transfer Logs:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get All Transfer Logs Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 2: Get Account Transfer Logs Only
async function testGetAccountTransferLogs() {
  console.log('\n2️⃣ Testing Get Account Transfer Logs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs?type=account`, {
      headers
    });
    
    console.log('✅ Get Account Transfer Logs:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get Account Transfer Logs Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 3: Get Profile Transfer Logs Only
async function testGetProfileTransferLogs() {
  console.log('\n3️⃣ Testing Get Profile Transfer Logs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs?type=profile`, {
      headers
    });
    
    console.log('✅ Get Profile Transfer Logs:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get Profile Transfer Logs Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 4: Get Transfer Logs with Pagination
async function testGetTransferLogsWithPagination() {
  console.log('\n4️⃣ Testing Get Transfer Logs with Pagination...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs?page=1&limit=5`, {
      headers
    });
    
    console.log('✅ Get Transfer Logs with Pagination:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get Transfer Logs with Pagination Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 5: Transfer Account Ownership
async function testTransferAccountOwnership() {
  console.log('\n5️⃣ Testing Transfer Account Ownership...');
  
  const transferData = {
    toUserId: '60d5ecb54b24c0001f5f3e8d',
    reason: 'Transfer to family member'
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/accounts/test-account-id/transfer`, transferData, {
      headers
    });
    
    console.log('✅ Transfer Account Ownership:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Transfer Account Ownership Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 6: Transfer Profile
async function testTransferProfile() {
  console.log('\n6️⃣ Testing Transfer Profile...');
  
  const transferData = {
    toUserId: '60d5ecb54b24c0001f5f3e8d',
    reason: 'Transfer to colleague'
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/profiles/p/test-profile-id/transfer`, transferData, {
      headers
    });
    
    console.log('✅ Transfer Profile:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Transfer Profile Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 7: Claim Profile
async function testClaimProfile() {
  console.log('\n7️⃣ Testing Claim Profile...');
  
  try {
    const response = await axios.post(`${BASE_URL}/profiles/p/test-profile-id/claim`, {}, {
      headers
    });
    
    console.log('✅ Claim Profile:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Claim Profile Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 8: Verify Icon Legend
async function testIconLegend() {
  console.log('\n8️⃣ Testing Icon Legend Verification...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs`, {
      headers
    });
    
    const logs = response.data.data.logs;
    const iconLegend = {
      '👤✅': 'Profile claimed by a user',
      '🔁👤': 'Profile moved to another account',
      '🔁💼': 'Account transferred',
      '🔄🔐': 'Account ownership changed'
    };
    
    console.log('📋 Icon Legend:');
    Object.entries(iconLegend).forEach(([icon, description]) => {
      console.log(`   ${icon} - ${description}`);
    });
    
    console.log('\n📊 Found Icons in Logs:');
    const foundIcons = [...new Set(logs.map(log => log.icon))];
    foundIcons.forEach(icon => {
      console.log(`   ${icon} - ${iconLegend[icon] || 'Unknown action'}`);
    });
    
    return true;
  } catch (error) {
    console.log('❌ Icon Legend Verification Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 9: Test Response Format
async function testResponseFormat() {
  console.log('\n9️⃣ Testing Response Format...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs?type=profile`, {
      headers
    });
    
    const data = response.data;
    
    // Verify response structure
    const hasStatus = data.hasOwnProperty('status');
    const hasData = data.hasOwnProperty('data');
    const hasLogs = data.data.hasOwnProperty('logs');
    const hasTotal = data.data.hasOwnProperty('total');
    const hasPage = data.data.hasOwnProperty('page');
    const hasLimit = data.data.hasOwnProperty('limit');
    
    console.log('✅ Response Structure Check:');
    console.log(`   status: ${hasStatus ? '✅' : '❌'}`);
    console.log(`   data: ${hasData ? '✅' : '❌'}`);
    console.log(`   logs: ${hasLogs ? '✅' : '❌'}`);
    console.log(`   total: ${hasTotal ? '✅' : '❌'}`);
    console.log(`   page: ${hasPage ? '✅' : '❌'}`);
    console.log(`   limit: ${hasLimit ? '✅' : '❌'}`);
    
    // Verify log entry structure
    if (data.data.logs && data.data.logs.length > 0) {
      const log = data.data.logs[0];
      const hasType = log.hasOwnProperty('type');
      const hasUser = log.hasOwnProperty('user');
      const hasAction = log.hasOwnProperty('action');
      const hasIcon = log.hasOwnProperty('icon');
      const hasDate = log.hasOwnProperty('date');
      const hasMyPts = log.hasOwnProperty('myPts');
      
      console.log('\n✅ Log Entry Structure Check:');
      console.log(`   type: ${hasType ? '✅' : '❌'}`);
      console.log(`   user: ${hasUser ? '✅' : '❌'}`);
      console.log(`   action: ${hasAction ? '✅' : '❌'}`);
      console.log(`   icon: ${hasIcon ? '✅' : '❌'}`);
      console.log(`   date: ${hasDate ? '✅' : '❌'}`);
      console.log(`   myPts: ${hasMyPts ? '✅' : '❌'}`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Response Format Test Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 10: Test Transfer Statistics
async function testTransferStats() {
  console.log('\n🔟 Testing Transfer Statistics...');
  
  try {
    const response = await axios.get(`${BASE_URL}/accounts/transfer/stats`, {
      headers
    });
    
    console.log('✅ Transfer Statistics:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Transfer Statistics Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 11: Test Error Handling
async function testErrorHandling() {
  console.log('\n1️⃣1️⃣ Testing Error Handling...');
  
  try {
    // Test with invalid type
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs?type=invalid`, {
      headers
    });
    
    console.log('✅ Invalid Type Handling:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Error Handling Test:', error.response?.data || error.message);
    return false;
  }
}

// Test 12: Test Database Integration
async function testDatabaseIntegration() {
  console.log('\n1️⃣2️⃣ Testing Database Integration...');
  
  try {
    // Test that we get real data from database
    const response = await axios.get(`${BASE_URL}/accounts/transfer/logs`, {
      headers
    });
    
    const logs = response.data.data.logs;
    
    if (logs.length > 0) {
      console.log('✅ Database Integration: Found real transfer logs');
      console.log(`   Total logs: ${response.data.data.total}`);
      console.log(`   Sample log:`, logs[0]);
    } else {
      console.log('⚠️  Database Integration: No logs found (may need seeding)');
    }
    
    return true;
  } catch (error) {
    console.log('❌ Database Integration Error:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    testGetAllTransferLogs,
    testGetAccountTransferLogs,
    testGetProfileTransferLogs,
    testGetTransferLogsWithPagination,
    testTransferAccountOwnership,
    testTransferProfile,
    testClaimProfile,
    testIconLegend,
    testResponseFormat,
    testTransferStats,
    testErrorHandling,
    testDatabaseIntegration
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
    } catch (error) {
      console.log('❌ Test failed with error:', error.message);
      results.push(false);
    }
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  const passed = results.filter(r => r).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! Transfer Logs API with icons is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
}

// Environment check
console.log('🔧 Environment Check:');
console.log('====================');
console.log(`Base URL: ${BASE_URL}`);
console.log(`Test Token: ${TEST_TOKEN ? '✅ Set' : '❌ Not set'}`);
console.log('');

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/accounts/transfer/logs`);
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('Please start the server before running tests');
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runAllTests();
  }
}

// Run the tests
main().catch(console.error);