const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile');

// Import User model
const User = require('../src/models/User');

async function getTestUserToken() {
  try {
    console.log('🔍 Finding a test user...\n');

    // Find the first user in the database
    const user = await User.findOne({});
    
    if (!user) {
      console.log('❌ No users found in database');
      console.log('Please create a user first or run the user creation script');
      return;
    }

    console.log('✅ Found user:', {
      id: user._id,
      email: user.email,
      name: user.name || 'N/A'
    });

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id.toString(),
        email: user.email,
        role: user.role || 'user'
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    console.log('\n🎫 Test User Token:');
    console.log('='.repeat(50));
    console.log(token);
    console.log('='.repeat(50));
    
    console.log('\n📋 To use this token in tests:');
    console.log('1. Set environment variable:');
    console.log(`   export TEST_USER_TOKEN="${token}"`);
    console.log('\n2. Or use it directly in your test script');
    console.log('\n3. Run the Flutterwave test:');
    console.log('   node test-flutterwave-subscription.js');

  } catch (error) {
    console.error('❌ Error getting test user token:', error.message);
  } finally {
    mongoose.connection.close();
  }
}

// Run the function
getTestUserToken(); 