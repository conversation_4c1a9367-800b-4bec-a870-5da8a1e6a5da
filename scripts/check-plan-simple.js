const mongoose = require('mongoose');
require('dotenv').config();

async function checkPlan() {
  try {
    console.log('🔍 Checking plan in database...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile');
    console.log('✅ Connected to MongoDB\n');

    const planId = '688b300120fdcb1fabb5e587'; // The plan ID from your request
    
    console.log('Looking for plan ID:', planId);
    
    // Use the plans collection directly
    const db = mongoose.connection.db;
    const plansCollection = db.collection('plans');
    
    const plan = await plansCollection.findOne({ _id: new mongoose.Types.ObjectId(planId) });
    
    if (!plan) {
      console.log('❌ Plan not found in database');
      console.log('Available plans:');
      
      const allPlans = await plansCollection.find({}).toArray();
      allPlans.forEach(p => {
        console.log(`- ID: ${p._id}, Name: ${p.name}, Price: ${p.price}`);
      });
      
      return;
    }

    console.log('✅ Plan found:');
    console.log('ID:', plan._id);
    console.log('Name:', plan.name);
    console.log('Price:', plan.price);
    console.log('Plan Type:', plan.planType);
    console.log('Is Active:', plan.isActive);

  } catch (error) {
    console.error('❌ Error checking plan:', error.message);
  } finally {
    await mongoose.connection.close();
  }
}

checkPlan(); 