const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile');

// Import Plan model
const Plan = require('../src/models/Plan.ts');

async function checkPlan() {
  try {
    console.log('🔍 Checking plan in database...\n');

    const planId = '688b300120fdcb1fabb5e587'; // The plan ID from your request
    
    console.log('Looking for plan ID:', planId);
    
    const plan = await Plan.findById(planId);
    
    if (!plan) {
      console.log('❌ Plan not found in database');
      console.log('Available plans:');
      
      const allPlans = await Plan.find({});
      allPlans.forEach(p => {
        console.log(`- ID: ${p._id}, Name: ${p.name}, Price: ${p.price}`);
      });
      
      return;
    }

    console.log('✅ Plan found:');
    console.log('ID:', plan._id);
    console.log('Name:', plan.name);
    console.log('Price:', plan.price);
    console.log('Plan Type:', plan.planType);
    console.log('Is Active:', plan.isActive);

  } catch (error) {
    console.error('❌ Error checking plan:', error.message);
  } finally {
    mongoose.connection.close();
  }
}

checkPlan(); 