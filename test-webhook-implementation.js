const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const TEST_TOKEN = 'your-test-jwt-token'; // Replace with actual test token
const ADMIN_TOKEN = 'your-admin-jwt-token'; // Replace with actual admin token

// Test data
const TEST_USER_ID = '60d5ecb54b24c0001f5f3e8c';
const TEST_PLAN_ID = '60d5ecb54b24c0001f5f3e8a';
const TEST_SUBSCRIPTION_ID = 'sub_test_1234567890';

// Headers
const headers = {
  'Authorization': `Bearer ${TEST_TOKEN}`,
  'Content-Type': 'application/json'
};

const adminHeaders = {
  'Authorization': `Bearer ${ADMIN_TOKEN}`,
  'Content-Type': 'application/json'
};

console.log('🧪 Testing Stripe and Flutterwave Webhook Implementations\n');

// Test 1: Stripe Webhook - Payment Succeeded
async function testStripePaymentSucceeded() {
  console.log('1️⃣ Testing Stripe Payment Succeeded Webhook...');
  
  const stripePayload = {
    type: 'invoice.payment_succeeded',
    data: {
      object: {
        id: 'in_test_1234567890',
        subscription: TEST_SUBSCRIPTION_ID,
        status: 'paid',
        amount_paid: 499,
        currency: 'usd',
        next_payment_attempt: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days from now
        customer: 'cus_test_1234567890',
        metadata: {
          userId: TEST_USER_ID,
          planId: TEST_PLAN_ID
        }
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/stripe`, stripePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Stripe Payment Succeeded:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Stripe Payment Succeeded Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 2: Stripe Webhook - Payment Failed
async function testStripePaymentFailed() {
  console.log('\n2️⃣ Testing Stripe Payment Failed Webhook...');
  
  const stripePayload = {
    type: 'invoice.payment_failed',
    data: {
      object: {
        id: 'in_test_1234567891',
        subscription: TEST_SUBSCRIPTION_ID,
        status: 'open',
        amount_due: 499,
        currency: 'usd',
        next_payment_attempt: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days from now
        customer: 'cus_test_1234567890'
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/stripe`, stripePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Stripe Payment Failed:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Stripe Payment Failed Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 3: Stripe Webhook - Subscription Created
async function testStripeSubscriptionCreated() {
  console.log('\n3️⃣ Testing Stripe Subscription Created Webhook...');
  
  const stripePayload = {
    type: 'customer.subscription.created',
    data: {
      object: {
        id: TEST_SUBSCRIPTION_ID,
        status: 'active',
        customer: 'cus_test_1234567890',
        metadata: {
          userId: TEST_USER_ID,
          planId: TEST_PLAN_ID,
          planType: 'basic'
        },
        items: {
          data: [{
            price: {
              recurring: {
                interval: 'month'
              }
            }
          }]
        }
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/stripe`, stripePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Stripe Subscription Created:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Stripe Subscription Created Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 4: Stripe Webhook - Subscription Deleted
async function testStripeSubscriptionDeleted() {
  console.log('\n4️⃣ Testing Stripe Subscription Deleted Webhook...');
  
  const stripePayload = {
    type: 'customer.subscription.deleted',
    data: {
      object: {
        id: TEST_SUBSCRIPTION_ID,
        status: 'canceled',
        customer: 'cus_test_1234567890',
        cancel_at_period_end: true
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/stripe`, stripePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Stripe Subscription Deleted:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Stripe Subscription Deleted Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 5: Flutterwave Webhook - Charge Completed
async function testFlutterwaveChargeCompleted() {
  console.log('\n5️⃣ Testing Flutterwave Charge Completed Webhook...');
  
  const flutterwavePayload = {
    event: 'charge.completed',
    data: {
      id: '1234567890',
      tx_ref: TEST_SUBSCRIPTION_ID,
      status: 'successful',
      amount: 499,
      currency: 'USD',
      customer: {
        email: '<EMAIL>',
        name: 'Test User'
      },
      meta: {
        userId: TEST_USER_ID,
        planId: TEST_PLAN_ID
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/flutterwave`, flutterwavePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Flutterwave-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Flutterwave Charge Completed:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Flutterwave Charge Completed Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 6: Flutterwave Webhook - Subscription Activated
async function testFlutterwaveSubscriptionActivated() {
  console.log('\n6️⃣ Testing Flutterwave Subscription Activated Webhook...');
  
  const flutterwavePayload = {
    event: 'subscription.activated',
    data: {
      subscription_id: TEST_SUBSCRIPTION_ID,
      status: 'active',
      customer: {
        email: '<EMAIL>',
        name: 'Test User'
      },
      meta: {
        userId: TEST_USER_ID,
        planId: TEST_PLAN_ID
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/flutterwave`, flutterwavePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Flutterwave-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Flutterwave Subscription Activated:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Flutterwave Subscription Activated Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 7: Flutterwave Webhook - Subscription Cancelled
async function testFlutterwaveSubscriptionCancelled() {
  console.log('\n7️⃣ Testing Flutterwave Subscription Cancelled Webhook...');
  
  const flutterwavePayload = {
    event: 'subscription.cancelled',
    data: {
      subscription_id: TEST_SUBSCRIPTION_ID,
      status: 'cancelled',
      customer: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/webhook/flutterwave`, flutterwavePayload, {
      headers: {
        'Content-Type': 'application/json',
        'Flutterwave-Signature': 'test-signature'
      }
    });
    
    console.log('✅ Flutterwave Subscription Cancelled:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Flutterwave Subscription Cancelled Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 8: Test Webhook (Development)
async function testTestWebhook() {
  console.log('\n8️⃣ Testing Test Webhook (Development)...');
  
  const testPayload = {
    subscriptionId: TEST_SUBSCRIPTION_ID,
    status: 'active',
    userId: TEST_USER_ID
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/test-webhook`, testPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Test Webhook:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Test Webhook Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 9: Create Subscription with Stripe
async function testCreateStripeSubscription() {
  console.log('\n9️⃣ Testing Create Subscription with Stripe...');
  
  const subscriptionData = {
    planId: TEST_PLAN_ID,
    billingCycle: 'monthly',
    paymentProvider: 'stripe'
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/subscribe`, subscriptionData, {
      headers
    });
    
    console.log('✅ Create Stripe Subscription:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Create Stripe Subscription Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 10: Create Subscription with Flutterwave
async function testCreateFlutterwaveSubscription() {
  console.log('\n🔟 Testing Create Subscription with Flutterwave...');
  
  const subscriptionData = {
    planId: TEST_PLAN_ID,
    billingCycle: 'monthly',
    paymentProvider: 'flutterwave'
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/subscriptions/subscribe`, subscriptionData, {
      headers
    });
    
    console.log('✅ Create Flutterwave Subscription:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Create Flutterwave Subscription Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 11: Get Available Plans
async function testGetAvailablePlans() {
  console.log('\n1️⃣1️⃣ Testing Get Available Plans...');
  
  try {
    const response = await axios.get(`${BASE_URL}/subscriptions/plans`);
    
    console.log('✅ Get Available Plans:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get Available Plans Error:', error.response?.data || error.message);
    return false;
  }
}

// Test 12: Get My Subscription
async function testGetMySubscription() {
  console.log('\n1️⃣2️⃣ Testing Get My Subscription...');
  
  try {
    const response = await axios.get(`${BASE_URL}/subscriptions/my-subscription`, {
      headers
    });
    
    console.log('✅ Get My Subscription:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get My Subscription Error:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    testStripePaymentSucceeded,
    testStripePaymentFailed,
    testStripeSubscriptionCreated,
    testStripeSubscriptionDeleted,
    testFlutterwaveChargeCompleted,
    testFlutterwaveSubscriptionActivated,
    testFlutterwaveSubscriptionCancelled,
    testTestWebhook,
    testCreateStripeSubscription,
    testCreateFlutterwaveSubscription,
    testGetAvailablePlans,
    testGetMySubscription
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
    } catch (error) {
      console.log('❌ Test failed with error:', error.message);
      results.push(false);
    }
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  const passed = results.filter(r => r).length;
  const total = results.length;
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! Webhook implementations are working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
}

// Environment check
console.log('🔧 Environment Check:');
console.log('====================');
console.log(`Base URL: ${BASE_URL}`);
console.log(`Test Token: ${TEST_TOKEN ? '✅ Set' : '❌ Not set'}`);
console.log(`Admin Token: ${ADMIN_TOKEN ? '✅ Set' : '❌ Not set'}`);
console.log('');

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/subscriptions/plans`);
    console.log('✅ Server is running and accessible');
    return true;
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('Please start the server before running tests');
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runAllTests();
  }
}

// Run the tests
main().catch(console.error);