const axios = require('axios');
const crypto = require('crypto');

const BASE_URL = 'http://localhost:3000';
const USER_TOKEN = process.env.TEST_USER_TOKEN || 'YOUR_USER_JWT_TOKEN';
const FLUTTERWAVE_SECRET_HASH = process.env.FLUTTERWAVE_SECRET_HASH || 'test_hash';

async function testFlutterwaveSubscription() {
  try {
    console.log('🧪 Testing Flutterwave Subscription System...\n');

    // 1. Get available plans
    console.log('1. Getting available plans...');
    const plansResponse = await axios.get(`${BASE_URL}/api/subscriptions/plans`);
    console.log('✅ Plans retrieved:', plansResponse.data.data.length, 'plans found');
    console.log('Available plans:', plansResponse.data.data.map(p => p.name).join(', '), '\n');

    // 2. Subscribe to Basic plan with Flutterwave
    console.log('2. Subscribing to Basic plan with Flutterwave...');
    const subscribeResponse = await axios.post(
      `${BASE_URL}/api/subscriptions/subscribe`,
      {
        planId: '688b300120fdcb1fabb5e585', // Free plan ID from your DB
        billingCycle: 'monthly',
        paymentProvider: 'flutterwave'
      },
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Subscription created:', subscribeResponse.data.data.subscriptionId, '\n');

    // 3. Simulate webhook for successful payment
    console.log('3. Simulating Flutterwave webhook for successful payment...');
    const webhookPayload = {
      event: 'charge.completed',
      data: {
        id: 'test_charge_' + Date.now(),
        tx_ref: 'test_transaction_' + Date.now(),
        flw_ref: 'FLW' + Date.now(),
        amount: 1000,
        currency: 'USD',
        customer: {
          email: '<EMAIL>'
        },
        meta: {
          userId: '681d8a68d3bffe4846ffca7f',
          planId: '688b300120fdcb1fabb5e585',
          planType: 'free'
        },
        status: 'successful',
        payment_type: 'card',
        created_at: new Date().toISOString()
      }
    };

    // Generate signature for webhook
    const signature = crypto
      .createHmac('sha512', FLUTTERWAVE_SECRET_HASH)
      .update(JSON.stringify(webhookPayload))
      .digest('hex');

    const webhookResponse = await axios.post(
      `${BASE_URL}/api/subscriptions/webhook/flutterwave`,
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'flutterwave-signature': signature
        }
      }
    );
    console.log('✅ Webhook processed successfully\n');

    // 4. Check subscription status
    console.log('4. Checking subscription status...');
    const subscriptionResponse = await axios.get(
      `${BASE_URL}/api/subscriptions/my-subscription`,
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`
        }
      }
    );
    console.log('✅ Current subscription:', subscriptionResponse.data.data.planName);
    console.log('Payment status:', subscriptionResponse.data.data.paymentStatus);
    console.log('Plan data structure:', {
      planId: subscriptionResponse.data.data.planId,
      hasPlanData: !!subscriptionResponse.data.data.planData
    }, '\n');

    // 5. Test subscription upgrade
    console.log('5. Testing subscription upgrade...');
    const upgradeResponse = await axios.put(
      `${BASE_URL}/api/subscriptions/upgrade`,
      {
        newPlanId: '688b300120fdcb1fabb5e585', // Same plan for testing
        billingCycle: 'monthly',
        paymentProvider: 'flutterwave'
      },
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Upgrade successful:', upgradeResponse.data.data.planName, '\n');

    // 6. Test usage stats
    console.log('6. Testing usage statistics...');
    const usageResponse = await axios.get(
      `${BASE_URL}/api/subscriptions/usage`,
      {
        headers: {
          'Authorization': `Bearer ${USER_TOKEN}`
        }
      }
    );
    console.log('✅ Usage stats retrieved:', {
      individualProfiles: usageResponse.data.data.usageStats.individualProfiles,
      storageUsedMB: usageResponse.data.data.usageStats.storageUsedMB
    }, '\n');

    console.log('🎉 All Flutterwave subscription tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.message) {
      console.error('Error details:', error.response.data.message);
    }
  }
}

// Test webhook signature validation
function testWebhookSignature() {
  console.log('🔐 Testing webhook signature validation...\n');
  
  const testPayload = {
    event: 'charge.completed',
    data: {
      id: 'test_charge_123',
      status: 'successful'
    }
  };

  const signature = crypto
    .createHmac('sha512', FLUTTERWAVE_SECRET_HASH)
    .update(JSON.stringify(testPayload))
    .digest('hex');

  console.log('Test payload:', JSON.stringify(testPayload, null, 2));
  console.log('Generated signature:', signature);
  console.log('✅ Signature validation test completed\n');
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Flutterwave Subscription Tests\n');
  console.log('Base URL:', BASE_URL);
  console.log('User Token:', USER_TOKEN ? '✅ Set' : '❌ Not set');
  console.log('Secret Hash:', FLUTTERWAVE_SECRET_HASH ? '✅ Set' : '❌ Not set\n');

  testWebhookSignature();
  await testFlutterwaveSubscription();
}

// Check if running directly
if (require.main === module) {
  runAllTests();
}

module.exports = { testFlutterwaveSubscription, testWebhookSignature }; 