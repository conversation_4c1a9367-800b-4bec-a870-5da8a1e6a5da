{"name": "my-profile", "version": "1.0.0", "description": "MyProfile Backend - A secure and scalable authentication system", "main": "dist/server.js", "scripts": {"start": "NODE_NO_WARNINGS=1 node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc --skipLibCheck && cp -r src/templates dist/templates && cp -r public dist/public", "build:bun": "bun build ./src/server.ts --outdir ./dist --target node && cp -r src/templates dist/templates && cp -r public dist/public", "postinstall": "npm run build", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "jest --config jest.config.js", "test:watch": "jest --watch --config jest.config.js", "validate-logs": "ts-node src/tests/validate-logging.ts", "test-tracking": "ts-node src/tests/test-tracking.ts", "verify-tracking": "npm run validate-logs && npm run test-tracking", "test:sockets": "ts-node src/scripts/test-socket-interactions.ts", "test:sockets:watch": "nodemon --exec ts-node src/scripts/test-socket-interactions.ts", "migrate-tokens": "ts-node src/scripts/migrate-refresh-tokens.ts", "update-profile-country": "ts-node src/scripts/update-user-profile-country.ts", "update-secondary-ids": "ts-node src/scripts/update-profiles-with-secondary-id.ts", "update-secondary-ids:prod": "node dist/scripts/update-profiles-with-secondary-id.js", "update-country-info:prod": "ts-node src/scripts/update-profile-countries.ts", "cleanup-tokens": "ts-node src/scripts/run-token-cleanup.ts", "cleanup-tokens:prod": "node dist/scripts/run-token-cleanup.js", "cleanup-tokens:scalable": "ts-node src/scripts/run-scalable-token-cleanup.ts", "cleanup-tokens:scalable:prod": "node dist/scripts/run-scalable-token-cleanup.js", "cleanup-tokens:scalable:bun": "bun run src/scripts/run-scalable-token-cleanup.ts", "update-profile-usernames": "ts-node src/scripts/update-profile-usernames.ts", "update-profile-usernames:prod": "node dist/scripts/update-profile-usernames.js", "update-gradients": "ts-node src/scripts/update-profiles-with-gradients.ts", "fix-profile-completion-status": "ts-node src/scripts/fix-profile-completion-status.ts", "add-missing-profile-complete": "ts-node src/scripts/add-missing-profile-complete-field.ts", "fix-mypts-allocation": "ts-node src/scripts/fix-mypts-allocation.ts", "quick-fix-mypts": "ts-node src/scripts/quick-fix-mypts-allocation.ts", "update-gradients:prod": "node dist/scripts/update-profiles-with-gradients.js", "update-gradients:render": "NODE_ENV=production node dist/scripts/update-profiles-with-gradients.js", "seed:activity-rewards": "ts-node src/scripts/seed-activity-rewards.ts", "retroactive:platform-join": "ts-node src/scripts/retroactive-platform-join-rewards.ts", "test:mypts-rewards": "ts-node src/scripts/test-mypts-rewards.ts", "check:database-state": "ts-node src/scripts/check-database-state.ts", "verify:mypts-system": "npm run check:database-state && npm run seed:activity-rewards && npm run test:mypts-rewards", "script:init-verification": "ts-node scripts/initialize-verification.ts", "script:init-verification:dry-run": "ts-node scripts/initialize-verification.ts --dry-run", "script:init-verification:prod": "node dist/scripts/initialize-verification.js", "script:init-verification:prod:dry-run": "node dist/scripts/initialize-verification.js --dry-run", "optimize:vault-indexes": "node src/scripts/optimize-vault-indexes.js", "script:fix-template-fields": "ts-node src/scripts/fix-template-fields-isfavourite.ts", "seed:badges": "ts-node src/seeds/badge-seed.ts", "seed:badges:prod": "node dist/seeds/badge-seed.js", "test:activity-tracking": "ts-node src/scripts/test-activity-tracking.ts", "script:sync-personal-profiles": "ts-node src/scripts/sync-personal-profiles-with-template.ts", "rbac:seed": "ts-node src/scripts/rbac-seed.ts", "rbac:dev-seed": "NODE_ENV=development ts-node src/scripts/rbac-seed.ts", "rbac:upgrade-admins": "ts-node src/scripts/upgrade-admins-to-superadmin.ts", "generate:permissions": "ts-node src/scripts/permission-generator.ts", "permissions:add": "ts-node src/scripts/seed-additional-permissions.ts", "sync:legacy-admin": "ts-node src/scripts/sync-legacy-admin.ts", "script:fix-rbac-time-restrictions": "ts-node src/scripts/fix-rbac-time-restrictions.ts", "script:fix-admin-rbac": "ts-node src/scripts/fix-admin-rbac.ts", "script:fix-all-rbac": "ts-node src/scripts/fix-all-rbac-time-restrictions.ts", "update-profile-style": "ts-node src/scripts/update-profiles-to-new-default-style.ts", "update-profile-style:prod": "node dist/scripts/update-profiles-to-new-default-style.js", "generate-qr-codes": "ts-node src/scripts/generate-missing-qr-codes.ts", "generate-qr-codes:prod": "node dist/scripts/generate-missing-qr-codes.js", "update-profile-titles": "ts-node src/scripts/update-profile-titles-and-names.ts", "update-profile-titles:prod": "node dist/scripts/update-profile-titles-and-names.js", "regenerate-static-qr": "ts-node src/scripts/regenerate-static-qr-codes.ts", "regenerate-static-qr:prod": "node dist/scripts/regenerate-static-qr-codes.js", "regenerate-links": "ts-node src/scripts/regenerate-profile-links.ts", "regenerate-links:prod": "node dist/scripts/regenerate-profile-links.js"}, "keywords": ["authentication", "typescript", "node", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"@types/express-session": "^1.18.1", "@types/multer": "^1.4.12", "@types/node-cache": "^4.1.3", "@types/node-cron": "^3.0.11", "@types/stripe": "^8.0.416", "@uploadthing/shared": "^7.1.9", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "bowser": "^2.11.0", "canvas": "^3.0.0", "cloudinary": "^2.6.0", "colors": "^1.4.0", "compression": "^1.7.5", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "detect-browser": "^5.3.0", "docx": "^9.5.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.0", "fingerprintjs2": "^2.1.4", "firebase-admin": "^13.3.0", "geoip-lite": "^1.4.10", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "maxmind": "^4.3.24", "memcached": "^2.2.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^2.0.0", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "passport": "^0.6.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "pptxgenjs": "^4.0.0", "puppeteer-core": "^18.1.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "redis": "^5.5.6", "sharp": "^0.33.5", "socket.io": "^4.8.1", "socket.io-client": "^4.7.0", "spdy": "^4.0.2", "speakeasy": "^2.0.0", "stripe": "^18.3.0", "twilio": "^5.5.1", "ua-parser-js": "^2.0.3", "uploadthing": "^7.7.3", "uuid": "^11.0.5", "whatsapp-web.js": "^1.26.0", "winston": "^3.11.0", "ws": "^8.18.0", "zod": "^3.22.4"}, "optionalDependencies": {"nfc-pcsc": "^0.8.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/csv-stringify": "^1.4.3", "@types/express": "^4.17.21", "@types/express-validator": "^3.0.2", "@types/geoip-lite": "^1.4.4", "@types/handlebars": "^4.0.40", "@types/http-errors": "^2.0.4", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lru-cache": "^7.10.9", "@types/morgan": "^1.9.9", "@types/node": "^20.19.7", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-linkedin-oauth2": "^1.5.6", "@types/qrcode": "^1.5.5", "@types/qrcode-terminal": "^0.12.2", "@types/sharp": "^0.32.0", "@types/spdy": "^3.4.9", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/twilio": "^3.19.3", "@types/ua-parser-js": "^0.7.39", "@types/ws": "^8.5.14", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "jest-extended": "^5.0.3", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}